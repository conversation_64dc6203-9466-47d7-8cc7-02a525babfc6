# Spring
spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        # 命名空间
        namespace: 12fbf7c4-d6a6-41d1-8948-49c694825958
        group: ZHOU_YONGSHNEG
      config:
#        # 配置中心地址
#        server-addr: 111.22.163.233:28848
#        # 命名空间
#        namespace: 77062278-f6c1-46b9-8112-072f7fe976b5
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        # 命名空间
        namespace: 12fbf7c4-d6a6-41d1-8948-49c694825958
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-biaopu-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
      username: nacos
      password: <PERSON><PERSON><PERSON><PERSON>@8888
