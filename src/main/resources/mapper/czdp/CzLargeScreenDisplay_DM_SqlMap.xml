<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Wed Feb 26 18:51:09 CST 2025-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.czdp.mapper.CzLargeScreenDisplayMapper">

    <!--财政大屏近十年数据-->
    <select id="getNearlyADecadeData" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        SELECT fnf xAxis, sum(fljje) yAxis, '完成数' legend, 'bar' type
        from (
            SELECT fnf, fljje,fkmmc, ROW_NUMBER() OVER ( partition by fkmmc,fnf ORDER BY fyf DESC) row_num
            from tb_dm_czsz_czszyb_gen
                WHERE 1=1
                    <if test="fkmmc != null and fkmmc != ''">
                        AND fkmmc = #{fkmmc}
                    </if>
                    <if test="fkmmc_in != null and fkmmc_in != ''">
                        AND fkmmc in ('一般公共预算收入合计','国有资本经营预算收入合计','政府性基金预算收入合计')
                    </if>
                    <if test="ffkmbm != null and ffkmbm != ''">
                        AND ffkmbm = #{ffkmbm}
                    </if>
                    <if test="fssdq != null and fssdq != ''">
                        AND fssdq = #{fssdq}
                    </if>
                    <if test="yearStart != null and yearStart != ''">
                        AND fnf &gt; #{yearStart}
                    </if>
                    <if test="yearEnd != null and yearEnd != ''">
                        AND fnf &lt;= #{yearEnd}
                    </if>
                    <if test="fmonth != null and fmonth != ''">
                        AND fyf = #{fmonth}
                    </if>
            ) tmp
        WHERE row_num = 1
        group by fnf
        union all
        SELECT fnf xAxis, sum(fncysje) yAxis, '目标值' legend, 'bar' type
        from (
            SELECT fnf, fncysje,fkmmc, ROW_NUMBER() OVER ( partition by fkmmc,fnf ORDER BY fnf DESC) row_num
            from tb_dw_czsz_ysszmb_gen
                WHERE 1=1
                    <if test="fkmmc != null and fkmmc != ''">
                        AND fkmmc = #{fkmmc}
                    </if>
                    <if test="fkmmc_in != null and fkmmc_in != ''">
                        AND fkmmc in ('一般公共预算收入合计','国有资本经营预算收入合计','政府性基金预算收入合计')
                    </if>
                    <if test="ffkmbm != null and ffkmbm != ''">
                        AND ffkmbm = #{ffkmbm}
                    </if>
                    <if test="fssdq != null and fssdq != ''">
                        AND fssdq = #{fssdq}
                    </if>
                    <if test="yearStart != null and yearStart != ''">
                        AND fnf &gt; #{yearStart}
                    </if>
                    <if test="yearEnd != null and yearEnd != ''">
                        AND fnf &lt;= #{yearEnd}
                    </if>
            ) tmp
        WHERE row_num = 1
        group by fnf

        ORDER BY legend,xAxis
    </select>

    <!-- 财政当年月份财政情况 -->
    <select id="getMonthData" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT SUM(fljje) fwcs, SUM(fncysje) fmbz,
               CASE WHEN SUM(fncysje) = 0 THEN 0 ELSE round(SUM(fljje)/SUM(fncysje)*100, 2) END fjdz
        from (
            select cz.fljje, cz.fzjbl, ys.fncysje,
                ROW_NUMBER() OVER ( partition by cz.fkmmc,cz.fnf ORDER BY cz.fyf DESC) row_num
            from tb_czsz_czszyb_gen cz, tb_dw_czsz_ysszmb_gen ys
            WHERE 1=1
            <if test="fkmmc != null and fkmmc != ''">
                AND cz.fkmmc = #{fkmmc}
            </if>
            <if test="fkmmc_in != null and fkmmc_in != ''">
                AND cz.fkmmc in ('一般公共预算收入合计','国有资本经营预算收入合计','政府性基金预算收入合计')
            </if>
            <if test="fkmbm != null and fkmbm != ''">
                AND cz.fkmbm = #{fkmbm}
            </if>
            <if test="fssdq != null and fssdq != ''">
                AND cz.fssdq = #{fssdq}
            </if>
            <if test="fmonth != null and fmonth != ''">
                AND cz.fyf = #{fmonth}
            </if>
            <if test="fyear != null and fyear != ''">
                AND cz.fnf = #{fyear}
            </if>
            <if test="fkmmc != null and fkmmc != ''">
                AND ys.fkmmc = #{fkmmc}
            </if>
            <if test="fkmmc_in != null and fkmmc_in != ''">
                AND ys.fkmmc in ('一般公共预算收入合计','国有资本经营预算收入合计','政府性基金预算收入合计')
            </if>
            <if test="fyear != null and fyear != ''">
                AND ys.fnf = #{fyear}
            </if>
        )  tmp
        WHERE row_num = 1
    </select>

    <select id="getCzTotalIncomeBarData" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        select
        fnf xAxis, fjnlj yAxis, '财政总收入' legend, 'bar' type
        from TB_DM_ZHZS_SSDP_BNGMJJZYTJZB_GEN
        where fzbmc like '%1.财政总收入'
        <if test="yearStart != null and yearStart != ''">
            AND fnf &gt; #{yearStart}
        </if>
        <if test="yearEnd != null and yearEnd != ''">
            AND fnf &lt;= #{yearEnd}
        </if>
        and fjd = CEIL(#{fmonth} / 3)
    </select>
    <!-- 财政总收入 占比 -->
    <select id="getCzTotalIncomeData" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
            fjnlj fwcs, ftb fzjf,'' fmbz,'' fjdz
        from TB_DM_ZHZS_SSDP_BNGMJJZYTJZB_GEN
        where fzbmc like '%1.财政总收入'
        <if test="fmonth != null and fmonth != ''">
            AND fjd = CEIL(#{fmonth} / 3)
        </if>
        <if test="fyear != null and fyear != ''">
            AND fnf = #{fyear}
        </if>
    </select>

    <!--全省一般公共预算收支情况-->
    <select id="getProvinceData" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        select xAxis,yAxis,legend,type from(
            select fxm xAxis,fsrljwcs yAxis, '收入' legend, 'Bar' type from (
            select fxm,fsrljwcs,ROW_NUMBER() OVER ( partition by fxm ORDER BY fyf DESC) rum
            from TB_DM_ZHZS_SSDP_QSYBGGYSSZ_GEN
            where fxm not in('州市小计','省本级','全省合计')
            <if test="fyear != null and fyear != ''">
                AND fnf = #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">
                AND fyf = #{fmonth}
            </if>
            )tmp where rum = 1
            union all
            select fxm xAxis,fzcljwcs yAxis, '支出' legend, 'Bar' type from (
            select fxm,fzcljwcs,ROW_NUMBER() OVER ( partition by fxm ORDER BY fyf DESC) rum
            from TB_DM_ZHZS_SSDP_QSYBGGYSSZ_GEN
            where fxm not in('州市小计','省本级','全省合计')
            <if test="fyear != null and fyear != ''">
                AND fnf = #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">
                AND fyf = #{fmonth}
            </if>
            )tmp where rum = 1)
        where 1=1
        <if test="legend != null and legend != ''">
            AND legend = #{legend}
        </if>
        order by legend,TO_NUMBER(yAxis) desc
    </select>

    <!-- 地方全省排名情况 -->
    <select id="getProvinceRank" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select fxm,fsrljwcs,fpm,fzfpm,legend from(
             select fxm,fsrljwcs, DENSE_RANK() OVER ( ORDER BY TO_NUMBER(fsrljwcs)desc) fpm,fsrzfpm fzfpm, '收入' legend
             from (select fxm, TO_NUMBER(fsrljwcs) fsrljwcs,fsrzfpm,
                          ROW_NUMBER() OVER ( partition by fxm ORDER BY fyf DESC) rum
                   from TB_DM_ZHZS_SSDP_QSYBGGYSSZ_GEN
                   where fxm not in ('州市小计', '省本级', '全省合计')
                    <if test="fyear != null and fyear != ''">
                        AND fnf = #{fyear}
                    </if>
                    <if test="fmonth != null and fmonth != ''">
                        AND fyf = #{fmonth}
                    </if>
                  ) tmp
             where rum = 1
             union all
             select fxm,fzcljwcs,DENSE_RANK() OVER ( ORDER BY TO_NUMBER(fzcljwcs)desc) fpm,fzczfpm fzfpm, '支出' legend
             from (select fxm,TO_NUMBER(fzcljwcs) fzcljwcs,fzczfpm,
                          ROW_NUMBER() OVER ( partition by fxm ORDER BY fyf DESC) rum
                   from TB_DM_ZHZS_SSDP_QSYBGGYSSZ_GEN
                   where fxm not in ('州市小计', '省本级', '全省合计')
                    <if test="fyear != null and fyear != ''">
                        AND fnf = #{fyear}
                    </if>
                    <if test="fmonth != null and fmonth != ''">
                        AND fyf = #{fmonth}
                    </if>
                  ) tmp
             where rum = 1) t
        where 1=1
        <if test="fxm != null and fxm != ''">
            AND fxm = #{fxm}
        </if>
        <if test="legend != null and legend != ''">
            AND legend = #{legend}
        </if>
    </select>

    <!-- 财政支出分科目排名情况 -->
    <select id="getCategorizeSpendRank" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
            fkmmc,
            fljje,
            fzjbl
        from tb_dm_czsz_czszyb_gen
        where fkmmc not in ('一般公共预算支出合计','国有资本经营预算支出合计','政府性基金预算支出合计')
          and fxmlx = '支出'
          and fljje != 0
        <if test="fssdq != null and fssdq != ''">
            AND fssdq = #{fssdq}
        </if>
        <if test="fmonth != null and fmonth != ''">
            AND fyf = #{fmonth}
        </if>
        <if test="fyear != null and fyear != ''">
            AND fnf = #{fyear}
        </if>

    </select>

    <!-- 地区支出排名情况 -->
    <select id="getRegionSpendRank" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
            fssdq,
            sum(fljje) fljje
        from tb_dm_czsz_czszyb_gen
        where fkmmc not in ('一般公共预算支出合计','国有资本经营预算支出合计','政府性基金预算支出合计')
          and fxmlx = '支出'
        <if test="fssdq == null or fssdq == ''">
            AND fssdq != '全市'
        </if>
        <if test="fssdq != null and fssdq != ''">
            AND fssdq = #{fssdq}
        </if>
        <if test="fmonth != null and fmonth != ''">
            AND fyf = #{fmonth}
        </if>
        <if test="fyear != null and fyear != ''">
            AND fnf = #{fyear}
        </if>
        group by fssdq
        order by fljje desc
    </select>

    <!-- 年度税收情况 -按区域 -->
    <select id="getAnnualTaxesDataByRegion" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.pie.PieChartsSeriesData">
        select
            fssdq name,
            sum(fljje) value
        from tb_dm_czsz_czszyb_gen
        where 1=1
          and fssdq != '全市'
          and ffkmbm = #{fkmbm}
          and fyf = (select fyf from tb_dm_czsz_czszyb_gen where fnf = #{fyear} order by fyf desc limit 1)
            <if test="fyear != null and fyear != ''">
                AND fnf = #{fyear}
            </if>
        group by fssdq
        order by fljje desc;
    </select>
    <!-- 年度税收情况 -按分类 -->
    <select id="getAnnualTaxesDataByType" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.pie.PieChartsSeriesData">
        select
            fkmmc name,
            sum(fljje) value
        from tb_dm_czsz_czszyb_gen
        where 1=1
          and fssdq != '全市'
          and ffkmbm = #{fkmbm}
          and fyf = (select fyf from tb_dm_czsz_czszyb_gen where fnf = #{fyear} order by fyf desc limit 1)
        <if test="fyear != null and fyear != ''">
            AND fnf = #{fyear}
        </if>
        group by fkmmc
        order by fljje desc;
    </select>




    <!-- 趋势数据 按年度 数据情况 -->
    <select id="getTrendDataByYear" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        SELECT fnf xAxis, fljje yAxis, '完成数' legend, 'bar' type
        from (
            SELECT fnf, fljje,fkmmc, ROW_NUMBER() OVER ( partition by fkmmc,fnf ORDER BY fyf DESC) row_num
            FROM TB_DM_ZHZS_CZSZYB_GEN
            WHERE 1=1
            <if test="fkmmc != null and fkmmc != ''">
                AND fkmmc IN
                 <foreach collection="fkmmcList" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="fkmlb != null and fkmlb != ''">
                AND fkmlb = #{fkmlb}
            </if>
            <if test="fssdq != null and fssdq != ''">
                AND fssdq = #{fssdq}
            </if>
            <if test="fyear != null and fyear != ''">
                AND fnf &lt;= #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">
                AND fyf = #{fmonth}
            </if>
            GROUP BY FNF
            ORDER BY FNF DESC
            limit #{limit}
        ) tmp
        WHERE row_num = 1

        union all
        SELECT fnf xAxis, fncysje yAxis, '目标值' legend, 'bar' type
        from (
            SELECT fnf, fncysje,fkmmc, ROW_NUMBER() OVER ( partition by fkmmc,fnf ORDER BY fnf DESC) row_num
            from TB_DW_CZSZ_YSSZMB_GEN
            WHERE 1=1
            <if test="fkmmc != null and fkmmc != ''">
                AND fkmmc IN
                 <foreach collection="fkmmcList" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="fssdq != null and fssdq != ''">
                AND fssdq = #{fssdq}
            </if>
            <if test="fyear != null and fyear != ''">
                AND fnf &lt;= #{fyear}
            </if>
            GROUP BY FNF
            ORDER BY FNF DESC
            limit #{limit}
        ) tmp
        WHERE row_num = 1

        ORDER BY legend,xAxis

    </select>

    <!-- 趋势数据 按月份 数据情况 -->
    <select id="getTrendDataByMonth" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        SELECT FYF xAxis, fje yAxis, #{fyear} legend, 'bar' type
        from (
            SELECT FYF, fje,fkmmc, ROW_NUMBER() OVER ( partition by fkmmc,FYF ORDER BY fyf DESC) row_num
            FROM TB_DM_ZHZS_CZSZYB_GEN
            WHERE 1=1
            <if test="fkmmc != null and fkmmc != ''">
                AND fkmmc  IN
                 <foreach collection="fkmmcList" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="fkmlb != null and fkmlb != ''">
                AND fkmlb = #{fkmlb}
            </if>
            <if test="fssdq != null and fssdq != ''">
                AND fssdq = #{fssdq}
            </if>
            <if test="fyear != null and fyear != ''">
                AND fnf = #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">
                AND fyf &lt;= #{fmonth}
            </if>
            GROUP BY FYF
            ORDER BY FYF DESC
        ) tmp
        WHERE row_num = 1
        ORDER BY legend,xAxis

    </select>

    <!-- 获取科目的 各种(占比,增幅,等..)比例 -->
    <!-- , CASE WHEN fncysje = 0 THEN 0 ELSE round(fljje/fncysje*100, 2) END fjdz -->
    <select id="getRatioOfEachSubjectData" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT fljje fwcs, ifnull(fncysje,0) fmbz
        from (
            select cz.fljje, cz.fzjbl,
            (select fncysje from tb_dw_czsz_ysszmb_gen where 1=1
                <if test="fkmmc != null and fkmmc != ''">
                    AND  fkmmc IN
                     <foreach collection="fkmmcList" item="item" separator="," close=")" open="(">
                        #{item}
                    </foreach>
                </if>
                <if test="fyear != null and fyear != ''">
                    AND  fnf = #{fyear}
                </if>
                <if test="fssdq != null and fssdq != ''">
                    AND fssdq = #{fssdq}
                </if>
            ) fncysje,
            ROW_NUMBER() OVER ( partition by cz.fkmmc,cz.fnf ORDER BY cz.fyf DESC) row_num
            from TB_DM_ZHZS_CZSZYB_GEN cz
            WHERE 1=1
            <if test="fkmmc != null and fkmmc != ''">
                AND cz.fkmmc IN
                 <foreach collection="fkmmcList" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="fkmbm != null and fkmbm != ''">
                AND cz.fkmbm = #{fkmbm}
            </if>
            <if test="fkmlb != null and fkmlb != ''">
                AND cz.fkmlb = #{fkmlb}
            </if>
            <if test="fssdq != null and fssdq != ''">
                AND cz.fssdq = #{fssdq}
            </if>
            <if test="fmonth != null and fmonth != ''">
                AND cz.fyf = #{fmonth}
            </if>
            <if test="fyear != null and fyear != ''">
                AND cz.fnf = #{fyear}
            </if>

        )  tmp
        WHERE row_num = 1
    </select>

    <!-- 地区支出排名情况 -->
    <select id="getCzybRegionData" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
        fssdq,
        fljje
        from TB_DM_ZHZS_CZSZYB_GEN
        where 1=1
        and fssdq IN ('全市', '市本级', '城关区', '七里河区', '安宁区', '西固区', '红古区', '榆中县', '永登县', '皋兰县')
        <if test="fkmmc == null or fkmmc == ''">
            AND fkmmc like CONCAT(#{fkmlb},'%','合计')
        </if>
        <if test="fkmmc != null and fkmmc != ''">
            AND fkmmc = #{fkmmc}
        </if>
        <if test="fkmlb != null and fkmlb != ''">
            AND fkmlb = #{fkmlb}
        </if>
        <if test="fxmlx != null and fxmlx != ''">
            AND fxmlx = #{fxmlx}
        </if>
        <if test="fmonth == null or fmonth == ''">
            AND fyf = (select fyf from TB_DM_ZHZS_CZSZYB_GEN where fnf = #{fyear} order by fyf desc limit 1)
        </if>
        <if test="fmonth != null and fmonth != ''">
            AND fyf = #{fmonth}
        </if>
        <if test="fyear != null and fyear != ''">
            AND fnf = #{fyear}
        </if>
        order by fljje desc
    </select>


    <!-- 财政支出分科目排名情况 -->
    <select id="getCzybSubjectData" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
        fkmmc,
        fljje
        from TB_DM_ZHZS_CZSZYB_GEN
        where 1=1
        and fljje != 0
        <if test="fkmbm == null or fkmbm == ''">
            AND length(fkmbm)=3
        </if>
        <if test="fkmbm != null and fkmbm != ''">
            AND fkmbm like CONCAT(#{fkmbm},'%')
            <if test="fkmbm == 101">
                AND (length(fkmbm)=(length(#{fkmbm})+2) OR fkmbm in ('1010101','1010201'))
            </if>
            <if test="fkmbm != 101">
                AND length(fkmbm)=(length(#{fkmbm})+2)
            </if>
        </if>
        <if test="fssdq != null and fssdq != ''">
            AND fssdq = #{fssdq}
        </if>
        <if test="fkmlb != null and fkmlb != ''">
            AND fkmlb = #{fkmlb}
        </if>
        <if test="fxmlx != null and fxmlx != ''">
            AND fxmlx = #{fxmlx}
        </if>
        <if test="fmonth == null or fmonth == ''">
            AND fyf = (select fyf from TB_DM_ZHZS_CZSZYB_GEN where fnf = #{fyear} order by fyf desc limit 1)
        </if>
        <if test="fmonth != null and fmonth != ''">
            AND fyf = #{fmonth}
        </if>
        <if test="fyear != null and fyear != ''">
            AND fnf = #{fyear}
        </if>
        order by fljje desc
    </select>

    <!-- 趋势数据 全省预算 按年度 数据情况 -->
    <select id="getQsysTrendDataByYear" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        SELECT fnf xAxis, fwcs yAxis, CONCAT('全省一般公共预算',#{fxmlx}) legend, 'bar' type
        from (
            select fnf fnf,fxm,
                <if test="fxmlx == &quot;收入&quot;">
                    fsrljwcs
                </if>
                <if test="fxmlx == &quot;支出&quot;">
                    fzcljwcs
                </if>
                   fwcs
            from TB_DM_ZHZS_SSDP_QSYBGGYSSZ_GEN
            where 1=1
            <if test="fxm != null and fxm != ''">
                AND fxm = #{fxm}
            </if>
            <if test="fyear != null and fyear != ''">
                AND fnf &lt;= #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">
                AND fyf = #{fmonth}
            </if>
            GROUP BY fnf
            ORDER BY fnf DESC
            limit #{limit}
        ) tmp
        ORDER BY legend,xAxis

    </select>

    <!-- 趋势数据 全省预算 按月度 数据情况 -->
    <select id="getQsysTrendDataByMonth" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        SELECT fyf xAxis, fwcs yAxis, #{fyear} legend, 'bar' type
        from (
            select TO_NUMBER(fyf) fyf,fxm,
                <if test="fxmlx == &quot;收入&quot;">
                    fsrljwcs
                </if>
                <if test="fxmlx == &quot;支出&quot;">
                    fzcljwcs
                </if>
                   fwcs
            from TB_DM_ZHZS_SSDP_QSYBGGYSSZ_GEN
            where 1=1
            <if test="fxm != null and fxm != ''">
                AND fxm = #{fxm}
            </if>
            <if test="fyear != null and fyear != ''">
                AND fnf = #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">
                AND fyf &lt;= #{fmonth}
            </if>
            GROUP BY fyf
            ORDER BY fyf DESC
        ) tmp
        ORDER BY legend,xAxis

    </select>

</mapper>