<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.yqmjss.mapper.YqmjssMapper">
    <select id="getYqmjss" parameterType="map" resultType="map">
		WITH
		jnmd AS (
			SELECT FQYMC, FSYZDMJPFM, FSSYQ, FYQXZ FROM TB_DW_CYJS_YQQYXX_GEN WHERE FNF = #{fyear}
		)
		, qnmd AS (
			SELECT FQYMC, FSYZDMJPFM, FSSYQ, FYQXZ FROM TB_DW_CYJS_YQQYXX_GEN WHERE FNF = #{fyear} - 1
		)
		, yqinfo AS (
			SELECT jn.FSSYQ, jn.<PERSON>YQXZ, jn.FSL FSL_JN, jn.FMJ FMJ_JN, qn.FSL FSL_QN, qn.FMJ FMJ_QN FROM
			(SELECT FSSYQ, COUNT(FQYMC) FSL, SUM(FSYZDMJPFM) FMJ, MAX(FYQXZ) FYQXZ FROM jnmd GROUP BY FSSYQ) jn
			LEFT JOIN
			(SELECT FSSYQ, COUNT(FQYMC) FSL, SUM(FSYZDMJPFM) FMJ, MAX(FYQXZ) FYQXZ FROM qnmd GROUP BY FSSYQ) qn
			ON jn.FSSYQ = qn.FSSYQ
		)
		, ss AS (
			SELECT FSSYQ, SUM(FSHJ_JN) FSHJ_JN, SUM(FSHJ_QN) FSHJ_QN FROM (
			SELECT jnmd.FSSYQ, SUM(FSHJ) FSHJ_JN, NULL FSHJ_QN
			FROM jnmd LEFT JOIN TB_DW_SRFX_SRFX_MAIN srfx ON jnmd.FQYMC = srfx.FNSRMC
			WHERE FZSXM LIKE '%税'
			AND FRKRQ BETWEEN TO_DATE(#{start_time}, 'YYYY-MM') AND LAST_DAY(TO_DATE(#{end_time}, 'YYYY-MM'))
			UNION ALL
			SELECT qnmd.FSSYQ, NULL FSHJ_JN, SUM(FSHJ) FSHJ_QN
			FROM qnmd LEFT JOIN TB_DW_SRFX_SRFX_MAIN srfx ON qnmd.FQYMC = srfx.FNSRMC
			WHERE FZSXM LIKE '%税'
			AND FRKRQ BETWEEN DATEADD(YEAR, -1, TO_DATE(#{start_time}, 'YYYY-MM')) AND DATEADD(YEAR, -1, LAST_DAY(TO_DATE(#{end_time}, 'YYYY-MM')))
			) GROUP BY FSSYQ
		)
		SELECT *
			, CASE WHEN FSHJ_QN=0 THEN NULL ELSE ROUND((FSHJ_JN-FSHJ_QN)/FSHJ_QN*100, 2) END AS FZJF_SHJ
			, CASE WHEN FMJSS_QN=0 THEN NULL ELSE ROUND((FMJSS_JN-FMJSS_QN)/FMJSS_QN*100, 2) END AS FZJF_MJSS
		FROM (
		SELECT FSSYQ, FYQXZ, FSL_JN, ROUND(FMJ_JN, 2) FMJ_JN, ROUND(FSHJ_JN/10000, 2) FSHJ_JN, FSL_QN, ROUND(FMJ_QN, 2) FMJ_QN, ROUND(FSHJ_QN/10000, 2) FSHJ_QN
			, CASE WHEN FMJ_JN=0 THEN NULL ELSE ROUND((FSHJ_JN/(FMJ_JN)/10000), 2) END FMJSS_JN
			, CASE WHEN FMJ_QN=0 THEN NULL ELSE ROUND((FSHJ_QN/(FMJ_QN)/10000), 2) END FMJSS_QN
		FROM (
		SELECT yqinfo.FSSYQ, yqinfo.FYQXZ, yqinfo.FSL_JN, (yqinfo.FMJ_JN/666) FMJ_JN, yqinfo.FSL_QN, (yqinfo.FMJ_QN/666) FMJ_QN, ss.FSHJ_JN, ss.FSHJ_QN
		FROM yqinfo LEFT JOIN ss ON yqinfo.FSSYQ = ss.FSSYQ
		))
    </select>

    <select id="getYqmjss_mx" resultType="java.util.Map">
        SELECT ifnull(fnsrmc,'合计')纳税人名称,
       decode(fnsrmc , null,'',max(FSKGK))FSKGK,
		decode(fnsrmc , null,'',max(FNSRSBH))	纳税人识别号,
       ROUND(SUM(fshj) / 10000, 2) 本期合计,
       ROUND(sum(fzzs) / 10000, 2) 增值税,
       ROUND(SUM(case when fzsxm='%营业税%' then fhj else 0 end) / 10000, 2) 营业税,
       ROUND(SUM(fqysds) / 10000, 2) 企业所得税,
       ROUND(SUM(fgrsds) / 10000, 2) 个人所得税,
       ROUND(SUM(ftdzzs) / 10000, 2) 土地增值税,
       ROUND(SUM(ffcs) / 10000, 2) 房产税,
       ROUND(SUM(fcswhjss) / 10000, 2) 城建税,
       ROUND(SUM(fccs) / 10000, 2) 车船税,
       ROUND(SUM(fclgzs) / 10000, 2) 车辆购置税,
       ROUND(SUM(fgdzys) / 10000, 2) 耕地占用税,
       ROUND(SUM(fcztdsys) / 10000, 2) 城镇土地使用税,
       ROUND(SUM(fyhs) / 10000, 2) 印花税,
       ROUND(SUM(fhjbhs) / 10000, 2) 环境保护税,
       ROUND(SUM(fqs) / 10000, 2) 契税,
       ROUND(SUM(fxfs) / 10000, 2) 消费税,
       ROUND(SUM(fzys) / 10000, 2) 资源税,
       ROUND(SUM(case when fzsxm like '%残疾人就业保障金%' then fhj else 0 end) / 10000, 2) 残疾人就业保障金,
       ROUND(SUM(fdfjyfj) / 10000, 2) 地方教育附加,
       ROUND(SUM(fjyffj) / 10000, 2) 教育费附加,
       ROUND(SUM(case when fzsxm like '%其他收入%' then fhj else 0 end) / 10000, 2) 其他收入,
       ROUND(SUM(case when fzsxm like '%水利建设专项收入%' then fhj else 0 end) / 10000, 2) 水利建设专项收入,
       ROUND(SUM(case when fzsxm like '%文化事业建设费%' then fhj else 0 end) / 10000, 2) 文化事业建设费,
       ROUND(SUM(fzyj) / 10000, 2) 今年中央级,
       ROUND(SUM(fssj) / 10000, 2) 今年省市级,
       ROUND(SUM(fdsj) / 10000, 2) 今年地市级,
       ROUND(SUM(fqxj) / 10000, 2) 今年区县级,
       ROUND(sum(fxzj) / 10000, 2) 今年乡镇级
		FROM tb_dw_srfx_srfx_main
        WHERE 1 = 1
		AND fzsxm like '%税'
		and fnsrmc is not null
		and frkrq BETWEEN TO_DATE(#{start_time}, 'YYYY-MM') AND LAST_DAY(TO_DATE(#{end_time}, 'YYYY-MM'))
		and fnsrmc in(SELECT FQYMC FROM TB_DW_CYJS_YQQYXX_GEN WHERE FNF = #{fyear} AND FSSYQ = #{fyqmc})
		GROUP BY rollup((fnsrmc))
		order by case when 纳税人名称 ='合计' then 0 else 1 end,本期合计 desc
	</select>
</mapper>
