<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.yjztfx.mapper.GasStationEarlyWarningMapper">

    <!-- 油品/型号/月度均价 处理 -->
    <sql id="oilPriceSql">
        WITH
            <!-- 步骤 1：所有月份（按月截断） -->
            months AS (
                SELECT DISTINCT TRUNC(ftjsj, 'MM') AS period
                FROM TB_ODS_ZHZS_DQCYQYYJSJ_GEN
            ),
            <!--  步骤 1：所有 (类型, 型号) -->
            types AS (
                SELECT DISTINCT fhwlx fyplx, fhwxh fypxh
                FROM TB_DW_ZHZS_DZYDXX_GEN
                where fhwxh is not null
            ),
            grid AS (
                SELECT
                    t.fyplx,
                    t.fypxh,
                    m.period,
                    TO_CHAR(m.period, 'yyyy') AS fnf,
                    TO_CHAR(m.period, 'MM')   AS fyf
                FROM types t
                         CROSS JOIN months m
            ),
            <!--  步骤 2：已有平均价 -->
            agg AS (
                SELECT
                    fyplx,
                    fypxh,
                    TRUNC(ftjsj, 'MM') AS period,
                    AVG(fjgyd) AS fjgyd
                FROM TB_ODS_ZHZS_DQCYQYYJSJ_GEN
                GROUP BY fyplx, fypxh, TRUNC(ftjsj, 'MM')
            ),
            <!--  步骤 4：取默认的 0 号/92 号价格 -->
            default_price AS (
                SELECT
                    fyplx,
                    AVG(fjgyd) AS default_fjgyd
                FROM agg
                WHERE (fyplx = '柴油' AND fypxh = '0号')
                   OR (fyplx = '汽油' AND fypxh = '92号')
                GROUP BY fyplx
            )
        SELECT
            g.fyplx, g.fypxh, g.fnf, g.fyf,
            <!--  先用原值；若 NULL 则用窗口“最近值”；若仍 NULL 则用默认价 -->
            COALESCE( a.fjgyd, MAX(a.fjgyd) OVER ( PARTITION BY g.fyplx, g.fypxh
                ORDER BY g.period ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW ), dp.default_fjgyd
            ) AS fpjjg
        FROM grid g
                 LEFT JOIN agg a
                           ON a.fyplx   = g.fyplx
                               AND a.fypxh   = g.fypxh
                               AND a.period  = g.period
                 LEFT JOIN default_price dp
                           ON dp.fyplx  = g.fyplx
        ORDER BY g.fyplx, g.fypxh, g.period
    </sql>

    <!-- 汽车保有量 处理 -->
    <sql id="carHoldingsSql">
        WITH merged_data AS (
            SELECT
                r.fdqmc,
                CAST(REPLACE(y.ftjnf, '年', '') AS INT) AS fnf,
                d.fqcbylwl
            FROM
                    (SELECT DISTINCT fdqmc FROM TB_ODS_ZHZS_DQQCBYLSJ_GEN) r
                        CROSS JOIN
                    (SELECT DISTINCT fnf ftjnf FROM TB_DM_YJ_JYZYJSSSJ_GEN) y
                        LEFT JOIN
                TB_ODS_ZHZS_DQQCBYLSJ_GEN d
                ON r.fdqmc = d.fdqmc
                    AND y.ftjnf = REPLACE(d.ftjnf, '年', '')
        )
        SELECT
            fdqmc,
            fnf,
            COALESCE(
                    fqcbylwl,
                    LAST_VALUE(fqcbylwl IGNORE NULLS)
                    OVER (
                            PARTITION BY fdqmc
                    ORDER BY fnf ASC
                    ROWS BETWEEN UNBOUNDED PRECEDING AND 1 PRECEDING
                    )
            ) AS fqcbyl
        FROM merged_data
    </sql>

    <sql id="gasStationsNumberCountSql">
    <!--     &#45;&#45; 1. 拿到每个纳税人在每个缴费性质下、每年是否出现过 -->
        with FNSRMC_YEAR as (
            select distinct
                md.FNSRMC,
                FSSS FJDXZ,
                md.flx,md.fnsrsbh,
                extract(year from FDJRQ) as YEAR
            from TB_DW_ZHZS_JYZSWMD_GEN md
            where FDJRQ is not null and md.FNSRMC is not null
            ),

        <!-- &#45;&#45; 2. 列出所有年份和所有缴费性质组合 -->
        YEAR_JDXZ as (
            select
            y.YEAR,
            FSSS FJDXZ
            from (
            select distinct extract(year from FDJRQ) as YEAR
            from TB_DW_ZHZS_JYZSWMD_GEN
            where FDJRQ is not null
            and extract(year from FDJRQ) >=2020
            ) y
            cross join (
            select distinct FSSS
            from TB_DW_ZHZS_JYZSWMD_GEN
            where FDJRQ is not null
            ) j
        ),

        <!-- &#45;&#45; 3. 计算每个纳税人在各缴费性质下的首次出现年份 -->
        FIRST_APPEAR as (
            select
                FNSRMC, FJDXZ, flx, fnsrsbh, min(YEAR) as FIRST_YEAR
            from FNSRMC_YEAR
            group by FNSRMC, FJDXZ
            )
        select
            yj.FJDXZ fdqmc,
            count(1) as fjyzsl,
            count(CASE WHEN (fa.flx= '民营' or fa.flx is null) THEN 1 END) as fjyzslmy,
            count(CASE WHEN fa.flx != '民营' THEN 1 END) as fjyzslgy,
            yj.YEAR fnf

        from YEAR_JDXZ yj
        left join FIRST_APPEAR fa on fa.FJDXZ = yj.FJDXZ and fa.FIRST_YEAR &lt;= yj.YEAR
        group by yj.YEAR, yj.FJDXZ
    </sql>
    
    <!-- 按区域汇总sql -->
    <sql id="regionRollupDzydAndSrfxSql">
        with t_ypjg as (
            <include refid="oilPriceSql"/>
        ),
        t_qcbyl as (
            <include refid="carHoldingsSql"/>
        ),
        t_jyztj as (
            <include refid="gasStationsNumberCountSql"/>
        ),
        t_dzyd as (
        select
            dzyd.fysmddsss,
            sum(FHWZZL) fhwzzl,
            SUM(CASE WHEN FKHLX = '加油站' THEN FHWZZL END) fjyzhwzzl,
            SUM(CASE WHEN FHWLX = '柴油' and FKHLX = '加油站' THEN FHWZZL END) fjyzcyzl,
            SUM(CASE WHEN FHWLX = '汽油' and FKHLX = '加油站' THEN FHWZZL END) fjyzqyzl,

            sum(CASE WHEN FKHLX = '加油站'
                              and (md.flx != '民营' or dzyd.fshf like '%中国石油%' or dzyd.fshf like '%中国石化%' or dzyd.fshf like '%中石油%' or dzyd.fshf like '%中石化%' or dzyd.fshf like '%中油交通%') THEN FHWZZL END) fhwzzlgy,
            SUM(CASE WHEN FKHLX = '加油站' and FHWLX = '柴油'
                              and (md.flx != '民营' or dzyd.fshf like '%中国石油%' or dzyd.fshf like '%中国石化%' or dzyd.fshf like '%中石油%' or dzyd.fshf like '%中石化%' or dzyd.fshf like '%中油交通%') THEN FHWZZL END) fcyzlgy,
            SUM(CASE WHEN FKHLX = '加油站' and FHWLX = '汽油'
                              and (md.flx != '民营' or dzyd.fshf like '%中国石油%' or dzyd.fshf like '%中国石化%' or dzyd.fshf like '%中石油%' or dzyd.fshf like '%中石化%' or dzyd.fshf like '%中油交通%') THEN FHWZZL END) fqyzlgy,

            sum(CASE WHEN FKHLX = '加油站' and (md.flx = '民营'
                               or (md.flx is null and dzyd.fshf not like '%中国石油%' and dzyd.fshf not like '%中国石化%' and dzyd.fshf not like '%中石油%' and dzyd.fshf not like '%中石化%' and dzyd.fshf not like '%中油交通%')) THEN FHWZZL END) fhwzzlmy,
            SUM(CASE WHEN FKHLX = '加油站' and FHWLX = '柴油' and (md.flx = '民营'
                               or (md.flx is null and dzyd.fshf not like '%中国石油%' and dzyd.fshf not like '%中国石化%' and dzyd.fshf not like '%中石油%' and dzyd.fshf not like '%中石化%' and dzyd.fshf not like '%中油交通%')) THEN FHWZZL END) fcyzlmy,
            SUM(CASE WHEN FKHLX = '加油站' and FHWLX = '汽油' and (md.flx = '民营'
                               or (md.flx is null and dzyd.fshf not like '%中国石油%' and dzyd.fshf not like '%中国石化%' and dzyd.fshf not like '%中石油%' and dzyd.fshf not like '%中石化%' and dzyd.fshf not like '%中油交通%')) THEN FHWZZL END) fqyzlmy,


            round(SUM(fhwzzl*ypjg.fpjjg),2) fyjxse,
            round(SUM(CASE WHEN FKHLX = '加油站' and (md.flx = '民营'
                                     or (md.flx is null and dzyd.fshf not like '%中国石油%' and dzyd.fshf not like '%中国石化%' and dzyd.fshf not like '%中石油%' and dzyd.fshf not like '%中石化%' and dzyd.fshf not like '%中油交通%'))
                            THEN FHWZZL END*ypjg.fpjjg),2) fyjxsemy,
            round(SUM(CASE WHEN FKHLX = '加油站'
                                    and (md.flx != '民营' or dzyd.fshf like '%中国石油%' or dzyd.fshf like '%中国石化%' or dzyd.fshf like '%中石油%' or dzyd.fshf like '%中石化%' or dzyd.fshf like '%中油交通%')
                            THEN FHWZZL END*ypjg.fpjjg),2) fyjxsegy,

            year(frksj) fnf
        from (select
            fshfmc,fshf,fnsrmc,fysmddsssf,fysmddsss,fhwzzl,FHWLX,frksj,fkhlx,
            IFNULL(fhwxh,IF(FHWLX='汽油','92号','0号')) fhwxh
        from TB_DW_ZHZS_DZYDXX_GEN) dzyd
        left join t_ypjg ypjg on ypjg.fyplx = dzyd.fhwlx and ypjg.fypxh = dzyd.fhwxh
                              and concat(ypjg.fnf,'-',ypjg.fyf) = to_char(dzyd.frksj, 'yyyy-mm')
        left join TB_DW_ZHZS_JYZSWMD_GEN md on md.fnsrmc = dzyd.fnsrmc
        where 1=1
          and dzyd.fysmddsssf = '甘肃省'
          <if test="fstartdate != null and fstartdate != '' and fenddate != null and fenddate != ''">
            and to_date(frksj,'YYYY-MM-DD') between to_date(#{fstartdate},'YYYY-MM') and last_day(to_date(#{fenddate},'YYYY-MM'))
          </if>

        group by dzyd.fysmddsss,year(frksj)
        ),
        t_srfx as (
            select srfx.FSSDS fdqmc,srfx.flx,
               SUM(FSHJ) AS FSHJ,
               COUNT(DISTINCT djxx.FNSRMC) FDJHS,
               COUNT(DISTINCT srfx.FNSRMC) FNSHS,
               SUM(FZZS) FHJ_ZZS,
               SUM(CASE WHEN (srfx.flx= '民营' or srfx.flx is null) THEN FZZS END) FHJ_ZZS_MY,
               SUM(CASE WHEN srfx.flx != '民营' THEN FZZS END) FHJ_ZZS_GY,
               COUNT(DISTINCT CASE WHEN FZZS > 0 THEN srfx.FNSRMC END) FNSHS_ZZS,
               COUNT(DISTINCT CASE WHEN FZZS > 0 and (srfx.flx= '民营' or srfx.flx is null) THEN srfx.FNSRMC END) FNSHS_ZZS_MY,
               COUNT(DISTINCT CASE WHEN FZZS > 0 and srfx.flx != '民营' THEN srfx.FNSRMC END) FNSHS_ZZS_GY,

               COUNT(DISTINCT CASE WHEN FZZS > 0 and (srfx.flx= '民营' or srfx.flx is null) THEN djxx.FNSRMC END) FDJHS_MY,
               COUNT(DISTINCT CASE WHEN FZZS > 0 and srfx.flx != '民营' THEN djxx.FNSRMC END) FDJHS_GY,

               SUM(FZZSZNJ) FHJ_FZZSZNJ,
               SUM(CASE WHEN (srfx.flx= '民营' or srfx.flx is null) THEN FZZSZNJ END) FHJ_FZZSZNJ_MY,
               SUM(CASE WHEN srfx.flx != '民营' THEN FZZSZNJ END) FHJ_FZZSZNJ_GY,
               COUNT(DISTINCT CASE WHEN FZZSZNJ > 0 THEN srfx.FNSRMC END) FNSHS_FZZSZNJ,
               COUNT(DISTINCT CASE WHEN FZZSZNJ > 0 and (srfx.flx= '民营' or srfx.flx is null) THEN srfx.FNSRMC END) FNSHS_FZZSZNJ_MY,
               COUNT(DISTINCT CASE WHEN FZZSZNJ > 0 and srfx.flx != '民营' THEN srfx.FNSRMC END) FNSHS_FZZSZNJ_GY,

               SUM(FYSCBSK) FHJ_FYSCBSK,
               SUM(CASE WHEN (srfx.flx= '民营' or srfx.flx is null) THEN FYSCBSK END) FHJ_FYSCBSK_MY,
               SUM(CASE WHEN srfx.flx != '民营' THEN FYSCBSK END) FHJ_FYSCBSK_GY,

               SUM(FFCS+FCZTDSYS) FHJ_FTLS,
               SUM(CASE WHEN (srfx.flx= '民营' or srfx.flx is null) THEN (FFCS+FCZTDSYS) END) FHJ_FTLS_MY,
               SUM(CASE WHEN srfx.flx != '民营' THEN (FFCS+FCZTDSYS) END) FHJ_FTLS_GY,
               COUNT(DISTINCT CASE WHEN (FFCS+FCZTDSYS) > 0  THEN srfx.FNSRMC END) FNSHS_FTLS,
               COUNT(DISTINCT CASE WHEN (FFCS+FCZTDSYS) > 0 and (srfx.flx= '民营' or srfx.flx is null) THEN srfx.FNSRMC END) FNSHS_FTLS_MY,
               COUNT(DISTINCT CASE WHEN (FFCS+FCZTDSYS) > 0 and srfx.flx != '民营' THEN srfx.FNSRMC END) FNSHS_FTLS_GY,
               srfx.FNF
            from TB_DM_YJ_JYZYJSSSJ_GEN srfx
            left join TB_DW_SRFX_SWDJXX_MAIN djxx on djxx.FNSRMC = srfx.FNSRMC
            where 1=1
              <if test="fstartdate != null and fstartdate != '' and fenddate != null and fenddate != ''">
                and to_date(concat(srfx.fnf,'-',srfx.fyf),'YYYY-MM') between to_date(#{fstartdate},'YYYY-MM') and last_day(to_date(#{fenddate},'YYYY-MM'))
              </if>
              <choose>
                  <when test="frqkjlx != null and frqkjlx != ''">
                      and srfx.frqkjlx = #{frqkjlx}
                  </when>
                  <otherwise>
                      and srfx.frqkjlx = '税款所属期'
                  </otherwise>
              </choose>

            group by srfx.FSSDS,srfx.FNF
        )
    </sql>

    <!-- 明细sql -->
    <sql id="detailsDzydAndSrfxSql">
        with t_ypjg as (
            <include refid="oilPriceSql"/>
        ),
        t_qcbyl as (
            <include refid="carHoldingsSql"/>
        ),
        t_dzyd_details as (
            select
                dzyd.fshfmc,dzyd.fshf fjyzmc,
                sum(FHWZZL) fhwzzl,
                SUM(CASE WHEN FHWLX = '柴油' THEN FHWZZL END) fcyzl,
                SUM(CASE WHEN FHWLX = '汽油' THEN FHWZZL END) fqyzl,
                round(SUM(fhwzzl*ypjg.fpjjg),2) fyjxse,
                year(frksj) fnf
            from (select
                fshfmc,fshf,fysmddsssf,fysmddsss,fhwzzl,FHWLX,frksj,fkhlx,
                IFNULL(fhwxh,IF(FHWLX='汽油','92号','0号')) fhwxh
            from TB_DW_ZHZS_DZYDXX_GEN where FKHLX = '加油站') dzyd
            left join t_ypjg ypjg on ypjg.fyplx = dzyd.fhwlx and ypjg.fypxh = dzyd.fhwxh
                                  and concat(ypjg.fnf,'-',ypjg.fyf) = to_char(dzyd.frksj, 'yyyy-mm')
            where 1=1
              and dzyd.fysmddsssf = '甘肃省'
              <if test="fstartdate != null and fstartdate != '' and fenddate != null and fenddate != ''">
                and to_date(frksj,'YYYY-MM-DD') between to_date(#{fstartdate},'YYYY-MM') and last_day(to_date(#{fenddate},'YYYY-MM'))
              </if>
            group by dzyd.fshfmc,year(frksj)
        ),
        t_srfx_details as (
            select srfx.FNSRMC,srfx.FSSS,srfx.FSSDS,srfx.FSSQX,srfx.flx,
               max(srfx.FNSRSBH)FNSRSBH,
               SUM(FSHJ) AS FSHJ,
               COUNT(DISTINCT djxx.FNSRMC) FDJHS,
               COUNT(DISTINCT srfx.FNSRMC) FNSHS,
               SUM(FZZS) FHJ_ZZS,
               SUM(FZZSZNJ) FHJ_FZZSZNJ,
               SUM(FYSCBSK) FHJ_FYSCBSK,
               COUNT(DISTINCT CASE WHEN FZZS > 0 THEN srfx.FNSRMC END) FNSHS_ZZS,
               COUNT(DISTINCT CASE WHEN FZZSZNJ > 0 THEN srfx.FNSRMC END) FNSHS_FZZSZNJ,
               SUM(FFCS+FCZTDSYS) FHJ_FTLS,
               COUNT(DISTINCT CASE WHEN (FFCS+FCZTDSYS) > 0  THEN srfx.FNSRMC END) FNSHS_FTLS,
               srfx.FNF
            from TB_DM_YJ_JYZYJSSSJ_GEN srfx
            left join TB_DW_SRFX_SWDJXX_MAIN djxx on djxx.FNSRMC = srfx.FNSRMC
            where 1=1
              <if test="fstartdate != null and fstartdate != '' and fenddate != null and fenddate != ''">
                and to_date(concat(srfx.fnf,'-',srfx.fyf),'YYYY-MM') between to_date(#{fstartdate},'YYYY-MM') and last_day(to_date(#{fenddate},'YYYY-MM'))
              </if>
              <choose>
                  <when test="frqkjlx != null and frqkjlx != ''">
                      and srfx.frqkjlx = #{frqkjlx}
                  </when>
                  <otherwise>
                      and srfx.frqkjlx = '税款所属期'
                  </otherwise>
              </choose>

            group by srfx.FNSRMC,srfx.FSSDS,srfx.FNF
        )
    </sql>

    <!-- 加油站区域汇总列表 -->
    <select id="getGasStationRegionRollupList" resultType="java.util.Map" parameterType="java.util.Map">
        <include refid="regionRollupDzydAndSrfxSql"/>
        SELECT
            CASE WHEN GROUPING(srfx.fdqmc)=1 THEN '合计' ELSE srfx.fdqmc END fdqmc,
            ROUND(SUM(srfx.FSHJ) / 10000, 2) AS FSHJ,
            SUM(srfx.FNSHS) AS FNSHS,

            SUM(srfx.FDJHS) AS FDJHS,
            SUM(srfx.FDJHS_MY) AS FDJHS_MY,
            SUM(srfx.FDJHS_GY) AS FDJHS_GY,

            ROUND(SUM(srfx.FHJ_ZZS) /SUM(jyztj.fjyzsl) / 10000, 2) AS FPJSS_ZZS,
            ROUND(SUM(srfx.FHJ_ZZS_MY) /SUM(jyztj.fjyzslmy) / 10000, 2) AS FPJSS_ZZS_MY,
            ROUND(SUM(srfx.FHJ_ZZS_GY) /SUM(jyztj.fjyzslgy) / 10000, 2) AS FPJSS_ZZS_GY,
            ROUND(SUM(srfx.FHJ_ZZS) /SUM(srfx.FNSHS_ZZS) / 10000, 2) AS FPJSS_NSHS_ZZS,
            ROUND(SUM(srfx.FHJ_ZZS_MY) /SUM(srfx.FNSHS_ZZS_MY) / 10000, 2) AS FPJSS_NSHS_ZZS_MY,
            ROUND(SUM(srfx.FHJ_ZZS_GY) /SUM(srfx.FNSHS_ZZS_GY) / 10000, 2) AS FPJSS_NSHS_ZZS_GY,


            SUM(jyztj.fjyzsl) AS FJYZSL,
            SUM(jyztj.fjyzslmy) AS FJYZSLMY,
            SUM(jyztj.fjyzslgy) AS FJYZSLGY,

            ROUND(SUM(srfx.FHJ_FYSCBSK) / 10000, 2) AS FHJ_FYSCBSK,
            ROUND(SUM(srfx.FHJ_FYSCBSK_MY) / 10000, 2) AS FHJ_FYSCBSK_MY,
            ROUND(SUM(srfx.FHJ_FYSCBSK_GY) / 10000, 2) AS FHJ_FYSCBSK_GY,

            ROUND(SUM(srfx.FHJ_FZZSZNJ), 2) AS FHJ_FZZSZNJ,
            ROUND(SUM(srfx.FHJ_FZZSZNJ_MY), 2) AS FHJ_FZZSZNJ_MY,
            ROUND(SUM(srfx.FHJ_FZZSZNJ_GY), 2) AS FHJ_FZZSZNJ_GY,
            SUM(srfx.FNSHS_FZZSZNJ) AS FNSHS_FZZSZNJ,
            SUM(srfx.FNSHS_FZZSZNJ_MY) AS FNSHS_FZZSZNJ_MY,
            SUM(srfx.FNSHS_FZZSZNJ_GY) AS FNSHS_FZZSZNJ_GY,

            ROUND(SUM(srfx.FHJ_ZZS) / 10000, 2) AS FHJ_ZZS,
            ROUND(SUM(srfx.FHJ_ZZS_MY) / 10000, 2) AS FHJ_ZZS_MY,
            ROUND(SUM(srfx.FHJ_ZZS_GY) / 10000, 2) AS FHJ_ZZS_GY,
            SUM(srfx.FNSHS_ZZS) AS FNSHS_ZZS,
            SUM(srfx.FNSHS_ZZS_MY) AS FNSHS_ZZS_MY,
            SUM(srfx.FNSHS_ZZS_GY) AS FNSHS_ZZS_GY,
            ROUND(SUM(srfx.FHJ_FTLS) / 10000, 2) AS FHJ_FTLS,
            ROUND(SUM(srfx.FHJ_FTLS_MY) / 10000, 2) AS FHJ_FTLS_MY,
            ROUND(SUM(srfx.FHJ_FTLS_GY) / 10000, 2) AS FHJ_FTLS_GY,
            SUM(srfx.FNSHS_FTLS) AS FNSHS_FTLS,
            SUM(srfx.FNSHS_FTLS_MY) AS FNSHS_FTLS_MY,
            SUM(srfx.FNSHS_FTLS_GY) AS FNSHS_FTLS_GY,

            ROUND( CASE WHEN SUM(jyztj.fjyzsl) = 0 THEN 0 ELSE SUM(srfx.FNSHS_FTLS) / SUM(jyztj.fjyzsl) * 100.0 END, 2 ) AS FNSZB_FTLS,
            ROUND( CASE WHEN SUM(jyztj.fjyzslmy) = 0 THEN 0 ELSE SUM(srfx.FNSHS_FTLS_MY) / SUM(jyztj.fjyzslmy) * 100.0 END, 2 ) AS FNSZB_FTLS_MY,
            ROUND(  CASE WHEN SUM(jyztj.fjyzslgy) = 0 THEN 0 ELSE SUM(srfx.FNSHS_FTLS_GY) / SUM(jyztj.fjyzslgy) * 100.0  END, 2 ) AS FNSZB_FTLS_GY,
            ROUND(  CASE WHEN SUM(srfx.FNSHS_ZZS_MY) = 0 THEN 0 ELSE SUM(srfx.FNSHS_FTLS_MY) / SUM(srfx.FNSHS_ZZS_MY) * 100.0  END, 2 ) AS FZZSZB_FTLS_MY,
            ROUND(  CASE WHEN SUM(srfx.FNSHS_ZZS_GY) = 0 THEN 0 ELSE SUM(srfx.FNSHS_FTLS_GY) / SUM(srfx.FNSHS_ZZS_GY) * 100.0  END, 2 ) AS FZZSZB_FTLS_GY,


            SUM(dzyd.fhwzzl) AS fhwzzl,
            SUM(dzyd.fjyzhwzzl) AS fjyzhwzzl,
            SUM(dzyd.fjyzcyzl) AS fjyzcyzl,
            SUM(dzyd.fjyzqyzl) AS fjyzqyzl,
            ROUND(SUM(dzyd.fyjxse) / 10000, 2) AS fyjxse,

            SUM(dzyd.fhwzzlmy) AS fhwzzlmy,
            SUM(dzyd.fcyzlmy) AS fcyzlmy,
            SUM(dzyd.fqyzlmy) AS fqyzlmy,
            ROUND(SUM(dzyd.fyjxsemy) / 10000, 2) AS fyjxsemy,

            SUM(dzyd.fhwzzlgy) AS fhwzzlgy,
            SUM(dzyd.fcyzlgy) AS fcyzlgy,
            SUM(dzyd.fqyzlgy) AS fqyzlgy,
            ROUND(SUM(dzyd.fyjxsegy) / 10000, 2) AS fyjxsegy,

            SUM(qcbyl.fqcbyl) AS fqcbyl,

            <if test="flcs != null and flcs != '' and fbglyh != null and fbglyh != ''">
                ROUND(#{flcs} * SUM(qcbyl.fqcbyl*10000) * #{fbglyh} / 100.0 / 1176, 2) AS fclyjhyl,
            </if>

            <if test="fzdysfl != null and fzdysfl != ''">
                ROUND(SUM(dzyd.fyjxse) / (1 + #{fjzysl} / 100.0) * (#{fzdysfl} / 100.0) / 10000, 2) AS fcs_zzs,
                ROUND((SUM(dzyd.fyjxse) / (1 + #{fjzysl} / 100.0) * (#{fzdysfl} / 100.0) - SUM(srfx.FHJ_ZZS)) / 10000, 2) AS fce_zzs,
            </if>
            <if test="fgysfl != null and fgysfl != ''">
                ROUND(SUM(dzyd.fyjxsegy) / (1 + #{fjzysl} / 100.0) * (#{fgysfl} / 100.0) / 10000, 2) AS fcs_zzs_gy,
                ROUND((SUM(dzyd.fyjxsegy) / (1 + #{fjzysl} / 100.0) * (#{fgysfl} / 100.0) - SUM(srfx.FHJ_ZZS_GY)) / 10000, 2) AS fce_zzs_gy,
            </if>
            <if test="fmysfl != null and fmysfl != ''">
                ROUND(SUM(dzyd.fyjxsemy) / (1 + #{fjzysl} / 100.0) * (#{fmysfl} / 100.0) / 10000, 2) AS fcs_zzs_my,
                ROUND((SUM(dzyd.fyjxsemy) / (1 + #{fjzysl} / 100.0) * (#{fmysfl} / 100.0) - SUM(srfx.FHJ_ZZS_MY)) / 10000, 2) AS fce_zzs_my,
            </if>

            ROUND(SUM(srfx.FHJ_ZZS) / SUM(dzyd.fjyzcyzl + dzyd.fjyzqyzl), 2) AS fpjyse_zzs,
            ROUND(SUM(srfx.FHJ_ZZS_MY) / SUM(dzyd.fcyzlmy + dzyd.fqyzlmy), 2) AS fpjyse_zzs_my,
            ROUND(SUM(srfx.FHJ_ZZS_GY) / SUM(dzyd.fcyzlgy + dzyd.fqyzlgy), 2) AS fpjyse_zzs_gy,
            srfx.FNF,
            CASE WHEN GROUPING(srfx.fdqmc)=1 THEN 0 ELSE 1 END sort_flag
        FROM t_srfx srfx
        LEFT JOIN t_dzyd dzyd ON srfx.fdqmc = dzyd.fysmddsss AND srfx.FNF = dzyd.fnf
        LEFT JOIN t_qcbyl qcbyl ON qcbyl.fdqmc = srfx.fdqmc AND srfx.FNF = qcbyl.fnf
        LEFT JOIN t_jyztj jyztj ON jyztj.fdqmc = srfx.fdqmc AND srfx.FNF = jyztj.fnf

        GROUP BY ROLLUP(srfx.fdqmc)

        ORDER BY sort_flag ASC, SUM(dzyd.fhwzzl) DESC, SUM(srfx.FHJ_ZZS) DESC;

    </select>

    <!-- 加油站明细列表 -->
    <select id="getGasStationDetailsList" resultType="java.util.Map" parameterType="java.util.Map">
        <include refid="detailsDzydAndSrfxSql"/>
        SELECT
            CASE WHEN GROUPING(md.fnsrmc) = 1 THEN '合计' ELSE md.fnsrmc END AS FNSRMC,
            CASE WHEN GROUPING(md.fnsrmc) = 1 THEN NULL ELSE md.FNSRSBH END AS FNSRSBH,
            CASE WHEN GROUPING(md.fnsrmc) = 1 THEN NULL ELSE dzyd.fjyzmc END AS fjyzmc,
            CASE WHEN GROUPING(md.fnsrmc) = 1 THEN NULL ELSE IFNULL(md.FSSQX, md.FSSS) END AS fdqmc,
            CASE WHEN GROUPING(md.fnsrmc) = 1 THEN NULL ELSE if(md.flx = '民营' or md.flx is null,'民营','国营') END AS flx,
            md.FSSQX, md.FSSS,
            ROUND(SUM(srfx.FSHJ) /10000, 2) AS FSHJ,
            SUM(srfx.FDJHS) AS FDJHS,
            SUM(srfx.FNSHS)   AS FNSHS,
            ROUND(SUM(srfx.FHJ_ZZS)   /10000, 2) AS FHJ_ZZS,
            SUM(srfx.FNSHS_ZZS)  AS FNSHS_ZZS,
            ROUND(SUM(srfx.FHJ_FZZSZNJ), 2) AS FHJ_FZZSZNJ,
            ROUND(SUM(srfx.FHJ_FYSCBSK) / 10000, 2) AS FHJ_FYSCBSK,
            SUM(srfx.FNSHS_FZZSZNJ)  AS FNSHS_FZZSZNJ,
            ROUND(SUM(srfx.FHJ_FTLS) /10000, 2) AS FHJ_FTLS,
            SUM(srfx.FNSHS_FTLS)  AS FNSHS_FTLS,
            ROUND( SUM(srfx.FNSHS_FTLS) / NULLIF(SUM(srfx.FNSHS),0) * 100.0 ,2) AS FNSZB_FTLS,

            SUM(dzyd.fhwzzl) AS fhwzzl,
            SUM(dzyd.fcyzl) AS fcyzl,
            SUM(dzyd.fqyzl) AS fqyzl,
            ROUND(SUM(dzyd.fyjxse) /10000, 2) AS fyjxse,

            <if test="fgysfl != null and fgysfl != '' and fmysfl != null and fmysfl != ''">
                ROUND((SUM(if(srfx.flx != '民营',dzyd.fyjxse,0)) / (1 + ${fjzysl} / 100.0) * (${fgysfl} / 100.0)
                          + SUM(if(srfx.flx= '民营' or srfx.flx is null,dzyd.fyjxse,0)) / (1 + ${fjzysl} / 100.0) * (${fmysfl} / 100.0)) / 10000, 2) AS fcs_zzs,
                ROUND((SUM(if(srfx.flx != '民营',dzyd.fyjxse,0)) / (1 + #{fjzysl} / 100.0) * (#{fgysfl} / 100.0)
                           + SUM(if(srfx.flx= '民营' or srfx.flx is null,dzyd.fyjxse,0)) / (1 + #{fjzysl} / 100.0) * (#{fmysfl} / 100.0)
                           - SUM(srfx.FHJ_ZZS)) / 10000, 2) AS fce_zzs,
            </if>

            ROUND( SUM(srfx.FHJ_ZZS) / SUM(dzyd.fhwzzl) ,2) AS fpjyse_zzs,
            srfx.FNF
        FROM TB_DW_ZHZS_JYZSWMD_GEN md
        LEFT JOIN t_srfx_details srfx ON srfx.fnsrmc = md.fnsrmc AND srfx.FSSQX = md.fssqx
        LEFT JOIN t_dzyd_details dzyd ON srfx.FNSRMC = dzyd.fshfmc AND srfx.FNF = dzyd.fnf

        WHERE 1 = 1
        <if test="fssqy != null and fssqy != ''">
          AND IFNULL(md.FSSQX, md.FSSS) IN
            <foreach collection="fssqy.split(',')" item="item" open="(" separator="," close=")">
              #{item}
            </foreach>
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">
          AND md.FNSRMC like concat('%',#{fnsrmc},'%')
        </if>
        <choose>
             <when test="flx == '民营'">
                AND (md.flx= '民营' or md.flx is null)
            </when>
             <when test="flx == '国营'">
                AND md.flx != '民营'
            </when>
        </choose>

        <if test="fznjFlag == 'true'">
            and srfx.FHJ_FZZSZNJ > 0
        </if>

        GROUP BY ROLLUP( md.FNSRMC)

        ORDER BY GROUPING(md.FNSRMC) desc, FHJ_FYSCBSK DESC,fhwzzl DESC, FHJ_ZZS DESC

    </select>

    <!-- 加油站明细平均线 -->
    <select id="getGasStationDetailsAverages" resultType="java.util.Map" parameterType="java.util.Map">
        <include refid="detailsDzydAndSrfxSql"/>
        SELECT
            COALESCE(srfx.flxmc, '合计') AS flx,
            SUM(srfx.FDJHS) AS FDJHS,
            ROUND(SUM(srfx.FHJ_ZZS)   /10000, 2) AS FHJ_ZZS,
            ROUND(SUM(srfx.FHJ_FTLS) /10000, 2) AS FHJ_FTLS,

            ROUND(AVG(srfx.FHJ_ZZS) /10000 ,2) AS fpj_ZZS,
            ROUND(AVG(srfx.FHJ_FTLS) /10000 ,2) AS fpj_FTLS,
            ROUND(AVG(dzyd.fhwzzl) ,2) AS fpj_hwzzl,

            ROUND(AVG(srfx.FHJ_ZZS) / AVG(dzyd.fhwzzl) ,2) AS fpj_yse
        FROM (select *,if(flx = '民营' or flx is null,'民营','国营') flxmc from t_srfx_details) srfx
        LEFT JOIN t_dzyd_details dzyd ON srfx.FNSRMC = dzyd.fshfmc AND srfx.FNF = dzyd.fnf
        WHERE 1 = 1
        <if test="fssqy != null and fssqy != ''">
          AND IFNULL(srfx.FSSQX, srfx.FSSDS) IN
            <foreach collection="fssqy.split(',')" item="item" open="(" separator="," close=")">
              #{item}
            </foreach>
        </if>

        GROUP BY ROLLUP(srfx.flxmc)
        ORDER BY GROUPING(srfx.flxmc) desc
    </select>

    <!-- 加油站房土两税区域对比 -->
    <select id="getHousingAndLandTaxRegionContrast" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData" parameterType="java.util.Map">
        <include refid="regionRollupDzydAndSrfxSql"/>
        select fdqmc xAxis,FSZ yAxis, FXMMC legend, case when FXMMC like '%占比' then 'line' else 'bar'end type
        from (
            select
                srfx.fdqmc,
                sum(jyztj.fjyzsl)fjyzsl,
                sum(srfx.FNSHS_FTLS) FNSHS_FTLS,
                sum(srfx.FNSHS_ZZS) FNSHS_ZZS,
                round(sum(srfx.FNSHS_FTLS)/sum(jyztj.fjyzsl)*100.0,2)FNSZB_FTLS,
                round(sum(srfx.FNSHS_ZZS)/sum(jyztj.fjyzsl)*100.0,2)FNSZB_ZZS,
                round(sum(srfx.FNSHS_FTLS)/sum(srfx.FNSHS_ZZS)*100.0,2)FZZSZB_FTLS,
                srfx.FNF
            FROM t_srfx srfx
            LEFT JOIN t_dzyd dzyd ON srfx.fdqmc = dzyd.fysmddsss AND srfx.FNF = dzyd.fnf
            LEFT JOIN t_qcbyl qcbyl ON qcbyl.fdqmc = srfx.fdqmc AND srfx.FNF = qcbyl.fnf
            LEFT JOIN t_jyztj jyztj ON jyztj.fdqmc = srfx.fdqmc AND srfx.FNF = jyztj.fnf

            group by srfx.fdqmc
         )
         UNPIVOT ( FSZ for FXMMC in (fjyzsl AS '加油站户数',FNSHS_FTLS AS '房土两税纳税户数',FNSHS_ZZS AS '增值税纳税户数',FNSZB_FTLS AS '房土两税缴纳户数占比',FZZSZB_FTLS AS '房土两税占增值税缴纳户数占比'))

    </select>

    <!-- 增值税平均每吨油税额对比 -->
    <select id="getIncrementTaxAvgOilContrast" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData" parameterType="java.util.Map">
        <include refid="regionRollupDzydAndSrfxSql"/>
        select fdqmc xAxis,FSZ yAxis, FXMMC legend, 'bar' type
        from (
            select
                srfx.fdqmc,
                ROUND(SUM(srfx.FHJ_ZZS) / SUM(dzyd.fjyzcyzl + dzyd.fjyzqyzl), 2) AS fpjyse_zzs,
                ROUND(SUM(srfx.FHJ_ZZS_MY) / SUM(dzyd.fcyzlmy + dzyd.fqyzlmy), 2) AS fpjyse_zzs_my,
                ROUND(SUM(srfx.FHJ_ZZS_GY) / SUM(dzyd.fcyzlgy + dzyd.fqyzlgy), 2) AS fpjyse_zzs_gy,
                 ROUND(AVG(srfx.FHJ_ZZS_MY)/10000, 2) AS fpjzzs_my
            from t_srfx srfx
            left join t_dzyd dzyd on srfx.fdqmc = dzyd.fysmddsss and srfx.FNF = dzyd.fnf
            left join t_qcbyl qcbyl on qcbyl.fdqmc = srfx.fdqmc and srfx.FNF = qcbyl.fnf
            group by srfx.fdqmc
         )
         UNPIVOT ( FSZ for FXMMC in ( fpjyse_zzs AS '增值税平均每吨油税额',fpjyse_zzs_my AS '民营增值税平均每吨油税额',fpjyse_zzs_gy AS '国营增值税平均每吨油税额'))
    </select>

    <!-- 增值税可征收空间对比 -->
    <select id="getIncrementTaxExpropriableContrast" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData" parameterType="java.util.Map">
        <include refid="regionRollupDzydAndSrfxSql"/>
        select
            srfx.fdqmc xAxis,
            IF(ROUND( (SUM(dzyd.fyjxsemy) / (1 + #{fjzysl} / 100.0) * (#{fmysfl} / 100.0) - SUM(srfx.FHJ_ZZS_MY)) / 10000  , 2) &lt; 0,0,
               ROUND( (SUM(dzyd.fyjxsemy) / (1 + #{fjzysl} / 100.0) * (#{fmysfl} / 100.0) - SUM(srfx.FHJ_ZZS_MY)) / 10000  , 2)
            ) yAxis,
            '民营增值税可征收空间' legend,'bar' type
        from t_srfx srfx
        left join t_dzyd dzyd on srfx.fdqmc = dzyd.fysmddsss and srfx.FNF = dzyd.fnf
        left join t_qcbyl qcbyl on qcbyl.fdqmc = srfx.fdqmc and srfx.FNF = qcbyl.fnf

        group by srfx.fdqmc
    </select>

</mapper>