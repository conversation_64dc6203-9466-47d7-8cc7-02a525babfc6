<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.yjztfx.mapper.EarlyWarningDataConfigMapper">

    <!-- 获取数据源的表名配置 -->
    <select id="getDataTableNameConfigList" resultType="com.hnbp.local.yjztfx.model.DataTableNameConfig" parameterType="java.util.Map">
        select
            yj.fid fid,
            ifnull(yj.t_level,tl.level_name) levelName,
            yj.table_name tableName,
            yj.ktr_file ktrFile,
            jcxx.FBSRQ submitDate,
            (select b.fddzq from dc_bill_excelpz b where jcxx.fid =b.fzczlid ) scheduledCycle,
            ifnull(tb.faliasname,yj.table_name) aliasName
        from tb_dw_yj_yjsjpz_cfg yj
        left join t_bill_model tb on lower(tb.fheadtablename) = lower(yj.table_name) and tb.IS_DELETE = 0
        left join dc_bill_importdatatoorcl_jcxx jcxx on lower(jcxx.fbm) = lower(tb.fheadtablename)
        left join t_level tl on tl.id = tb.t_level and tl.IS_DELETE = 0
        <where>
            <if test="levelName != null and levelName != ''">
                and ifnull(yj.t_level,tl.level_name) = #{levelName}
            </if>
            <if test="type != null and type != ''">
                and yj.type = #{type}
            </if>
        </where>

        order by yj.sort

    </select>


    <!-- 表中数据 最新/最旧时间 -->
    <select id="findTableDataTime" parameterType="java.util.Map" resultType="java.util.Map">
        WITH t AS (
            SELECT
                f_sys_year,
                f_sys_month,
                f_sys_day,
                f_sys_halfyear,
                f_sys_quarter,
                <!-- &#45;&#45; 拼出真实日期，优先级：日 > 月 > 季度 > 半年 > 年 -->
                CASE
                    WHEN f_sys_day IS NOT NULL
                        THEN TO_DATE(
                            TO_CHAR(f_sys_year) || '-' ||
                            LPAD(TO_CHAR(f_sys_month), 2, '0') || '-' ||
                            LPAD(TO_CHAR(f_sys_day),   2, '0'),
                            'YYYY-MM-DD'
                             )
                    WHEN f_sys_month IS NOT NULL
                        THEN LAST_DAY(TO_DATE(
                            TO_CHAR(f_sys_year) || '-' ||
                            LPAD(TO_CHAR(f_sys_month), 2, '0'),
                            'YYYY-MM'
                                      ))
                    WHEN f_sys_quarter IS NOT NULL
                        THEN TO_DATE(
                            TO_CHAR(f_sys_year) || '-' ||
                            CASE f_sys_quarter
                                WHEN 1 THEN '03-31'
                                WHEN 2 THEN '06-30'
                                WHEN 3 THEN '09-30'
                                WHEN 4 THEN '12-31'
                                END,
                            'YYYY-MM-DD'
                             )
                    WHEN f_sys_halfyear IS NOT NULL
                        THEN TO_DATE(
                            TO_CHAR(f_sys_year) || '-' ||
                            CASE f_sys_halfyear
                                WHEN 1 THEN '06-30'
                                WHEN 2 THEN '12-31'
                                END,
                            'YYYY-MM-DD'
                             )
                    ELSE TO_DATE(TO_CHAR(f_sys_year) || '-12-31', 'YYYY-MM-DD')
                    END AS real_date
            FROM ${tableName}
        )
        SELECT
            max_sub.f_sys_year    AS maxyear,
            max_sub.f_sys_month   AS maxmonth,
            max_sub.f_sys_day     AS maxday,
            max_sub.f_sys_halfyear AS maxhalfyear,
            max_sub.f_sys_quarter AS maxquarter,
            min_sub.f_sys_year    AS minyear,
            min_sub.f_sys_month   AS minmonth,
            min_sub.f_sys_day     AS minday,
            min_sub.f_sys_halfyear AS minhalfyear,
            min_sub.f_sys_quarter AS minquarter
        FROM
            <!-- &#45;&#45; 最大 real_date 的那一行 -->
            (SELECT * FROM t ORDER BY real_date DESC FETCH FIRST 1 ROW ONLY) max_sub
                CROSS JOIN
            <!-- &#45;&#45; 最小 real_date 的那一行 -->
            (SELECT * FROM t ORDER BY real_date ASC  FETCH FIRST 1 ROW ONLY) min_sub;

    </select>

</mapper>