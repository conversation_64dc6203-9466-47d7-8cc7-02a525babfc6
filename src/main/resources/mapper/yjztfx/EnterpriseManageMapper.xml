<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.yjztfx.mapper.EnterpriseManageMapper">

    <!-- 插入数据 -->
    <insert id="insertEnterpriseInfo">
        INSERT INTO TB_DW_ZHZS_JTQYXXB_GEN (
             FID, FQYMC, FJTJC, FJTCY, FCYCJ,CREATE_TIME
        ) VALUES (
            #{fid}, #{fqymc}, #{fjtjc}, #{fjtcy}, #{fcycj},now()
        )
    </insert>

    <!-- 根据主键更新数据 -->
    <update id="updateEnterpriseInfo">
        UPDATE TB_DW_ZHZS_JTQYXXB_GEN
        SET
            UPDATE_TIME = now(),
            FQYMC = #{fqymc},
            FJTJC = #{fjtjc},
            FJTCY = #{fjtcy},
            FCYCJ = #{fcycj}
        WHERE FID = #{fid}
    </update>

    <!-- 根据主键删除数据 -->
    <delete id="deleteEnterpriseInfo">
        DELETE FROM TB_DW_ZHZS_JTQYXXB_GEN
               WHERE FID in
                 <foreach collection="fid.split(',')" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
    </delete>

    <!-- 查询所有数据 -->
    <select id="selectAllEnterpriseInfo" resultType="com.hnbp.local.yjztfx.model.EnterpriseInfo" parameterType="java.util.Map">
        SELECT * FROM TB_DW_ZHZS_JTQYXXB_GEN
        where 1=1
        <if test="fqymc != null and fqymc != ''">
            and (FQYMC like concat('%',#{fqymc},'%') or  FJTJC like concat('%',#{fqymc},'%'))
        </if>
        <if test="fjtcy != null and fjtcy != ''">
            and FJTCY like concat('%',#{fjtcy},'%')
        </if>

        order by create_time desc, CASE  WHEN FCYCJ = '集团本身' THEN 1 WHEN FCYCJ = '一级子公司' THEN 2 ELSE 4 END
    </select>

    <!-- 根据主键查询数据 -->
    <select id="selectEnterpriseInfoById" resultType="com.hnbp.local.yjztfx.model.EnterpriseInfo">
        SELECT * FROM TB_DW_ZHZS_JTQYXXB_GEN WHERE FID = #{fid}
    </select>

</mapper>