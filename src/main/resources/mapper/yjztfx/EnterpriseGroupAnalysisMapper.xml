<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.yjztfx.mapper.EnterpriseGroupAnalysisMapper">


    <!-- 企业集团明细临时表 sql -->
    <sql id="detailsBusinessListSql">
        with t_srfx_year  as (
            select
                srfx.FNSRMC,
                SUM(srfx.FSHJ) FSHJ,
                SUM(srfx.FZZS) FZZS,
                SUM(srfx.FQYSDS) FQYSDS,
                srfx.FNF
            from TB_DM_YJ_JTQYZTSS_GEN srfx
            where 1=1
            <if test="fstartdate != null and fstartdate != '' and fenddate != null and fenddate != ''">
                and fyf >= EXTRACT(MONTH FROM to_date(#{fstartdate},'YYYY-MM'))
                and fyf &lt;= EXTRACT(MONTH FROM to_date(#{fenddate},'YYYY-MM'))
            </if>
            group by srfx.FNSRMC,srfx.FNF
        ),
        t_md_details as (
            select
                *
            from TB_DW_ZHZS_JTQYXXB_GEN md
--             where FCYCJ in ('集团本身','一级子公司')
        ),
        t_md_srfx_details as (
            select
                md.FQYMC, md.FJTJC, md.FJTCY,md.FCYCJ,
                srfx.FNSRMC,
                SUM(srfx.FSHJ) FHJ_SHJ,
                SUM(srfx.FZZS) FHJ_ZZS,
                SUM(srfx.FQYSDS) FHJ_QYSDS,
                SUM(srfxqn.FSHJ) FHJQN_SHJ,
                SUM(srfxqn.FZZS) FHJQN_ZZS,
                SUM(srfxqn.FQYSDS) FHJQN_QYSDS,
                ROW_NUMBER() OVER ( PARTITION BY FQYMC, FJTJC ORDER BY CASE WHEN FCYCJ = '集团本身' THEN 0 ELSE 1 END ) AS rn,
                srfx.FNF
            from t_md_details md
            left join t_srfx_year srfx on srfx.FNSRMC = md.FJTCY
            left join t_srfx_year srfxqn on srfxqn.FNSRMC = srfx.FNSRMC and srfxqn.FNF = srfx.FNF - 1
            where 1=1
            <if test="fstartdate != null and fstartdate != '' ">
                and srfx.FNF = EXTRACT(YEAR FROM to_date(#{fstartdate},'YYYY-MM'))
            </if>

            group by md.FJTCY,md.FQYMC,md.FJTJC
        ),
         t_md_srfx_hj as (
             select
                 '合计'FQYMC,'合计'FJTJC,''FJTCY,
                 round(sum(srfx.FSHJ)/10000,2) FHJ_SHJ,
                 round(sum(srfx.FZZS)/10000,2) FHJ_ZZS,
                 round(sum(srfx.FQYSDS)/10000,2) FHJ_QYSDS,
                 round(sum(srfxqn.FSHJ)/10000,2) FHJQN_SHJ,
                 round(sum(srfxqn.FZZS)/10000,2) FHJQN_ZZS,
                 round(sum(srfxqn.FQYSDS)/10000,2) FHJQN_QYSDS,
                 round(sum(srfx.FSHJ - srfxqn.FSHJ)/10000,2) FZJE_SHJ,
                 case when sum(srfxqn.FSHJ) = 0 then 0 else round(sum(srfx.FSHJ - srfxqn.FSHJ)/sum(srfxqn.FSHJ) * 100 ,2) end FZJL_SHJ,
                 srfx.FNF
             from (select
                        distinct FJTCY
                   from t_md_details md
                   where 1=1
                   <if test="fqymc != null and fqymc != ''">
                       AND (md.FQYMC like concat('%',#{fqymc},'%') or md.FJTJC like concat('%',#{fqymc},'%'))
                   </if>
                   ) md
             left join t_srfx_year srfx on srfx.FNSRMC = md.FJTCY
             left join t_srfx_year srfxqn on srfxqn.FNSRMC = srfx.FNSRMC and srfxqn.FNF = srfx.FNF - 1
             where 1=1
             <if test="fstartdate != null and fstartdate != '' ">
                and srfx.FNF = EXTRACT(YEAR FROM to_date(#{fstartdate},'YYYY-MM'))
             </if>
         )
    </sql>


    <!-- 企业集团明细列表 -->
    <select id="getEnterpriseDetailsList" resultType="com.hnbp.local.yjztfx.model.EnterpriseGroupAnalysis" parameterType="java.util.Map">
        <include refid="detailsBusinessListSql"/>
        select
            md.FJTCY,
            md.FQYMC,
            md.FJTJC,
            md.FCYCJ,
            round(t1.FHJ_SHJ/10000,2) FHJ_SHJ,
            round(t1.FHJ_ZZS/10000,2) FHJ_ZZS,
            round(t1.FHJ_QYSDS/10000,2) FHJ_QYSDS,
            round(t1.FHJQN_SHJ/10000,2) FHJQN_SHJ,
            round(t1.FHJQN_ZZS/10000,2) FHJQN_ZZS,
            round(t1.FHJQN_QYSDS/10000,2) FHJQN_QYSDS,
            round((t1.FHJ_SHJ - t1.FHJQN_SHJ)/10000,2) FZJE_SHJ,
            case when t1.FHJQN_SHJ = 0 then 0 else round((t1.FHJ_SHJ - t1.FHJQN_SHJ)/t1.FHJQN_SHJ * 100 ,2) end FZJL_SHJ,
            case when mdjt.fqymc is not null then 1  else 0 end AS FLAG_JT,
            FNF
        from t_md_details md
        left join t_md_srfx_details t1 on t1.FQYMC = md.FQYMC and t1.FJTJC = md.FJTJC and t1.FJTCY = md.FJTCY
        left join TB_DW_ZHZS_JTQYXXB_GEN mdjt on md.FJTCY = mdjt.fqymc and mdjt.FCYCJ = '集团本身'and md.FCYCJ != '集团本身'
        where 1=1

        <if test="fqymcList != null and fqymcList.size > 0">
          AND t1.FQYMC IN
            <foreach collection="fqymcList" item="item" open="(" separator="," close=")">
              #{item}
            </foreach>
        </if>

        order by FLAG_JT desc,FHJ_SHJ desc
    </select>

    <!--企业集团分总列表 -->
    <select id="getEnterpriseGroupList" resultType="com.hnbp.local.yjztfx.model.EnterpriseGroupAnalysis" parameterType="java.util.Map">
        <include refid="detailsBusinessListSql"/>
        select * from (
            select
                t1.FQYMC,
                t1.FJTJC,
                '' FJTCY,
                round(sum(t1.FHJ_SHJ)/10000,2) FHJ_SHJ,
                round(sum(t1.FHJ_ZZS)/10000,2) FHJ_ZZS,
                round(sum(t1.FHJ_QYSDS)/10000,2) FHJ_QYSDS,
                round(sum(t1.FHJQN_SHJ)/10000,2) FHJQN_SHJ,
                round(sum(t1.FHJQN_ZZS)/10000,2) FHJQN_ZZS,
                round(sum(t1.FHJQN_QYSDS)/10000,2) FHJQN_QYSDS,
                round(sum(t1.FHJ_SHJ - t1.FHJQN_SHJ)/10000,2) FZJE_SHJ,
                case when sum(t1.FHJQN_SHJ) = 0 then 0 else round(sum(t1.FHJ_SHJ - t1.FHJQN_SHJ)/sum(t1.FHJQN_SHJ) * 100 ,2) end FZJL_SHJ,
                FNF
            from t_md_srfx_details t1
            where 1=1

            <if test="fqymc != null and fqymc != ''">
                AND (t1.FQYMC like concat('%',#{fqymc},'%') or t1.FJTJC like concat('%',#{fqymc},'%'))
            </if>

            group by t1.FQYMC,t1.FJTJC
        )
        where 1=1
        <if test="fqymc == null or fqymc == ''">
            and FQYMC in (SELECT DISTINCT F1.FQYMC FROM TB_DW_ZHZS_JTQYXXB_GEN F1
                           LEFT JOIN TB_DW_ZHZS_JTQYXXB_GEN F2 ON F1.FJTCY = F2.FJTCY AND F2.FCYCJ != '集团本身'
                           WHERE F1.FCYCJ = '集团本身' AND F2.FJTCY IS NULL) or  FQYMC = '合计'
        </if>

        order by case when FQYMC='合计' then 1 else 2 end,FHJ_SHJ desc
    </select>



</mapper>