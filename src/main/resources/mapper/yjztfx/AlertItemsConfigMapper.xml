<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.yjztfx.mapper.AlertItemsConfigMapper">

    <!-- 插入数据 -->
    <insert id="insertAlertItemsConfig">
        INSERT INTO TB_DM_YJ_SGXKZYJSZ_CFG (
             FID, FSGXKZ_FID, FYJX, FZT, FJZRQ,CREATE_TIME
        ) VALUES (
            #{fid}, #{fsgxkz_fid}, #{fyjx}, #{fzt}, #{fjzrq},now()
        )
    </insert>

    <!-- 删除数据 -->
    <delete id="deleteAlertItemsConfig">
        DELETE FROM TB_DM_YJ_SGXKZYJSZ_CFG
               WHERE 1=1
                <if test="fid != null and fid != ''" >
                    and FID in
                     <foreach collection="fid.split(',')" item="item" separator="," close=")" open="(">
                        #{item}
                    </foreach>
                </if>
                <if test="fsgxkzfid != null and fsgxkzfid != ''" >
                    and FSGXKZ_FID in
                     <foreach collection="fsgxkzfid.split(',')" item="item" separator="," close=")" open="(">
                        #{item}
                    </foreach>
                </if>
    </delete>

    <!-- 查询所有数据 -->
    <select id="selectAllAlertItemsConfig" resultType="com.hnbp.local.yjztfx.model.AlertItemsConfig" parameterType="java.util.Map">
        SELECT * FROM TB_DM_YJ_SGXKZYJSZ_CFG
        where 1=1
        and FJZRQ > now()
    </select>


</mapper>