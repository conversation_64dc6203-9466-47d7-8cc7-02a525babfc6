<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.yjztfx.mapper.GreenTaxAnalysisMapper">


    <!-- 环保税明细临时表 sql -->
    <sql id="detailsBusinessListSql">
        with
        t_md_details as (
            select
                md.FSGDW,md.FNSRMC,max(md.FNSRSBH) FNSRSBH,
                md.FNSRSSSF,md.FNSRSSSZ,md.FNSRSSQX,
                md.FNSRSDLX,md.FSFWFGS,
                row_number() over (partition by FSGDW order by if(md.FSGDW = md.FNSRMC,0,1),md.FNSRMC desc) as sgdw_rn,
                row_number() OVER (PARTITION BY FNSRMC ORDER BY if(md.FSGDW = md.FNSRMC, 0, 1), md.FSGDW DESC) AS nsrmc_rn
            from TB_DW_ZHZS_SGQYNSRYSB_GEN md
            group by md.FSGDW,md.FNSRMC
        ),
        t_sgxkz as (
            select *,
                case when EXTRACT(YEAR FROM sgxkz.FHTGQJSRQ) &lt;= EXTRACT(YEAR FROM to_date(#{fenddate},'YYYY-MM')) then 1
                    when sgxkz.FHTGQKSRQ > concat(EXTRACT(YEAR FROM to_date(#{fenddate},'YYYY-MM')),'-12-31') then 0
                    else  DATEDIFF(MONTH,sgxkz.FHTGQKSRQ,concat(EXTRACT(YEAR FROM to_date(#{fenddate},'YYYY-MM')),'-12-31'))
                              / DATEDIFF(MONTH,sgxkz.FHTGQKSRQ,sgxkz.FHTGQJSRQ)
                    end FGCJD
            from TB_DW_ZHZS_SGXKZ_GEN sgxkz
            where  sgxkz.FHTJEYZBS = '是'
        ),
        t_sgxkz_details as (
            select
                sgxkz.FSGDW,
                sum(sgxkz.FHTJGY)FHTJGY,
                sum(sgxkz.FHTJGY * sgxkz.FGCJD)FYJZYS,
                sum(sgxkz.FMJ)FMJ,
                min(sgxkz.FHTGQKSRQ)FHTGQKSRQ,
                max(sgxkz.FHTGQJSRQ)FHTGQJSRQ,
                sum(case when FXMLX = '房屋建筑类' then 0.12*1.2*sgxkz.FMJ*sgxkz.FGCJD
                        when FXMLX = '市政拆迁类' then 0.165*1.2*sgxkz.FMJ*sgxkz.FGCJD
                        else 0 end) FYJSE_HBS,
                sum(CASE
                        WHEN md.FNSRSDLX = '市内' THEN sgxkz.FHTJGY * sgxkz.FGCJD / (1 + 0.09) * 0.025
                        WHEN md.FNSRSDLX = '市外' THEN sgxkz.FHTJGY * sgxkz.FGCJD / (1 + 0.09) * 0.02
                        ELSE 0 END) FYJSE_ZZS,
                sum(CASE
                        WHEN md.FNSRSDLX = '市内' THEN sgxkz.FHTJGY * sgxkz.FGCJD / (1 + 0.09) * 0.015
                        WHEN md.FNSRSDLX = '市外' THEN sgxkz.FHTJGY * sgxkz.FGCJD / (1 + 0.09) * 0.002
                        ELSE 0 END) FYJSE_QYSDS,

                EXTRACT(YEAR FROM sgxkz.FHTGQKSRQ) FNF
            from t_sgxkz sgxkz
            LEFT JOIN t_md_details md ON sgxkz.FSGDW = md.FSGDW and md.sgdw_rn = 1
            where 1=1
              and sgxkz.fsgdw NOT LIKE '%、%'
              and sgxkz.FMJ is not null
            <if test="fstartdate != null and fstartdate != '' and fenddate != null and fenddate != ''">
              and to_date(sgxkz.FHTGQKSRQ) between to_date(#{fstartdate},'YYYY-MM') and last_day(to_date(#{fenddate},'YYYY-MM'))
            </if>

            group by sgxkz.FSGDW
        ),
        t_srfx_details as (
            select srfx.FNSRMC,srfx.FSSS,srfx.FSSDS,srfx.FSSQX,
               max(srfx.FNSRSBH)FNSRSBH,
               SUM(CASE WHEN sgxkz.FHTGQKSRQ &lt;= LAST_DAY(TO_DATE(concat(srfx.fnf,'-',srfx.fyf),'YYYY-MM')) THEN srfx.FSHJ END) AS FHJ_SHJ,
               SUM(CASE WHEN sgxkz.FHTGQKSRQ &lt;= LAST_DAY(TO_DATE(concat(srfx.fnf,'-',srfx.fyf),'YYYY-MM')) THEN srfx.FZZS END) FHJ_ZZS,
               SUM(CASE WHEN sgxkz.FHTGQKSRQ &lt;= LAST_DAY(TO_DATE(concat(srfx.fnf,'-',srfx.fyf),'YYYY-MM')) THEN srfx.FHBS END) FHJ_HBS,
               SUM(CASE WHEN sgxkz.FHTGQKSRQ &lt;= LAST_DAY(TO_DATE(concat(srfx.fnf,'-',srfx.fyf),'YYYY-MM')) THEN srfx.FQYSDS END) FHJ_QYSDS,
               srfx.FNF
            from TB_DM_YJ_HBSZTSSSJ_GEN srfx
            left join t_md_details md on srfx.FNSRMC = md.FNSRMC
            left join t_sgxkz_details sgxkz on sgxkz.FSGDW = md.FSGDW
            where 1=1
              <if test="fstartdate != null and fstartdate != '' and fenddate != null and fenddate != ''">
                and to_date(concat(srfx.fnf,'-',srfx.fyf),'YYYY-MM') between to_date(#{fstartdate},'YYYY-MM') and last_day(to_date(#{fenddate},'YYYY-MM'))
              </if>

            group by srfx.FNSRMC
        )
        ,t_sgxkz_detail_list as (
            select
                md.FSGDW,md.FNSRMC,md.FNSRSBH,
                md.FNSRSSSF,md.FNSRSSSZ,md.FNSRSSQX,
                md.FNSRSDLX,md.FSFWFGS,

                round(srfx.FHJ_SHJ,2) FHJ_SHJ,
                round(srfx.FHJ_ZZS,2) FHJ_ZZS,
                round(srfx.FHJ_HBS,2) FHJ_HBS,
                round(srfx.FHJ_QYSDS,2) FHJ_QYSDS,

                round((ifnull(sgxkz.FYJSE_HBS,0) - ifnull(srfx.FHJ_HBS,0)),2) FCE_HBS,
                round((ifnull(sgxkz.FYJSE_ZZS,0) - ifnull(srfx.FHJ_ZZS,0)),2) FCE_ZZS,
                round((ifnull(sgxkz.FYJSE_QYSDS,0) - ifnull(srfx.FHJ_QYSDS,0)),2) FCE_QYSDS,

                sgxkz.FMJ,
                TO_CHAR(sgxkz.FHTGQKSRQ, 'YYYY-MM-DD') FHTGQKSRQ,
                TO_CHAR(sgxkz.FHTGQKSRQ, 'YYYY-MM') FZXKGNY,
                TO_CHAR(sgxkz.FHTGQJSRQ, 'YYYY-MM-DD') FHTGQJSRQ,
                TO_CHAR(sgxkz.FHTGQJSRQ, 'YYYY-MM') FZWWGNY,
                round(sgxkz.FHTJGY,2) FHTJGY,
                round(sgxkz.FYJZYS,2) FYJZYS,
                round(sgxkz.FYJSE_HBS,2) FYJSE_HBS,
                round(sgxkz.FYJSE_ZZS,2) FYJSE_ZZS,
                round(sgxkz.FYJSE_QYSDS,2) FYJSE_QYSDS,

                srfx.FNF
            from t_md_details md
            left join t_sgxkz_details sgxkz on sgxkz.FSGDW = md.FSGDW and md.sgdw_rn = 1
            left join t_srfx_details srfx on srfx.FNSRMC = md.FNSRMC
            where 1=1
              and (ifnull(srfx.FHJ_ZZS,0) + ifnull(srfx.FHJ_HBS,0) + ifnull(srfx.FHJ_QYSDS,0) + ifnull(sgxkz.FMJ,0)) > 0
            <if test="fsgdw != null and fsgdw != '' ">
              AND md.FSGDW like concat('%',#{fsgdw},'%')
            </if>
            <if test="fnsrmc != null and fnsrmc != '' ">
              AND md.FNSRMC like concat('%',#{fnsrmc},'%')
            </if>
            <if test="fnsrsbh != null and fnsrsbh != '' ">
              AND md.FNSRSBH = #{fnsrsbh}
            </if>

            <!-- <if test="filter == 'wjsj' "> -->
            <!--   AND round((ifnull(sgxkz.FYJSE_HBS,0) - ifnull(srfx.FHJ_HBS,0)),2) >0 -->
            <!-- </if> -->
            <!-- <if test="filter == 'wjsjl' "> -->
            <!--   AND md.FNSRSDLX = '市外' and md.FNSRMC in (select distinct FNSRMC from TB_DM_YJ_HBSZTSSSJ_GEN where FSHJ is null  and fzzs is null and fhbs is null and FQYSDS is null) -->
            <!-- </if> -->
        )
    </sql>


    <!-- 施工许可证明细列表 -->
    <select id="getConstructionPermitsDetailsList" resultType="java.util.Map" parameterType="java.util.Map">
        <include refid="detailsBusinessListSql"/>
        select
            t1.FSGDW FSGDW,
            t1.FNSRMC  FNSRMC,
            t1.FNSRSBH  FNSRSBH,
            t1.FNSRSSSF  FNSRSSSF,
            t1.FNSRSSSZ  FNSRSSSZ,
            t1.FNSRSSQX  FNSRSSQX,
            t1.FNSRSDLX  FNSRSDLX,
            t1.FSFWFGS  FSFWFGS,
            t1.FZXKGNY  FZXKGNY,
            t1.FZWWGNY  FZWWGNY,
            round(t1.FHJ_SHJ/10000,2) FHJ_SHJ,
            round(t1.FHJ_ZZS/10000,2) FHJ_ZZS,
            round(t1.FHJ_HBS/10000,2) FHJ_HBS,
            round(t1.FHJ_QYSDS/10000,2) FHJ_QYSDS,
            round(t1.FCE_HBS/10000,2) FCE_HBS,
            round(t1.FCE_ZZS/10000,2) FCE_ZZS,
            round(t1.FCE_QYSDS/10000,2) FCE_QYSDS,
            round(t1.FMJ,2) FMJ,
            round(t1.FHTJGY/10000,2) FHTJGY,
            round(t1.FYJZYS/10000,2) FYJZYS,
            round(t1.FYJSE_HBS/10000,2) FYJSE_HBS,
            round(t1.FYJSE_ZZS/10000,2) FYJSE_ZZS,
            round(t1.FYJSE_QYSDS/10000,2) FYJSE_QYSDS,
            FNF
        from t_sgxkz_detail_list t1
        where 1=1

        <if test="fsgdwList != null and fsgdwList.size > 0">
          AND FSGDW IN
            <foreach collection="fsgdwList" item="item" open="(" separator="," close=")">
              #{item}
            </foreach>
        </if>

        order by FHTJGY desc
    </select>

    <!-- 施工许可证分总列表 -->
    <select id="getConstructionPermitsTotalList" resultType="java.util.Map" parameterType="java.util.Map">
        <include refid="detailsBusinessListSql"/>
        ,t_rollupList as (
            select
                case when GROUPING(t1.FSGDW)=1 then '合计' else t1.FSGDW end FSGDW,
                case when GROUPING(t1.FSGDW)!=1 then '' else '' end FNSRMC,
                case when GROUPING(t1.FSGDW)!=1 then '' else '' end FNSRSBH,
                case when GROUPING(t1.FSGDW)!=1 then t1.FNSRSSSF else '' end FNSRSSSF,
                case when GROUPING(t1.FSGDW)!=1 then t1.FNSRSSSZ else '' end FNSRSSSZ,
                case when GROUPING(t1.FSGDW)!=1 then t1.FNSRSSQX else '' end FNSRSSQX,
                case when GROUPING(t1.FSGDW)!=1 then MAX(CASE WHEN t1.FSGDW = t1.FNSRMC THEN t1.FNSRSDLX END) else '' end FNSRSDLX,
                case when GROUPING(t1.FSGDW)!=1 then t1.FSFWFGS else '' end FSFWFGS,
                case when GROUPING(t1.FSGDW)!=1 then min(t1.FZXKGNY) else '' end FZXKGNY,
                case when GROUPING(t1.FSGDW)!=1 then min(t1.FZWWGNY) else '' end FZWWGNY,
                case when GROUPING(t1.FSGDW)!=1 then case when count(distinct t1.FNSRMC)>1 then '是' else '否' end else '' end FSFHFGSJS,
                round(sum(t1.FHJ_SHJ/10000),2) FHJ_SHJ,
                round(sum(t1.FHJ_ZZS/10000),2) FHJ_ZZS,
                round(sum(t1.FHJ_HBS/10000),2) FHJ_HBS,
                round(sum(t1.FHJ_QYSDS/10000),2) FHJ_QYSDS,
                round(sum(t1.FCE_HBS/10000),2) FCE_HBS,
                round(sum(t1.FCE_ZZS/10000),2) FCE_ZZS,
                round(sum(t1.FCE_QYSDS/10000),2) FCE_QYSDS,
                round(sum(t1.FCE_HBS),2) FCE_HBS_Y,
                round(sum(t1.FCE_ZZS),2) FCE_ZZS_Y,
                round(sum(t1.FCE_QYSDS),2) FCE_QYSDS_Y,
                round(sum(t1.FMJ),2) FMJ,
                round(sum(t1.FHTJGY/10000),2) FHTJGY,
                round(sum(t1.FYJZYS/10000),2) FYJZYS,
                round(sum(t1.FYJSE_HBS/10000),2) FYJSE_HBS,
                round(sum(t1.FYJSE_ZZS/10000),2) FYJSE_ZZS,
                round(sum(t1.FYJSE_QYSDS/10000),2) FYJSE_QYSDS,
                FNF
            from t_sgxkz_detail_list t1
            where 1=1
            group by grouping sets ((t1.FSGDW))
            having FMJ is not null
        ),
        t_wjnqyList as (
            select
                md.FSGDW,count(md.FNSRMC) mdjnsl,count(t2.FNSRMC) ssjnsl
            from TB_DW_ZHZS_SGQYNSRYSB_GEN md
            left join (select distinct FNSRMC from TB_DM_YJ_HBSZTSSSJ_GEN where FSHJ is not null) t2 on t2.FNSRMC = md.FNSRMC
            group by md.FSGDW
            having ssjnsl = 0
        )
        select
            case when GROUPING(t1.FSGDW)=1 then '合计' else t1.FSGDW end FSGDW,
            case when GROUPING(t1.FSGDW)!=1 then '' else '' end FNSRMC,
            case when GROUPING(t1.FSGDW)!=1 then '' else '' end FNSRSBH,
            case when GROUPING(t1.FSGDW)!=1 then t1.FNSRSSSF else '' end FNSRSSSF,
            case when GROUPING(t1.FSGDW)!=1 then t1.FNSRSSSZ else '' end FNSRSSSZ,
            case when GROUPING(t1.FSGDW)!=1 then t1.FNSRSSQX else '' end FNSRSSQX,
            case when GROUPING(t1.FSGDW)!=1 then t1.FNSRSDLX else '' end FNSRSDLX,
            case when GROUPING(t1.FSGDW)!=1 then t1.FSFWFGS else '' end FSFWFGS,
            case when GROUPING(t1.FSGDW)!=1 then min(t1.FZXKGNY) else '' end FZXKGNY,
            case when GROUPING(t1.FSGDW)!=1 then min(t1.FZWWGNY) else '' end FZWWGNY,
            case when GROUPING(t1.FSGDW)!=1 then case when count(distinct t1.FNSRMC)>1 then '是' else '否' end else '' end FSFHFGSJS,
            round(sum(t1.FHJ_SHJ),2) FHJ_SHJ,
            round(sum(t1.FHJ_ZZS),2) FHJ_ZZS,
            round(sum(t1.FHJ_HBS),2) FHJ_HBS,
            round(sum(t1.FHJ_QYSDS),2) FHJ_QYSDS,
            round(sum(t1.FMJ),2) FMJ,
            round(sum(t1.FCE_HBS),2) FCE_HBS,
            round(sum(t1.FCE_ZZS),2) FCE_ZZS,
            round(sum(t1.FCE_QYSDS),2) FCE_QYSDS,
            round(sum(t1.FHTJGY),2) FHTJGY,
            round(sum(t1.FYJZYS),2) FYJZYS,
            round(sum(t1.FYJSE_HBS),2) FYJSE_HBS,
            round(sum(t1.FYJSE_ZZS),2) FYJSE_ZZS,
            round(sum(t1.FYJSE_QYSDS),2) FYJSE_QYSDS,
            FNF
        from t_rollupList t1
        left join t_wjnqyList t2 on t2.FSGDW = t1.FSGDW
        where 1=1

        <if test="fnsrsdlx != null and fnsrsdlx != ''">
          AND t1.FNSRSDLX = #{fnsrsdlx}
        </if>

        <if test="filter == 'hbsfx' ">
          AND t1.FCE_HBS_Y > 0
        </if>

        <if test="filter == 'zzsfx' ">
          AND t1.FCE_ZZS_Y > 0
        </if>

        <if test="filter == 'qysdsfx' ">
          AND t1.FCE_QYSDS_Y > 0
        </if>
        <if test="filter == 'wjsjl' ">
          AND t1.FNSRSDLX = '市外' and t2.FSGDW is not null
        </if>
        group by grouping sets ((t1.FSGDW),())

        order by case when FSGDW='合计' then 1 else 2 end,FHTJGY desc
    </select>

    <!-- 分属地类型施工企业施工及环保税缴纳情况 -->
    <select id="getTerritorialConstructionThings" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData" parameterType="java.util.Map">
        <include refid="detailsBusinessListSql"/>
        ,t_rollupList as (
            select
                case when GROUPING(t1.FSGDW)=1 then '合计' else t1.FSGDW end FSGDW,
                case when GROUPING(t1.FSGDW)!=1 then '' else '' end FNSRMC,
                case when GROUPING(t1.FSGDW)!=1 then '' else '' end FNSRSBH,
                case when GROUPING(t1.FSGDW)!=1 then t1.FNSRSSSF else '' end FNSRSSSF,
                case when GROUPING(t1.FSGDW)!=1 then t1.FNSRSSSZ else '' end FNSRSSSZ,
                case when GROUPING(t1.FSGDW)!=1 then t1.FNSRSSQX else '' end FNSRSSQX,
                case when GROUPING(t1.FSGDW)!=1 then t1.FNSRSDLX else '' end FNSRSDLX,
                case when GROUPING(t1.FSGDW)!=1 then t1.FSFWFGS else '' end FSFWFGS,
                case when GROUPING(t1.FSGDW)!=1 then min(t1.FZXKGNY) else '' end FZXKGNY,
                case when GROUPING(t1.FSGDW)!=1 then min(t1.FZWWGNY) else '' end FZWWGNY,
                case when GROUPING(t1.FSGDW)!=1 then case when count(distinct t1.FNSRMC)>1 then '是' else '否' end else '' end FSFHFGSJS,
                round(sum(t1.FHJ_SHJ/10000),2) FHJ_SHJ,
                round(sum(t1.FHJ_ZZS/10000),2) FHJ_ZZS,
                round(sum(t1.FHJ_HBS/10000),2) FHJ_HBS,
                round(sum(t1.FHJ_QYSDS/10000),2) FHJ_QYSDS,
                round(sum(t1.FCE_HBS/10000),2) FCE_HBS,
                round(sum(t1.FCE_ZZS/10000),2) FCE_ZZS,
                round(sum(t1.FCE_QYSDS/10000),2) FCE_QYSDS,
                round(sum(t1.FCE_HBS),2) FCE_HBS_Y,
                round(sum(t1.FCE_ZZS),2) FCE_ZZS_Y,
                round(sum(t1.FCE_QYSDS),2) FCE_QYSDS_Y,
                round(sum(t1.FMJ),2) FMJ,
                round(sum(t1.FHTJGY/10000),2) FHTJGY,
                round(sum(t1.FYJZYS/10000),2) FYJZYS,
                round(sum(t1.FYJSE_HBS/10000),2) FYJSE_HBS,
                round(sum(t1.FYJSE_ZZS/10000),2) FYJSE_ZZS,
                round(sum(t1.FYJSE_QYSDS/10000),2) FYJSE_QYSDS,
                FNF
            from t_sgxkz_detail_list t1
            where 1=1
            group by grouping sets ((t1.FSGDW))
            having FMJ is not null
        )
        select FXMMC xAxis,FSZ yAxis, concat(FNSRSDLX,'施工企业') legend, 'bar' type
        from (
            select
                t1.FNSRSDLX,
                count(distinct t1.FSGDW) FSGDW_SL,
                count(distinct t1.FNSRMC) FNSRMC_SL,
                round(sum(t1.FHJ_SHJ),2) FHJ_SHJ,
                round(sum(t1.FHJ_ZZS),2) FHJ_ZZS,
                round(sum(t1.FHJ_HBS),2) FHJ_HBS,
                round(sum(t1.FHJ_QYSDS),2) FHJ_QYSDS,
                round(sum(t1.FCE_HBS),2) FCE_HBS,
                round(sum(t1.FCE_ZZS),2) FCE_ZZS,
                round(sum(t1.FCE_QYSDS),2) FCE_QYSDS,
                round(sum(t1.FMJ)/10000,2) FMJ,
                round(sum(t1.FHTJGY)/10000,2) FHTJGY,
                round(sum(t1.FYJZYS)/10000,2) FYJZYS,
                round(sum(t1.FYJSE_HBS),2) FYJSE_HBS,
                round(sum(t1.FYJSE_ZZS),2) FYJSE_ZZS,
                round(sum(t1.FYJSE_QYSDS),2) FYJSE_QYSDS
            from t_rollupList t1
            where 1=1
              AND FCE_HBS_Y > 0
            group by t1.FNSRSDLX
         )
         UNPIVOT ( FSZ for FXMMC in ( FSGDW_SL AS '企业数量',FHTJGY AS '合同价格(亿元)',FMJ AS '面积(万㎡)',FYJSE_HBS AS '环保税测算金额(万元)',FHJ_HBS AS '环保税实缴金额(万元)'))
    </select>


    <!-- 项目营收分析 -->
    <select id="getProjectRevenueList" resultType="java.util.Map" parameterType="java.util.Map">
        <include refid="detailsBusinessListSql"/>
        SELECT
            CASE WHEN GROUPING(sgxkz.FSGDW)=1 THEN '合计' ELSE sgxkz.FSGDW END   AS FSGDW,
            CASE WHEN GROUPING(sgxkz.FGCMC)=1 THEN NULL   ELSE sgxkz.FGCMC END   AS FGCMC,
            CASE WHEN GROUPING(sgxkz.FJSDW)=1 THEN NULL   ELSE sgxkz.FJSDW END   AS FJSDW,
            CASE WHEN GROUPING(sgxkz.FJSDW)=1 THEN NULL   ELSE ROUND(sgxkz.FGCJD*100,2) END AS FGCJD,

            ROUND( SUM(sgxkz.FHTJGY) / 10000 , 2 ) AS FHTJG,
            ROUND( SUM(sgxkz.FHTJGY * sgxkz.FGCJD) / 10000 , 2 ) AS FYJZYS,
            ROUND( SUM(sgxkz.FMJ) , 2 ) AS FMJ,

            MIN(sgxkz.FHTGQKSRQ) AS FHTGQKSRQ,
            MAX(sgxkz.FHTGQJSRQ) AS FHTGQJSRQ,
            MAX(sgxkz.FFZRQ) AS FFZRQ

        FROM t_sgxkz sgxkz
        where 1=1 and sgxkz.FMJ > 0
        <if test="fsgdw != null and fsgdw != ''">
            and sgxkz.FSGDW like  concat('%',#{fsgdw},'%')
        </if>
        <if test="fjsdw != null and fjsdw != ''">
            and sgxkz.FJSDW like  concat('%',#{fjsdw},'%')
        </if>
        <if test="fgcmc != null and fgcmc != ''">
            and sgxkz.FGCMC like  concat('%',#{fgcmc},'%')
        </if>
        <if test="fgcjd_start != null and fgcjd_start != ''">
            and sgxkz.fgcjd >= #{fgcjd_start} and sgxkz.fgcjd &lt;= #{fgcjd_end}
        </if>
        <if test="fstartdate != null and fstartdate != ''">
            and to_date(sgxkz.FHTGQKSRQ) between to_date(#{fstartdate},'YYYY-MM') and last_day(to_date(#{fenddate},'YYYY-MM'))
        </if>
        <if test="fhtgqjsrq_start != null and fhtgqjsrq_start != ''">
            and to_date(sgxkz.FHTGQJSRQ) between to_date(#{fhtgqjsrq_start},'YYYY-MM') and last_day(to_date(#{fhtgqjsrq_end},'YYYY-MM'))
        </if>
        <if test="ffzrq_start != null and ffzrq_start != ''">
            and to_date(sgxkz.FFZRQ) between to_date(#{ffzrq_start},'YYYY-MM') and last_day(to_date(#{ffzrq_end},'YYYY-MM'))
        </if>


        GROUP BY GROUPING SETS ( (sgxkz.FSGDW, sgxkz.FGCMC, sgxkz.FJSDW,sgxkz.FFZRQ), ( ) )

        ORDER BY case when FSGDW='合计' then 1 else 2 end, FHTJG desc

    </select>

    <!-- 项目进度分析 -->
    <select id="getProjectProgressList" resultType="java.util.Map" parameterType="java.util.Map">
        <include refid="detailsBusinessListSql"/>
        ,t_srfx_gc_details as (
            select
               sgxkz.FSGDW, sgxkz.FGCMC, sgxkz.FJSDW,
               sgxkz.FFZRQ,sgxkz.FHTGQKSRQ,sgxkz.FHTGQJSRQ,sgxkz.FHTJGY,
               SUM(CASE WHEN sgxkz.FHTGQKSRQ &lt;= LAST_DAY(TO_DATE(concat(srfx.fnf,'-',srfx.fyf),'YYYY-MM')) THEN srfx.FSHJ END) AS FHJ_SHJ,
               SUM(CASE WHEN sgxkz.FHTGQKSRQ &lt;= LAST_DAY(TO_DATE(concat(srfx.fnf,'-',srfx.fyf),'YYYY-MM')) THEN srfx.FZZS END) FHJ_ZZS,
               SUM(CASE WHEN sgxkz.FHTGQKSRQ &lt;= LAST_DAY(TO_DATE(concat(srfx.fnf,'-',srfx.fyf),'YYYY-MM')) THEN srfx.FHBS END) FHJ_HBS,
               SUM(CASE WHEN sgxkz.FHTGQKSRQ &lt;= LAST_DAY(TO_DATE(concat(srfx.fnf,'-',srfx.fyf),'YYYY-MM')) THEN srfx.FQYSDS END) FHJ_QYSDS,
               SUM(CASE WHEN ADD_MONTHS(sgxkz.FHTGQKSRQ, -6) &lt;= LAST_DAY(TO_DATE(concat(srfx.fnf,'-',srfx.fyf),'YYYY-MM')) THEN srfx.FYHS END) FHJ_YHS
            from t_sgxkz sgxkz
            left join t_md_details md on md.FNSRMC = sgxkz.FSGDW
            left join TB_DM_YJ_HBSZTSSSJ_GEN srfx on srfx.FNSRMC = md.FSGDW
            where 1 = 1
              <if test="fstartdate != null and fstartdate != '' and fenddate != null and fenddate != ''">
                and to_date(concat(srfx.fnf,'-',srfx.fyf),'YYYY-MM') between ADD_MONTHS(to_date(#{fstartdate},'YYYY-MM'), -6) and last_day(to_date(#{fenddate},'YYYY-MM'))
              </if>
            group by sgxkz.FSGDW, sgxkz.FGCMC, sgxkz.FJSDW,sgxkz.FFZRQ )
        ,t_sgxkz_gc_details as (
            SELECT
                sgxkz.FID,
                sgxkz.FSGDW,
                sgxkz.FGCMC,
                sgxkz.FJSDW,
                sgxkz.FHTJGY,
                sgxkz.FGCJD,
                sgxkz.FMJ,

                CASE WHEN sgxkz.FXMLX = '房屋建筑类' THEN 0.12  * 1.2 * sgxkz.FMJ * sgxkz.FGCJD
                    WHEN sgxkz.FXMLX = '市政拆迁类' THEN 0.165 * 1.2 * sgxkz.FMJ * sgxkz.FGCJD
                    ELSE 0 END AS FYJSE_HBS ,
                CASE WHEN md.FNSRSDLX = '市内' THEN sgxkz.FHTJGY * sgxkz.FGCJD / 1.09 * 0.025
                    WHEN md.FNSRSDLX = '市外' THEN sgxkz.FHTJGY * sgxkz.FGCJD / 1.09 * 0.020
                    ELSE 0 END AS FYJSE_ZZS ,
                CASE WHEN md.FNSRSDLX = '市内' THEN sgxkz.FHTJGY * sgxkz.FGCJD / 1.09 * 0.015
                    WHEN md.FNSRSDLX = '市外' THEN sgxkz.FHTJGY * sgxkz.FGCJD / 1.09 * 0.002
                    ELSE 0 END AS FYJSE_QYSDS,
                sgxkz.FHTJGY * 0.0003 AS FYJSE_YHS,

                sgxkz.FHTGQKSRQ,
                sgxkz.FHTGQJSRQ,
                sgxkz.FFZRQ

            FROM t_sgxkz sgxkz
            LEFT JOIN t_md_details md ON sgxkz.FSGDW = md.FSGDW and md.sgdw_rn = 1
            where 1=1
            group by sgxkz.FSGDW, sgxkz.FGCMC, sgxkz.FJSDW,sgxkz.FFZRQ
        )
        SELECT
            sgxkz.FID,sgxkz.FSGDW, sgxkz.FGCMC, sgxkz.FJSDW,
            sgxkz.FHTJGY, sgxkz.FMJ,
            ROUND(sgxkz.FGCJD*100,2) FGCJD,
            CASE WHEN sgxkz.FYJSE_HBS = 0 OR sgxkz.FYJSE_HBS IS NULL THEN 0
                WHEN srfx.FHJ_HBS = 0 OR srfx.FHJ_HBS IS NULL THEN -100
                ELSE ROUND( (srfx.FHJ_HBS - sgxkz.FYJSE_HBS) / sgxkz.FYJSE_HBS * 100, 2 )END FPLZ_HBS,
            CASE WHEN sgxkz.FYJSE_ZZS = 0 OR sgxkz.FYJSE_ZZS IS NULL THEN 0
                WHEN srfx.FHJ_ZZS = 0 OR srfx.FHJ_ZZS IS NULL THEN -100
                ELSE ROUND( (srfx.FHJ_ZZS - sgxkz.FYJSE_ZZS) / sgxkz.FYJSE_ZZS * 100, 2 )END FPLZ_ZZS,
            CASE WHEN sgxkz.FYJSE_QYSDS = 0 OR sgxkz.FYJSE_QYSDS IS NULL THEN 0
                WHEN srfx.FHJ_QYSDS = 0 OR srfx.FHJ_QYSDS IS NULL THEN -100
                ELSE ROUND( (srfx.FHJ_QYSDS - sgxkz.FYJSE_QYSDS) / sgxkz.FYJSE_QYSDS * 100, 2 )END FPLZ_QYSDS,
            CASE WHEN sgxkz.FYJSE_YHS = 0 OR sgxkz.FYJSE_YHS IS NULL THEN 0
                WHEN srfx.FHJ_YHS = 0 OR srfx.FHJ_YHS IS NULL THEN -100
                ELSE ROUND( (srfx.FHJ_YHS - sgxkz.FYJSE_YHS) / sgxkz.FYJSE_YHS * 100, 2 )END FPLZ_YHS,

            CASE WHEN sgxkz.FHTGQKSRQ >= last_day(to_date(#{fenddate},'YYYY-MM')) THEN '未到缴税时间'
                WHEN sgxkz.FYJSE_HBS > ifnull(srfx.FHJ_HBS,0) THEN '未按规定缴税'
                ELSE '正常缴税' END AS FZT_HBS,
            CASE WHEN sgxkz.FHTGQKSRQ >= last_day(to_date(#{fenddate},'YYYY-MM')) THEN '未到缴税时间'
                WHEN sgxkz.FYJSE_ZZS > ifnull(srfx.FHJ_ZZS,0) THEN '未按规定缴税'
                ELSE '正常缴税' END AS FZT_ZZS,
            CASE WHEN sgxkz.FHTGQKSRQ >= last_day(to_date(#{fenddate},'YYYY-MM')) THEN '未到缴税时间'
                WHEN sgxkz.FYJSE_QYSDS > ifnull(srfx.FHJ_QYSDS,0) THEN '未按规定缴税'
                ELSE '正常缴税' END AS FZT_QYSDS,
            CASE WHEN sgxkz.FHTGQKSRQ >= last_day(to_date(#{fenddate},'YYYY-MM')) THEN '未到缴税时间'
                WHEN sgxkz.FYJSE_YHS > ifnull(srfx.FHJ_YHS,0) THEN '未按规定缴税'
                ELSE '正常缴税' END AS FZT_YHS,

            sgxkz.FHTGQKSRQ, sgxkz.FHTGQJSRQ, sgxkz.FFZRQ
        FROM t_sgxkz_gc_details sgxkz
        LEFT JOIN t_srfx_gc_details srfx ON srfx.FSGDW = sgxkz.FSGDW and srfx.FGCMC = sgxkz.FGCMC
                                         and srfx.FJSDW = sgxkz.FJSDW and srfx.FFZRQ = sgxkz.FFZRQ
        where 1=1 and sgxkz.FMJ > 0
        <if test="fsgdw != null and fsgdw != ''">
            and sgxkz.FSGDW like  concat('%',#{fsgdw},'%')
        </if>
        <if test="fjsdw != null and fjsdw != ''">
            and sgxkz.FJSDW like  concat('%',#{fjsdw},'%')
        </if>
        <if test="fgcmc != null and fgcmc != ''">
            and sgxkz.FGCMC like  concat('%',#{fgcmc},'%')
        </if>
        <if test="fgcjd_start != null and fgcjd_start != ''">
            and sgxkz.fgcjd >= #{fgcjd_start} and sgxkz.fgcjd &lt;= #{fgcjd_end}
        </if>
        <if test="fstartdate != null and fstartdate != ''">
            and to_date(sgxkz.FHTGQKSRQ) between to_date(#{fstartdate},'YYYY-MM') and last_day(to_date(#{fenddate},'YYYY-MM'))
        </if>
        <if test="fhtgqjsrq_start != null and fhtgqjsrq_start != ''">
            and to_date(sgxkz.FHTGQJSRQ) between to_date(#{fhtgqjsrq_start},'YYYY-MM') and last_day(to_date(#{fhtgqjsrq_end},'YYYY-MM'))
        </if>
        <if test="ffzrq_start != null and ffzrq_start != ''">
            and to_date(sgxkz.FFZRQ) between to_date(#{ffzrq_start},'YYYY-MM') and last_day(to_date(#{ffzrq_end},'YYYY-MM'))
        </if>


        order by FPLZ_ZZS desc

    </select>

</mapper>