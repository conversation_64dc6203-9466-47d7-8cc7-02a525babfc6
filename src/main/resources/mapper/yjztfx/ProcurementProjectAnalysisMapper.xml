<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.yjztfx.mapper.GreenTaxAnalysisMapper">

    <select id="overview" resultType="java.util.Map" parameterType="java.util.Map">
        WITH
        zcxx AS (
            SELECT
                FCGZBDW
                , COUNT(1) AS FXMSL
                , SUM(FCJZBJ) AS FCJZBJ
                , MIN(FZBCJJGGSFBSJ) FZBCJJGGSFBSJ_MIN
                , MAX(FZBCJJGGSFBSJ) FZBCJJGGSFBSJ_MAX
            FROM TB_DW_ZHZS_CGXMJYMX_GEN
            <where>
                <if test="fzbgssj != null and fzbgssj != ''">
                    AND FZBCJJGGSFBSJ BETWEEN TO_DATE('2025-01-01', 'YYYY-MM-DD') AND TO_DATE('2025-06-30', 'YYYY-MM-DD')
                </if>
                <if test="fzcdwmc != null and fzcdwmc != ''">
                    AND FCGZBDW LIKE CONCAT('%', #{fzcdwmc}, '%')
                </if>
                <if test="fzbdwmc != null and fzbdwmc != ''">
                    AND FZBDW LIKE CONCAT('%', #{fzbdwmc}, '%')
                </if>
            </where>
            GROUP BY FCGZBDW
        )
        , ss AS (
            SELECT
                zcxx.FCGZBDW
                , SUM(FYHS) FHJ_YHS
                , MAX(rel.FNSRMC) FNSRMC
                , COALESCE(MAX(rel.FNSRSBH), MAX(srfx.FNSRSBH)) FNSRSBH
            FROM zcxx
            LEFT JOIN TB_DW_ZHZS_ZCZBDWNSRYSB_GEN rel ON zcxx.FCGZBDW = rel.FCGZBZBDW
            LEFT JOIN TB_DW_SRFX_SRFX_MAIN srfx ON rel.FNSRMC=srfx.FNSRMC
                                                AND srfx.FSKSSQQ BETWEEN TRUNC(zcxx.FZBCJJGGSFBSJ_MIN, 'MM') AND ADD_MONTHS(zcxx.FZBCJJGGSFBSJ_MAX, COALESCE(#{fsszhzq}, 3))
                                                AND srfx.FZSXM='印花税' AND srfx.FZSPM != '滞纳金'
            GROUP BY zcxx.FCGZBDW
        )
        SELECT
            GROUPING(FCGZBDW) fgrouping
            , CASE WHEN GROUPING(FCGZBDW)=1 THEN '合计' ELSE FCGZBDW END FCGZBDW
            , CASE WHEN GROUPING(FCGZBDW)=1 THEN '/' ELSE FNSRMC END FNSRMC
            , CASE WHEN GROUPING(FCGZBDW)=1 THEN '/' ELSE FNSRSBH END FNSRSBH
            , SUM(FXMSL) FXMSL
            , ROUND(SUM(FCJZBJ)/10000, 2) FCJZBJ
            , CASE WHEN GROUPING(FCGZBDW)=1 THEN NULL ELSE MIN(FZBCJJGGSFBSJ_MIN) END FZBCJJGGSFBSJ_MIN
            , CASE WHEN GROUPING(FCGZBDW)=1 THEN NULL ELSE MAX(FZBCJJGGSFBSJ_MAX) END FZBCJJGGSFBSJ_MAX
            , ROUND(SUM(FHJ_YHS)/10000, 2) FHJ_YHS
            , ROUND(SUM(FCS_YHS)/10000, 2) FCS_YHS
            , ROUND(SUM(FCE_YHS)/10000, 2) FCE_YHS
        FROM (
            SELECT zcxx.FCGZBDW, zcxx.FXMSL, zcxx.FCJZBJ, zcxx.FZBCJJGGSFBSJ_MIN, zcxx.FZBCJJGGSFBSJ_MAX
                , COALESCE(ss.FHJ_YHS, 0) FHJ_YHS, ss.FNSRSBH, ss.FNSRMC
                , COALESCE(zcxx.FCJZBJ * :fyhsSl, 0) AS FCS_YHS
                , COALESCE(zcxx.FCJZBJ * :fyhsSl, 0) - COALESCE(ss.FHJ_YHS， 0) AS FCE_YHS
            FROM zcxx LEFT JOIN ss ON zcxx.FCGZBDW=ss.FCGZBDW
        ) GROUP BY ROLLUP(FCGZBDW)
        ORDER BY fgrouping DESC, FCJZBJ DESC
    </select>

</mapper>