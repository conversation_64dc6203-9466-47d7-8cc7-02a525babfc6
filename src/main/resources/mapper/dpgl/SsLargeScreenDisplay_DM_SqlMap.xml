<?xml version="1.0" encoding="UTF-8"?><!--

    Copyright 2004-2024 the original author or authors.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

--><!--Converted at: Tue Oct 29 11:00:55 CST 2024-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.dpgl.mapper.SsLargeScreenDisplayMapper">

    <!-- 税收按省/地/区级 数据情况 -->
    <select id="getTaxDataByLevel" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
        sum(FHJ) FHJ,sum(FZY) FZY,sum(FDFSJ) FDFSJ,sum(FDFSIJ) FDFSIJ,sum(FDFXIJ) FDFXIJ, sum(FDFJKJ) FDFJKJ
        from tb_dm_zhzs_ssdp_gen
        where  1=1
        and fzsxm = '税收收入合计'
        and fsjly = '税务汇总入库税收收入表'
        <if test="fyear != null and fyear != ''">AND
            fnf = #{fyear}
        </if>
        <if test="fmonth != null and fmonth != ''">AND
            fyf = #{fmonth}
        </if>

    </select>

    <!-- 年度数据 按 省/地/区级 数据情况 -->
    <select id="getYearDataByLevel" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
        fnf,FHJ,FZY,FDFSJ,FDFSIJ,FDFXIJ,FDFJKJ
        from(
            select
            fnf,sum(FHJ) FHJ,sum(FZY) FZY,sum(FDFSJ) FDFSJ,sum(FDFSIJ) FDFSIJ,sum(FDFXIJ) FDFXIJ,SUM(FDFJKJ) FDFJKJ
            from tb_dm_zhzs_ssdp_gen
            where  1=1
            and fzsxm = '税收收入合计'
            and fsjly = '税务汇总入库税收收入表'
            <if test="fyear != null and fyear != ''">AND
                fnf &lt;= #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">AND
                fyf = #{fmonth}
            </if>
            group by fnf
            order by fnf desc
            limit #{limit}
        ) order by fnf asc
    </select>

    <!-- 月度数据 按 省/地/区级 数据情况 -->
    <select id="getMonthDataByLevel" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
            CONCAT(fnf, '-', fyf) AS xAxis,
            fnf, fyf,
            FHJ - IFNULL(prev_FHJ, 0) AS FHJ,
            (FHJ - IFNULL(prev_FHJ, 0)) - (FZY - IFNULL(prev_FZY, 0)) AS FDFKJ,
            FZY - IFNULL(prev_FZY, 0) AS FZY,
            FDFSJ - IFNULL(prev_FDFSJ, 0) AS FDFSJ,
            FDFSIJ - IFNULL(prev_FDFSIJ, 0) AS FDFSIJ,
            FDFXIJ - IFNULL(prev_FDFXIJ, 0) AS FDFXIJ,
            FDFJKJ - IFNULL(prev_FDFJKJ, 0) AS FDFJKJ
        FROM (
            SELECT
                fnf, fyf,
                SUM(FHJ) AS FHJ,
                SUM(FZY) AS FZY,
                SUM(FDFSJ) AS FDFSJ,
                SUM(FDFSIJ) AS FDFSIJ,
                SUM(FDFXIJ) AS FDFXIJ,
                SUM(FDFJKJ) AS FDFJKJ,
                LAG(SUM(FHJ)) OVER (PARTITION BY fnf ORDER BY fyf) AS prev_FHJ,
                LAG(SUM(FZY)) OVER (PARTITION BY fnf ORDER BY fyf) AS prev_FZY,
                LAG(SUM(FDFSJ)) OVER (PARTITION BY fnf ORDER BY fyf) AS prev_FDFSJ,
                LAG(SUM(FDFSIJ)) OVER (PARTITION BY fnf ORDER BY fyf) AS prev_FDFSIJ,
                LAG(SUM(FDFXIJ)) OVER (PARTITION BY fnf ORDER BY fyf) AS prev_FDFXIJ,
                LAG(SUM(FDFJKJ)) OVER (PARTITION BY fnf ORDER BY fyf) AS prev_FDFJKJ
            FROM tb_dm_zhzs_ssdp_gen
            WHERE
                fzsxm = '税收收入合计'
                AND fsjly = '税务汇总入库税收收入表'
                <if test="fyear != null and fyear != ''">
                    AND fnf = #{fyear}
                </if>
            GROUP BY fnf, fyf
        ) t
        ORDER BY fnf, fyf ASC

    </select>

    <!-- 税收按 区域 数据情况 -->
    <select id="getTaxDataByRegion" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
        fskgk,
        fhjtb,
        sum(FHJ) fhj
        from tb_dm_zhzs_ssdp_gen
        where 1=1
        and fsjly = '分地区税收收入统计表'
        and fskgk != '全州'
        and fxm = '国内税收收入'
        <if test="fyear != null and fyear != ''">AND
            fnf = #{fyear}
        </if>
        <if test="fmonth != null and fmonth != ''">AND
            fyf = #{fmonth}
        </if>
        group by fskgk
        order by fhj desc
    </select>

    <!-- 税收按 区域 税种 数据情况 -->
    <select id="getTaxDataByRegionTax" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select fzsxm,fhj,fhjtb from (
            select '合计' fzsxm, FSSSRHJ fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '国内增值税' fzsxm, FGNZZS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '国内消费税' fzsxm, FGNXFS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '企业所得税' fzsxm, FQYSDS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '个人所得税' fzsxm, FGRSDS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '资源税' fzsxm, FZYS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '城市维护建设税' fzsxm, FCSWHJSS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '房产税' fzsxm, FFCS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '印花税' fzsxm, FYHS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '城镇土地使用税' fzsxm, FCZTDSYS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '土地增值税' fzsxm, FTDZZS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '车船税' fzsxm, FCCS fhj,fhjtb, fskgk,fsjly,fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '车辆购置税' fzsxm, FCLGZS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '耕地占用税' fzsxm, FGDZYS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '契税' fzsxm, FQS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '环境保护税' fzsxm, FHJBHS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
            union all
            select '其他税收' fzsxm, FQTGS fhj,fhjtb, fskgk, fsjly, fnf, fyf
            from tb_dm_zhzs_ssdp_gen where fsjly = '分地区税收收入统计表'
        ) where 1=1
        <if test="fskgk != null and fskgk != ''">AND
            fskgk = #{fskgk}
        </if>
        <if test="fyear != null and fyear != ''">AND
            fnf = #{fyear}
        </if>
        <if test="fmonth != null and fmonth != ''">AND
            fyf = #{fmonth}
        </if>

    </select>

    <!-- 税收按 区域 项目明细 数据情况 -->
    <select id="getTaxDataByRegionFxm" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
            fskgk,fxm,fjhxmmc,fhj
        from tb_dm_zhzs_ssdp_gen
        where fsjly = '分地区税收收入统计表'
        <if test="fskgk != null and fskgk != ''">AND
            fskgk = #{fskgk}
        </if>
        <if test="fyear != null and fyear != ''">AND
            fnf = #{fyear}
        </if>
        <if test="fmonth != null and fmonth != ''">AND
            fyf = #{fmonth}
        </if>

        order by fxh
    </select>

    <!-- 年度数据 按 区域 数据情况 -->
    <select id="getYearDataByRegion" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        select fnf xAxis,FHJ yAxis, fskgk legend, 'bar' type
        from(
            select
            fskgk,fnf,fhjtb,sum(FHJ) FHJ
            from tb_dm_zhzs_ssdp_gen
            where 1=1
            and fsjly = '分地区税收收入统计表'
            and fskgk != '全州'
            and fxm = '国内税收收入'
            <if test="fxm != null and fxm != ''">AND
                fskgk = #{fxm}
            </if>
            <if test="fyear != null and fyear != ''">AND
                fnf &lt;= #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">AND
                fyf = #{fmonth}
            </if>
            group by fnf
            order by fnf desc
            limit #{limit}
        ) order by xAxis asc
    </select>

    <!-- 月度数据 按 区域 数据情况 -->
    <select id="getMonthDataByRegion" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        select CONCAT(fnf, '-', fyf) xAxis,FHJ-IFNULL(PREV_FHJ, 0) yAxis, fskgk legend, 'bar' type
        from(
            select
                fskgk,fnf,fyf,fhjtb,FHJ FHJ,
                LAG(FHJ) OVER (PARTITION BY fnf ORDER BY fyf) AS PREV_FHJ
            from tb_dm_zhzs_ssdp_gen
            where 1=1
                and fsjly = '分地区税收收入统计表'
                and fskgk != '全州'
                and fxm = '国内税收收入'
                <if test="fxm != null and fxm != ''">
                    AND fskgk = #{fxm}
                </if>
                <if test="fyear != null and fyear != ''">
                    AND fnf = #{fyear}
                </if>
            group by fnf,fyf
        ) order by xAxis asc
    </select>

    <!-- 税收按 行业 数据情况 -->
    <select id="getTaxDataByIndustry" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
        fhyml,
        sum(fsssrhj-fhgdz) fsssrhj
        from tb_dm_zhzs_ssdp_gen
        where 1=1
        and fsjly = '税务汇总分行业分税种收入表_大屏'
        and fhyml is not null
        and fhydl is null and fhyzl is null and fhy is null
        and fhyml != '合计'
        <if test="fyear != null and fyear != ''">AND
            fnf = #{fyear}
        </if>
        <if test="fmonth != null and fmonth != ''">AND
            fyf = #{fmonth}
        </if>
        group by fhyml
        order by fsssrhj desc

    </select>

    <!-- 税收按 特色行业 数据情况 -->
    <select id="getTaxDataBySpecialIndustry" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
        tst.ftshymc,
        LISTAGG(tst.fhy, ',') fhy,
        sum(tdzsg.fsssrhj-ifnull(tdzsg.fhgdz, 0)) fsssrhj
        from TB_DW_ZHZS_SSDP_TSHY_GEN tst
        left join tb_dm_zhzs_ssdp_gen tdzsg on tdzsg.fjhxmmc = tst.fhy
        where 1=1
        and fsjly in('税务汇总分行业分税种收入表_大屏','数字经济核心产业相关行业表')
        <if test="fyear != null and fyear != ''">AND
            fnf = #{fyear}
        </if>
        <if test="fmonth != null and fmonth != ''">AND
            fyf = #{fmonth}
        </if>
        group by tst.ftshymc
        order by fsssrhj desc

    </select>

    <!-- 税收按 部门 数据情况 -->
    <select id="getTaxDataByDepartment" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
        tsh.fbmmc,
        <if test="fbmmc != null and fbmmc != ''">
        tsh.fhyfl,
        </if>
        sum(tdzsg.fsssrhj-tdzsg.fhgdz) fsssrhj,
        zbb.fjnlj fczhj,
        zbb.ftb fcztb
        from TB_DW_ZHZS_SSDP_HYBMFL_GEN tsh
        left join tb_dm_zhzs_ssdp_gen tdzsg on tdzsg.fjhxmmc = tsh.fhy
        left join TB_DM_ZHZS_SSDP_BNGMJJZYTJZB_GEN zbb on zbb.fzbmc like tsh.fzbmc
        and zbb.fnf = tdzsg.fnf and zbb.fjd = CEIL(tdzsg.fyf / 3)
        where 1=1
        and fsjly = '税务汇总分行业分税种收入表_大屏'
        <if test="fyear != null and fyear != ''">AND
            tdzsg.fnf = #{fyear}
        </if>
        <if test="fmonth != null and fmonth != ''">AND
            tdzsg.fyf = #{fmonth}
        </if>
        <if test="fbmmc != null and fbmmc != ''">AND
            tsh.fbmmc = #{fbmmc}
        </if>


        <if test="fbmmc != null and fbmmc != ''">
            GROUP BY tsh.fhyfl
        </if>
        <if test="fbmmc == null or fbmmc == ''">
            GROUP BY tsh.fbmmc
        </if>
        order by fsssrhj desc

    </select>


    <!-- 年度数据 按部门 数据情况 -->
    <select id="getYearDataByDepartment" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        select fnf xAxis,fsssrhj yAxis, '税收' legend, 'bar' type
        from(
            select
            tsh.fbmmc,tdzsg.fnf,
            sum(tdzsg.fsssrhj-tdzsg.fhgdz) fsssrhj
            from TB_DW_ZHZS_SSDP_HYBMFL_GEN tsh
            left join tb_dm_zhzs_ssdp_gen tdzsg on tdzsg.fjhxmmc = tsh.fhy
            where 1=1
            and tdzsg.fsjly = '税务汇总分行业分税种收入表_大屏'
            <if test="fxm != null and fxm != ''">AND
                tsh.fbmmc = #{fxm}
            </if>
            <if test="fhyfl != null and fhyfl != ''">AND
                tsh.fhyfl = #{fhyfl}
            </if>
            <if test="fyear != null and fyear != ''">AND
                tdzsg.fnf &lt;= #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">AND
                tdzsg.fyf = #{fmonth}
            </if>
            group by tdzsg.fnf
            order by tdzsg.fnf desc
            limit #{limit}
        )
        UNION ALL
        select fnf xAxis,fsssrhj yAxis,'产值' legend,'bar' type
        from(
            select tsh.fbmmc,zbb.fnf fnf,zbb.fjnlj fsssrhj
            from TB_DW_ZHZS_SSDP_HYBMFL_GEN tsh
            left join TB_DM_ZHZS_SSDP_BNGMJJZYTJZB_GEN zbb on zbb.fzbmc like tsh.fzbmc
            where 1=1
            <if test="fxm != null and fxm != ''">AND
                tsh.fbmmc = #{fxm}
            </if>
            <if test="fhyfl != null and fhyfl != ''">AND
                tsh.fhyfl = #{fhyfl}
            </if>
            <if test="fyear != null and fyear != ''">AND
                zbb.fnf &lt;= #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">AND
                zbb.fjd = CEIL(#{fmonth} / 3)
            </if>
            group by zbb.fnf
            order by zbb.fnf desc
            limit #{limit}
        )
        order by legend ,xAxis asc
    </select>

    <!-- 月度数据 按部门 数据情况 -->
    <select id="getMonthDataByDepartment" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        SELECT
            CONCAT(t1.fnf, '-', LPAD(t1.fyf, 2, '0')) AS xAxis,
            t1.fsssrhj - NVL(t1.prev_val, 0) AS yAxis,
            '税收' AS legend,
            'bar' AS type
        FROM (
            SELECT
                tdzsg.fnf,
                tdzsg.fyf,
                SUM(tdzsg.fsssrhj - tdzsg.fhgdz) AS fsssrhj,
                LAG(SUM(tdzsg.fsssrhj - tdzsg.fhgdz)) OVER (PARTITION BY tdzsg.fnf ORDER BY tdzsg.fyf) AS prev_val
            FROM TB_DW_ZHZS_SSDP_HYBMFL_GEN tsh
            LEFT JOIN tb_dm_zhzs_ssdp_gen tdzsg ON tdzsg.fjhxmmc = tsh.fhy
            WHERE tdzsg.fsjly = '税务汇总分行业分税种收入表_大屏'
                <if test="fxm != null and fxm != ''">
                    AND tsh.fbmmc = #{fxm}
                </if>
                <if test="fhyfl != null and fhyfl != ''">
                    AND tsh.fhyfl = #{fhyfl}
                </if>
                <if test="fyear != null and fyear != ''">
                    AND tdzsg.fnf = #{fyear}
                </if>
            GROUP BY tdzsg.fnf, tdzsg.fyf
        ) t1
        UNION ALL
        SELECT
            CONCAT(t2.fnf, '-', LPAD(t2.start_month + lv.level - 1, 2, '0')) AS xAxis,
            t2.quarter_val AS yAxis,
            '产值' AS legend,
            'bar' AS type
        FROM (
            SELECT
                zbb.fnf,
                CASE zbb.fjd
                    WHEN '1' THEN 1
                    WHEN '2' THEN 4
                    WHEN '3' THEN 7
                    WHEN '4' THEN 10
                END AS start_month,
                zbb.fjnlj - NVL(LAG(zbb.fjnlj) OVER (PARTITION BY zbb.fnf ORDER BY zbb.fjd), 0) AS quarter_val
            FROM TB_DW_ZHZS_SSDP_HYBMFL_GEN tsh
            LEFT JOIN TB_DM_ZHZS_SSDP_BNGMJJZYTJZB_GEN zbb ON zbb.fzbmc LIKE tsh.fzbmc
            WHERE 1=1
                <if test="fxm != null and fxm != ''">
                    AND tsh.fbmmc = #{fxm}
                </if>
                <if test="fhyfl != null and fhyfl != ''">
                    AND tsh.fhyfl = #{fhyfl}
                </if>
                <if test="fyear != null and fyear != ''">
                    AND zbb.fnf = #{fyear}
                </if>
        ) t2
        JOIN (
            SELECT LEVEL AS level FROM dual CONNECT BY LEVEL &lt;= 3
        ) lv ON 1=1

        ORDER BY legend, xAxis;

    </select>

    <!-- 税收按 税种 数据情况 -->
    <select id="getTaxDataByTaxType" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select t1.fzsxm,t1.fhj,t2.fhj fhshj
        from (
            select fzsxm,fhj from (
            select '国内增值税' fzsxm,FGNZZS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '国内消费税' fzsxm,FGNXFS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '企业所得税' fzsxm,FQYSDS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '个人所得税' fzsxm,FGRSDS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '资源税' fzsxm,FZYS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '城市维护建设税' fzsxm,FCSWHJSS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '房产税' fzsxm,FFCS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '印花税' fzsxm,FYHS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '城镇土地使用税' fzsxm,FCZTDSYS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '土地增值税' fzsxm,FTDZZS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '车船税' fzsxm,FCCS fhj,fxm, fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '车辆购置税' fzsxm,FCLGZS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '耕地占用税' fzsxm,FGDZYS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '契税' fzsxm,FQS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '环境保护税' fzsxm,FHJBHS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '其他税收' fzsxm,FQTGS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
            ) tmp where tmp.fxm = '合计' and tmp.fsjly = '税务汇总分行业分税种收入表_大屏'
            <if test="fyear != null and fyear != ''">AND
                tmp.fnf = #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">AND
                tmp.fyf = #{fmonth}
            </if>
        ) t1
        left join (
            select fzsxm,fhj from (
            select '国内增值税' fzsxm,FGNZZS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '国内消费税' fzsxm,FGNXFS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '企业所得税' fzsxm,FQYSDS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '个人所得税' fzsxm,FGRSDS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '资源税' fzsxm,FZYS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '城市维护建设税' fzsxm,FCSWHJSS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '房产税' fzsxm,FFCS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '印花税' fzsxm,FYHS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '城镇土地使用税' fzsxm,FCZTDSYS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '土地增值税' fzsxm,FTDZZS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '车船税' fzsxm,FCCS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '车辆购置税' fzsxm,FCLGZS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '烟叶税' fzsxm,FYYS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '耕地占用税' fzsxm,FGDZYS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '契税' fzsxm,FQS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '环境保护税' fzsxm,FHJBHS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            union all
            select '其他税收' fzsxm,FQTGS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
            ) tmp where tmp.fxm = '合计' and tmp.fsjly = '纳税户数分行业统计月报表'
        <if test="fyear != null and fyear != ''">AND
            tmp.fnf = #{fyear}
        </if>
        <if test="fmonth != null and fmonth != ''">AND
            tmp.fyf = #{fmonth}
        </if>
        ) t2 on  t1.fzsxm = t2.fzsxm

        order by TO_NUMBER(fhj) desc
    </select>


    <!-- 税收按 税种分类 数据情况 -->
    <select id="getTaxDataByTaxCategory" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
        <if test="flxmc == null or flxmc == ''">
            tss.flxmc,sum(ifnull(tsz.fhj,0)) fhj, sum(ifnull(tsz.fhshj,0)) fhshj
        </if>
        <if test="flxmc != null and flxmc != ''">
            tss.flxmc,tss.fzsxm fzsxm,ifnull(tsz.fhj,0) fhj, ifnull(tsz.fhshj,0) fhshj
        </if>
        from TB_DW_ZHZS_SSDP_SSLX_GEN tss
        left join
        (select t1.fzsxm,t1.fhj,t2.fhj fhshj
			from (
			select fzsxm,fhj from (
			select '国内增值税' fzsxm,FGNZZS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '国内消费税' fzsxm,FGNXFS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '企业所得税' fzsxm,FQYSDS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '个人所得税' fzsxm,FGRSDS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '资源税' fzsxm,FZYS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '城市维护建设税' fzsxm,FCSWHJSS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '房产税' fzsxm,FFCS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '印花税' fzsxm,FYHS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '城镇土地使用税' fzsxm,FCZTDSYS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '土地增值税' fzsxm,FTDZZS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '车船税' fzsxm,FCCS fhj,fxm, fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '车辆购置税' fzsxm,FCLGZS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '耕地占用税' fzsxm,FGDZYS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '契税' fzsxm,FQS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '环境保护税' fzsxm,FHJBHS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			union all
			select '其他税收' fzsxm,FQTGS fhj,fxm,fsjly,fnf,fyf from tb_dm_zhzs_ssdp_gen
			) tmp where tmp.fxm = '合计' and tmp.fsjly = '税务汇总分行业分税种收入表_大屏'
            <if test="fyear != null and fyear != ''">AND
                tmp.fnf = #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">AND
                tmp.fyf = #{fmonth}
            </if>
            ) t1
            left join (
                select fzsxm,fhj from (
                select '国内增值税' fzsxm,FGNZZS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '国内消费税' fzsxm,FGNXFS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '企业所得税' fzsxm,FQYSDS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '个人所得税' fzsxm,FGRSDS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '资源税' fzsxm,FZYS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '城市维护建设税' fzsxm,FCSWHJSS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '房产税' fzsxm,FFCS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '印花税' fzsxm,FYHS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '城镇土地使用税' fzsxm,FCZTDSYS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '土地增值税' fzsxm,FTDZZS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '车船税' fzsxm,FCCS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '车辆购置税' fzsxm,FCLGZS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '烟叶税' fzsxm,FYYS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '耕地占用税' fzsxm,FGDZYS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '契税' fzsxm,FQS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '环境保护税' fzsxm,FHJBHS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                union all
                select '其他税收' fzsxm,FQTGS fhj,fxm,fsjly, fnf, fyf from tb_dm_zhzs_ssdp_gen
                ) tmp where tmp.fxm = '合计' and tmp.fsjly = '纳税户数分行业统计月报表'
            <if test="fyear != null and fyear != ''">AND
                tmp.fnf = #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">AND
                tmp.fyf = #{fmonth}
            </if>
            ) t2 on  t1.fzsxm = t2.fzsxm
        ) tsz on tss.fzsxm = tsz.fzsxm
        where  1=1
        <if test="flxmc != null and flxmc != ''">AND
            <if test="flxmcAll == null or flxmcAll == ''">
                tss.flxmc in (#{flxmc})
            </if>
            <if test="flxmcAll != null and flxmcAll != ''">
                tss.flxmc in ('流转税','所得税','财产行为资源税')
            </if>
        </if>
        <if test="flxmc == null or flxmc == ''">
            group by tss.flxmc
        </if>
        order by TO_NUMBER(fhj) desc
    </select>

    <!-- 年度数据 按 税种分类 数据情况 -->
    <select id="getYearDataByTaxType" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        SELECT
            tmp.fnf AS xAxis, SUM(tmp.fhj) AS yAxis, '税收' AS legend, 'bar' AS type
        FROM (
            SELECT
                fnf, fyf, fxm, fsjly,fzsxm_h fzsxm,fhj_h fhj
            FROM tb_dm_zhzs_ssdp_gen
            UNPIVOT (
                fhj_h FOR fzsxm_h IN (
                    FGNZZS AS '国内增值税', FGNXFS AS '国内消费税', FQYSDS AS '企业所得税', FGRSDS AS '个人所得税',
                    FZYS AS '资源税', FCSWHJSS AS '城市维护建设税', FFCS AS '房产税', FYHS AS '印花税', FCZTDSYS AS '城镇土地使用税',
                    FTDZZS AS '土地增值税', FCCS AS '车船税', FCLGZS AS '车辆购置税', FGDZYS AS '耕地占用税', FQS AS '契税', FHJBHS AS '环境保护税',
                    FQTGS AS '其他税收'
                )
            )
        ) tmp
        LEFT JOIN TB_DW_ZHZS_SSDP_SSLX_GEN tss ON tmp.fzsxm = tss.fzsxm
        WHERE tmp.fxm = '合计'
          AND tmp.fsjly = '税务汇总分行业分税种收入表_大屏'
          <if test="fxm != null and fxm != ''">
            AND
            <if test="flx == null or flx == ''">
                tmp.fzsxm = #{fxm}
            </if>
            <if test="flx != null and flx != ''">
                tss.flxmc = #{fxm}
          </if>
          </if>
          <if test="fyear != null and fyear != ''">
            AND tmp.fnf &lt;= #{fyear}
          </if>
          <if test="fmonth != null and fmonth != ''">
            AND tmp.fyf = #{fmonth}
        </if>
        <if test="flx != null and flx != ''">
          GROUP BY tss.flxmc, tmp.fnf
        </if>
        <if test="flx == null or flx == ''">
          GROUP BY tmp.fzsxm, tmp.fnf
        </if>
        UNION ALL
        SELECT
            tmp.fnf AS xAxis, SUM(tmp.fhj) AS yAxis, '户数' AS legend, 'bar' AS type
        FROM (
            SELECT
                fnf, fyf, fxm, fsjly,fzsxm_h fzsxm,fhj_h fhj
            FROM tb_dm_zhzs_ssdp_gen
            UNPIVOT (
                fhj_h FOR fzsxm_h IN (
                    FGNZZS AS '国内增值税', FGNXFS AS '国内消费税', FQYSDS AS '企业所得税', FGRSDS AS '个人所得税',
                    FZYS AS '资源税', FCSWHJSS AS '城市维护建设税', FFCS AS '房产税', FYHS AS '印花税', FCZTDSYS AS '城镇土地使用税',
                    FTDZZS AS '土地增值税', FCCS AS '车船税', FCLGZS AS '车辆购置税', FYYS AS '烟叶税', FGDZYS AS '耕地占用税', FQS AS '契税',
                    FHJBHS AS '环境保护税', FQTGS AS '其他税收'
                )
            )
        ) tmp
        LEFT JOIN TB_DW_ZHZS_SSDP_SSLX_GEN tss ON tmp.fzsxm = tss.fzsxm
        WHERE tmp.fxm = '合计'
          AND tmp.fsjly = '纳税户数分行业统计月报表'
          <if test="fxm != null and fxm != ''">
            AND
            <if test="flx == null or flx == ''">
                tmp.fzsxm = #{fxm}
            </if>
            <if test="flx != null and flx != ''">
                tss.flxmc = #{fxm}
            </if>
          </if>
          <if test="fyear != null and fyear != ''">
            AND tmp.fnf &lt;= #{fyear}
          </if>
          <if test="fmonth != null and fmonth != ''">
            AND tmp.fyf = #{fmonth}
          </if>
        <if test="flx != null and flx != ''">
          GROUP BY tss.flxmc, tmp.fnf
        </if>
        <if test="flx == null or flx == ''">
          GROUP BY tmp.fzsxm, tmp.fnf
        </if>
        ORDER BY legend, xAxis ASC;
    </select>

    <!-- 月度数据 按 税种分类 数据情况 -->
    <select id="getMonthDataByTaxType" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        SELECT
            CONCAT(tmp.fnf, '-', tmp.fyf) AS xAxis,
            SUM(CASE WHEN tmp.last_fhj IS NULL THEN tmp.fhj ELSE tmp.fhj - tmp.last_fhj END) AS yAxis,
            '税收' AS legend,
            'bar' AS type
        FROM (
            SELECT
                fnf,
                fyf,
                fxm,
                fsjly,
                fzsxm_h AS fzsxm,
                fhj_h AS fhj,
                LAG(fhj_h) OVER (PARTITION BY fzsxm_h,fnf ORDER BY  fyf) AS last_fhj
            FROM tb_dm_zhzs_ssdp_gen
            UNPIVOT (
                fhj_h FOR fzsxm_h IN (
                    FGNZZS AS '国内增值税', FGNXFS AS '国内消费税', FQYSDS AS '企业所得税', FGRSDS AS '个人所得税',
                    FZYS AS '资源税', FCSWHJSS AS '城市维护建设税', FFCS AS '房产税', FYHS AS '印花税', FCZTDSYS AS '城镇土地使用税',
                    FTDZZS AS '土地增值税', FCCS AS '车船税', FCLGZS AS '车辆购置税', FGDZYS AS '耕地占用税', FQS AS '契税',
                    FHJBHS AS '环境保护税', FQTGS AS '其他税收'
                )
            )
            WHERE fxm = '合计' AND fsjly = '税务汇总分行业分税种收入表_大屏'
        ) tmp
        LEFT JOIN TB_DW_ZHZS_SSDP_SSLX_GEN tss ON tmp.fzsxm = tss.fzsxm
        WHERE 1=1
        <if test="fxm != null and fxm != ''">
          AND (
            <if test="flx == null or flx == ''">
              tmp.fzsxm = #{fxm}
            </if>
            <if test="flx != null and flx != ''">
              tss.flxmc = #{fxm}
            </if>
          )
        </if>
        <if test="fyear != null and fyear != ''">
          AND tmp.fnf = #{fyear}
        </if>
        <if test="flx != null and flx != ''">
          GROUP BY tss.flxmc, tmp.fnf, tmp.fyf
        </if>
        <if test="flx == null or flx == ''">
          GROUP BY tmp.fzsxm, tmp.fnf, tmp.fyf
        </if>

        UNION ALL

        SELECT
            CONCAT(tmp.fnf, '-', tmp.fyf) AS xAxis,
            SUM(CASE WHEN tmp.last_fhj IS NULL THEN tmp.fhj ELSE tmp.fhj - tmp.last_fhj END) AS yAxis,
            '户数' AS legend,
            'bar' AS type
        FROM (
            SELECT
                fnf,
                fyf,
                fxm,
                fsjly,
                fzsxm_h AS fzsxm,
                fhj_h AS fhj,
                LAG(fhj_h) OVER (PARTITION BY fzsxm_h,fnf ORDER BY fyf) AS last_fhj
            FROM tb_dm_zhzs_ssdp_gen
            UNPIVOT (
                fhj_h FOR fzsxm_h IN (
                    FGNZZS AS '国内增值税', FGNXFS AS '国内消费税', FQYSDS AS '企业所得税', FGRSDS AS '个人所得税',
                    FZYS AS '资源税', FCSWHJSS AS '城市维护建设税', FFCS AS '房产税', FYHS AS '印花税', FCZTDSYS AS '城镇土地使用税',
                    FTDZZS AS '土地增值税', FCCS AS '车船税', FCLGZS AS '车辆购置税', FYYS AS '烟叶税', FGDZYS AS '耕地占用税',
                    FQS AS '契税', FHJBHS AS '环境保护税', FQTGS AS '其他税收'
                )
            )
            WHERE fxm = '合计' AND fsjly = '纳税户数分行业统计月报表'
        ) tmp
        LEFT JOIN TB_DW_ZHZS_SSDP_SSLX_GEN tss ON tmp.fzsxm = tss.fzsxm
        WHERE 1=1
        <if test="fxm != null and fxm != ''">
          AND (
            <if test="flx == null or flx == ''">
              tmp.fzsxm = #{fxm}
            </if>
            <if test="flx != null and flx != ''">
              tss.flxmc = #{fxm}
            </if>
          )
        </if>
        <if test="fyear != null and fyear != ''">
          AND tmp.fnf = #{fyear}
        </if>
        <if test="flx != null and flx != ''">
          GROUP BY tss.flxmc, tmp.fnf, tmp.fyf
        </if>
        <if test="flx == null or flx == ''">
          GROUP BY tmp.fzsxm, tmp.fnf, tmp.fyf
        </if>

        ORDER BY legend, xAxis ASC

    </select>


    <!-- 税收按 市场主体 数据情况 -->
    <select id="getTaxDataByMainstay" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
        tmp2024.fjhxmmc,(ifnull(tmp.fsssrhj,0)-ifnull(tmp.fhgdz,0)) fsssrhj
        from (
            select fjhxmmc from tb_dm_zhzs_ssdp_gen
            where fsjly = '税收收入分市场主体类型月报表'
            and REGEXP_LIKE (fxm,'(一|二|三|四|五|六|七|八|九|十|十一|十二|十三|十四|十五|十六|十七|十八|十九|二十)、.*$$')
            <if test="fyear != null and fyear != ''">AND
                fnf = #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">AND
                fyf = #{fmonth}
            </if>
            ) tmp2024
        left join(
            select fjhxmmc, fsssrhj, fhgdz,fnf,fyf from tb_dm_zhzs_ssdp_gen
            where fsjly = '税收收入分市场主体类型月报表'
            and REGEXP_LIKE (fxm,'(一|二|三|四|五|六|七|八|九|十|十一|十二|十三|十四|十五|十六|十七|十八|十九|二十)、.*$$')
            <if test="fyear != null and fyear != ''">AND
                fnf = #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">AND
                fyf = #{fmonth}
            </if>
            ) tmp on tmp.fjhxmmc = tmp2024.fjhxmmc

        order by TO_NUMBER(fsssrhj) desc
    </select>


    <!-- 年度数据 按主体 数据情况 -->
    <select id="getYearDataByMainstay" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        select fnf xAxis,fsssrhj yAxis, fjhxmmc legend, 'bar' type
        from(
            select
            fjhxmmc,(fsssrhj-fhgdz) fsssrhj,fnf
            from tb_dm_zhzs_ssdp_gen
            where fsjly = '税收收入分市场主体类型月报表'
            <if test="fxm != null and fxm != ''">AND
                fjhxmmc = #{fxm}
            </if>
            <if test="fyear != null and fyear != ''">AND
                fnf &lt;= #{fyear}
            </if>
            <if test="fmonth != null and fmonth != ''">AND
                fyf = #{fmonth}
            </if>
            group by fnf
            order by fnf desc
            limit #{limit}
        ) order by xAxis asc
    </select>

    <!-- 月度数据 按主体 数据情况 -->
    <select id="getMonthDataByMainstay" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        SELECT
            CONCAT(fnf, '-', fyf) AS xAxis,
            (fsssrhj - fhgdz) - IFNULL(prev_val, 0) AS yAxis,
            fjhxmmc AS legend, 'bar' AS type
        FROM (
            SELECT fjhxmmc, fnf, fyf,
                SUM(fsssrhj) AS fsssrhj,
                SUM(fhgdz) AS fhgdz,
                LAG(SUM(fsssrhj) - SUM(fhgdz)) OVER (PARTITION BY fjhxmmc, fnf ORDER BY fyf) AS prev_val
            FROM tb_dm_zhzs_ssdp_gen
            WHERE fsjly = '税收收入分市场主体类型月报表'
                <if test="fxm != null and fxm != ''">
                    AND fjhxmmc = #{fxm}
                </if>
                <if test="fyear != null and fyear != ''">
                    AND fnf = #{fyear}
                </if>
            GROUP BY fjhxmmc, fnf, fyf
        ) t
        ORDER BY xAxis ASC

    </select>

    <!-- 年度数据 按行业 数据情况 -->
    <select id="getYearDataByIndustry" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        select fnf xAxis,fsssrhj yAxis, #{fhy} legend, 'bar' type
        from (
                 select
                     fjhxmmc,max(fsssrhj-ifnull(fhgdz, 0)) fsssrhj,fnf,fyf
                 from tb_dm_zhzs_ssdp_gen
                 where fsjly in('税务汇总分行业分税种收入表_大屏','数字经济核心产业相关行业表')
                    <if test="fhy != null">AND
                        fjhxmmc IN
                         <foreach collection="fhyList" item="item" separator="," close=")" open="(">
                            #{item}
                        </foreach>
                    </if>
                    <if test="fyear != null and fyear != ''">AND
                        fnf &lt;= #{fyear}
                    </if>
                    <if test="fmonth != null and fmonth != ''">AND
                        fyf = #{fmonth}
                    </if>
                 group by fnf
                 order by fnf desc
                     limit #{limit}
             ) order by xAxis asc
    </select>

    <!-- 月度数据 按行业 数据情况 -->
    <select id="getMonthDataByIndustry" parameterType="java.util.HashMap" resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        SELECT
            CONCAT(fnf, '-', fyf) AS xAxis, fsssrhj - IFNULL(prev_fsssrhj, 0) AS yAxis,
            #{fhy} AS legend, 'bar' AS type
        FROM (
                 SELECT
                     fjhxmmc, fsssrhj, fnf, fyf,
                     LAG(fsssrhj) OVER (PARTITION BY fnf ORDER BY fyf) AS prev_fsssrhj
                 FROM (
                          SELECT
                              fjhxmmc, (fsssrhj - IFNULL(fhgdz, 0)) AS fsssrhj, fnf, fyf
                          FROM tb_dm_zhzs_ssdp_gen
                          WHERE fsjly IN ('税务汇总分行业分税种收入表_大屏', '数字经济核心产业相关行业表')
                          <if test="fhy != null">
                              AND fjhxmmc IN
                               <foreach collection="fhyList" item="item" separator="," close=")" open="(">
                                  #{item}
                              </foreach>
                          </if>
                          <if test="fyear != null and fyear != ''">
                              AND fnf = #{fyear}
                          </if>
                          GROUP BY fjhxmmc, fnf, fyf
                      ) t1
             ) t2
        ORDER BY xAxis ASC
    </select>

    <select id="rankingByTaxpayer" resultType="java.util.Map">
        select
        case when ishj is null then '合计' else fnsrmc end fnsrmc
        ,case when ishj is null then '' else 纳税人识别号 end fnsrsbh
        ,fjnsjrk
        ,fjndylx
        ,fqnsjrk
        ,fqndylx
        ,fzje
        ,mounthFzje
        ,mounthYearOverYear
        ,yearOverYear
        , case when ishj is null then ''else fdesc end fdesc
        from (
        select
        max(fdesc) fdesc,
        concat(纳税人名称,纳税人识别号) ishj,
        max(纳税人名称) fnsrmc,
        max(纳税人识别号) 纳税人识别号,
        round(sum(今年税收合计) / 10000, 2) fjnsjrk,
        round(sum(今年当月税收合计) / 10000, 2) fjndylx,
        round(sum(去年税收合计) / 10000, 2) fqnsjrk,
        round(sum(去年当月税收合计) / 10000, 2) fqndylx,
        round((sum(今年税收合计) - sum(去年税收合计)) / 10000, 2) fzje,
        round((sum(今年当月税收合计) - sum(去年当月税收合计)) / 10000, 2) as mounthFzje,
        case
        when sum(去年当月税收合计) = 0 then 100
        else round((sum(今年当月税收合计) - sum(去年当月税收合计)) / sum(去年当月税收合计), 4) * 100
        end mounthYearOverYear,
        case
        when sum(去年税收合计) = 0 then 100
        else round((sum(今年税收合计) - sum(去年税收合计)) / sum(去年税收合计), 4) * 100
        end yearOverYear
        from (
        select
        a.fnsrmc 纳税人名称
        ,row_number() over ( order by sum(case when YEAR(frkrq) = #{start_year} then
        case when #{frkkj} = '全口径税收' then fhj when #{frkkj} = '地方税收' then fdfsr end
        else 0 end) desc ) as fdesc
        ,max(fnsrsbh) 纳税人识别号
        ,sum(case when YEAR(frkrq) = #{start_year} then
        case when #{frkkj} = '全口径税收' then fhj when #{frkkj} = '地方税收' then fdfsr end
        else 0 end) as 今年税收合计
        ,sum(case when YEAR(frkrq) = #{start_year} and MONTH(frkrq) = #{fendmonth} * 1 then
        case when #{frkkj} = '全口径税收' then fhj when #{frkkj} = '地方税收' then fdfsr end
        else 0 end) as 今年当月税收合计
        ,sum(case when YEAR(frkrq) = #{start_year} - 1 then
        case when #{frkkj} = '全口径税收' then fhj when #{frkkj} = '地方税收' then fdfsr end
        else 0 end) as 去年税收合计
        ,sum(case when YEAR(frkrq) = #{start_year} - 1 and MONTH(frkrq) = #{fendmonth} * 1 then
        case when #{frkkj} = '全口径税收' then fhj when #{frkkj} = '地方税收' then fdfsr end
        else 0 end) as 去年当月税收合计
        from tb_dw_srfx_srfx_main a
        where
        (frkrq between to_date(CONCAT(#{start_year},'-',#{fstartmonth},'-','01'),'YYYY-MM-DD')
        and last_day(to_date(CONCAT(#{start_year},'-',#{fendmonth}),'yyyy-mm'))
        or
        frkrq between to_date(CONCAT(#{start_year} - 1,'-',#{fstartmonth},'-','01'),'YYYY-MM-DD')
        and last_day(to_date(CONCAT(#{start_year} -1,'-',#{fendmonth}),'yyyy-mm'))
        )
        <if test="fssqyList != null and fssqyList.size>0">
            and a.fskgk in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null and fzsxmList.size>0">
            and a.fzsxm in
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhymlList != null and fhymlList.size>0">
            and a.fhyml in
            <foreach collection="fhymlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            a.fnsrmc like CONCAT('%' ,#{fnsrmc},'%')
        </if>
        <if test="fldts == 2">and
            fyskm not like '%留抵退%'
        </if>
        group by a.fnsrmc
        ) as y3
        where 1=1
        <if test="fqsje != null and fqsje != ''">and
            今年税收合计 &gt;= ${fqsje} * 10000
        </if>
        <if test="fnsrpm != null and fnsrpm != ''">and
            fdesc &lt;= #{fnsrpm}
        </if>
        group by rollup(concat(纳税人名称,纳税人识别号) )
        ) aa
        order by case when ishj is null then 0 else 1 end, fjnsjrk desc
        LIMIT 11
    </select>

    <!-- 获取有数据的最大日期 -->
    <select id="getSsdpMaxDateByData" resultType="java.lang.String">
        SELECT min(maxDate) maxDate FROM (
         SELECT max(concat(fnf, '-', fyf)) maxDate FROM TB_DM_ZHZS_SSDP_GEN
         WHERE FSJLY  IN ('税务汇总分行业分税种收入表_大屏', '税务汇总入库税收收入表')
         UNION ALL
         SELECT max(concat(fnf, '-', fyf)) maxDate FROM TB_DM_ZHZS_CZSZYB_GEN
        )
    </select>

</mapper>
