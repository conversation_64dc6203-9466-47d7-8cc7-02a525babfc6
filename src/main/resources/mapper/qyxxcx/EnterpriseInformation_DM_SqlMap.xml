<?xml version="1.0" encoding="UTF-8"?><!--

    Copyright 2004-2024 the original author or authors.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

--><!--Converted at: Tue Oct 22 10:19:49 CST 2024-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.qyxxcx.mapper.EnterpriseInformationMapper">

    <!--企业信息查询-->
    <select id="findQyxxcx" parameterType="com.hnbp.local.qyxxcx.model.QyxxcxInfo" resultType="java.util.HashMap">
        WITH
        t2 AS(
        SELECT
        fnsrmc,
        fshxydm,
        fnsrzt,
        fdjzclx,
        fhy,
        fdjrq,
        fzczb,
        fjyfw,
        fdjjg,
        fzgswjg,
        fscjydz fjydz,
        fzcdz,
        fjdxz,
        ROW_NUMBER() OVER(PARTITION BY fnsrmc ORDER BY fdjrq) rn
        FROM tb_dw_srfx_swdjxx_main
        WHERE 1=1
        <if test="fnsrztList != null and fnsrztList.size>0">AND
            fnsrzt IN
             <foreach collection="fnsrztList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">AND
            fnsrmc like CONCAT('%',#{fnsrmc},'%')
        </if>
        <if test="fnsrsbh != null and fnsrsbh != ''">AND
            fshxydm like CONCAT('%',#{fnsrsbh},'%')
        </if>
        <if test="fdjrqqz != null and fdjrqqz != ''">AND
            fdjrq between to_date(concat(#{fdjrqqzks},'-01'),'YYYY-MM-DD')
            AND last_day(to_date(#{fdjrqqzjs},'YYYY-MM'))
        </if>
        )
        SELECT t2.fnsrmc,t2.fshxydm fnsrsbh, t2.fnsrzt,t2.fdjzclx, t2.fhy, date_format(t2.fdjrq,'YYYY-MM-DD') fdjrq,
        t2.fzczb,t2.fzcdz, t2.fjdxz fjdxz,
        t2.fjydz,
        t2.fjyfw,
        t2.fdjjg,
        t2.fzgswjg
        FROM t2
        WHERE t2.rn = 1
        order by case when instr(t2.fzgswjg, '企业') > 0 and t2.fnsrzt = '正常' then 2 else 1 end desc,fdjrq desc NULLS LAST
    </select>


</mapper>
