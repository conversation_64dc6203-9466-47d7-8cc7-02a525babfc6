<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.fxkgl.mapper.RiskLibraryFileMapper">


    <!-- 插入风险库附件 -->
    <insert id="insert">
        INSERT INTO TB_DW_ZHZS_FXKGL_FILE_GEN (fid, flb,ffxyjmxid, fwjmc, fwjlx, fwjlj,create_time)
        VALUES (#{fid}, #{flb},#{ffxyjmxid}, #{fwjmc}, #{fwjlx}, #{fwjlj},now())
    </insert>


    <!-- 删除风险库附件 -->
    <delete id="deleteByFxyjmxId">
        DELETE FROM TB_DW_ZHZS_FXKGL_FILE_GEN
        WHERE ffxyjmxid = #{ffxyjmxid}
    </delete>

    <!-- 删除风险库附件 -->
    <delete id="deleteById">
        DELETE FROM TB_DW_ZHZS_FXKGL_FILE_GEN
        WHERE fid = #{fid}
    </delete>


    <!-- 查询所有风险库附件 -->
    <select id="queryRiskLibraryFileList" resultType="com.hnbp.local.fxkgl.model.RiskLibraryFile">
        SELECT
            fid, flb,ffxyjmxid, fwjmc, fwjlx, fwjlj,TO_CHAR(create_time, 'YYYY-MM-DD HH24:MI:SS') createtime
        FROM TB_DW_ZHZS_FXKGL_FILE_GEN
        <where>
            <if test="ffxyjmxid != null and ffxyjmxid != ''">
                AND ffxyjmxid = #{ffxyjmxid}
            </if>
            <if test="fwjmc != null and fwjmc != ''">
                AND fwjmc = #{fwjmc}
            </if>
            <if test="fwjlx != null and fwjlx != ''">
                AND fwjlx = #{fwjlx}
            </if>
            <if test="flb != null and flb != ''">
                AND flb = #{flb}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
