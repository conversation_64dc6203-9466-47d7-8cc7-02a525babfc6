<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.fxkgl.mapper.RiskLibraryMapper">

    <insert id="insert" parameterType="com.hnbp.local.fxkgl.model.RiskLibrary">
        INSERT INTO TB_DW_ZHZS_FXKGL_GEN (fid,ffxyjmx,ffxyjrqq,ffxyjrqz,fyjhs,fyjkzskj,fbgscrq,fcjr,create_time)
        VALUES (#{fid},#{ffxyjmx},#{ffxyjrqq},#{ffxyjrqz},#{fyjhs},#{fyjkzskj},#{fbgscrq},#{fcjr},now())
    </insert>

    <update id="update" parameterType="com.hnbp.local.fxkgl.model.RiskLibrary">
        UPDATE TB_DW_ZHZS_FXKGL_GEN
        SET ffxyjmx = #{ffxyjmx},
            ffxyjrqq = #{ffxyjrqq},
            ffxyjrqz = #{ffxyjrqz},
            fyjhs = #{fyjhs},
            fyjkzskj = #{fyjkzskj},
            fbgscrq = #{fbgscrq},
            fcjr = #{fcjr},
            update_time = now()
        WHERE fid = #{fid}
    </update>

    <delete id="deleteById">
        DELETE FROM TB_DW_ZHZS_FXKGL_GEN
        WHERE fid = #{fid}
    </delete>


    <select id="queryRiskLibraryList" resultType="com.hnbp.local.fxkgl.model.RiskLibrary">
        select fid,ffxyjmx,ffxyjrqq,ffxyjrqz,fyjhs,fyjkzskj,fbgscrq,fcjr,TO_CHAR(create_time, 'YYYY-MM-DD HH24:MI:SS') createtime
        from TB_DW_ZHZS_FXKGL_GEN
        <where>
            <if test="ffxyjmx != null and ffxyjmx != ''">
                and ffxyjmx like concat('%',#{ffxyjmx},'%')
            </if>
            <if test="fbgscrqq != null and fbgscrqq != '' and fbgscrqz != null and fbgscrqz != ''">
                and fbgscrq &gt;= #{fbgscrqq} and fbgscrq &lt;= #{fbgscrqz}
            </if>
            <if test="ffxyjrqq != null and ffxyjrqq != '' and ffxyjrqz != null and ffxyjrqz != ''">
                and (
                    ffxyjrqq &gt;= #{ffxyjrqq} and ffxyjrqq &lt;= #{ffxyjrqz}
                    or ffxyjrqz &gt;= #{ffxyjrqq} and ffxyjrqz &lt;= #{ffxyjrqz}
                )
            </if>
            <if test="fidList != null">
                and fid IN
                <foreach collection="fidList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="getRiskLibraryFiles" resultType="com.hnbp.local.fxkgl.model.RiskLibraryFile">
        select fid, ffxyjmxid, fwjmc, fwjlx, fwjlj, create_time
        from TB_DW_ZHZS_FXKGL_FILE_GEN
        <where>
            <if test="riskIds != null">
                AND FFXYJMXID IN
                <foreach collection="riskIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>
