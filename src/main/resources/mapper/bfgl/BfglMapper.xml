<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.bfgl.mapper.BfglMapper">
    <select id="getyqkh" parameterType="com.hnbp.local.bfgl.model.BFglDTO" resultType="map">
        select aliasname
               ,fshjjn
               ,fcljn
               ,fshjqn
               ,fclqn
               ,fhjtqzj
               ,fhjtqzjb
               ,fcltqzj
               ,fcltqzjb
               ,fhykjjn
               ,fhykjqn
        ,round(case when fjnhj = 0 then 0 else ((fshjjn)-(fshjqn))/(fjnhj)*100 end,2) as fshjqnldxs
        ,round(case when fdfhjjn = 0 then 0 else((fcljn)-(fclqn))/(fdfhjjn)*100 end,2) as fclqnldxs
        from (
            select
            aliasname
            ,round((sum(fshjjn))/10000,2) fshjjn
            ,round((sum(fcljn))/10000,2) fcljn
            ,round((sum(fshjqn))/10000,2) fshjqn
            ,round((sum(fclqn))/10000,2) fclqn
            ,round((sum(fhjtqzj))/10000,2) fhjtqzj
            ,round(decode(sum(fshjqn),0,0,(sum(fshjjn)-sum(fshjqn))/sum(fshjqn)*100),2)fhjtqzjb
            ,round((sum(fcltqzj))/10000,2) fcltqzj
            ,round(decode(sum(fclqn),0,0,(sum(fcljn)-sum(fclqn))/sum(fclqn)*100),2)fcltqzjb
            ,round((sum(fhykjjn))/10000,2) fhykjjn
            ,round((sum(fhykjqn))/10000,2) fhykjqn
            ,round(sum(fjnhj)/10000,2) fjnhj
            ,round(sum(fdfhjjn)/10000,2) fdfhjjn
            from(
                select aliasname, nsrmc from(
                select aliasname, fid from tb_dw_cyjs_park_cfg
                where 1=1
                <if test="fyqmc != null and fyqmc != ''">
                    and fid in
                    <foreach item='fyqmc' index="fyqmc" collection="fyqmc.split(',')" open="(" separator="," close=")">
                        #{fyqmc}
                    </foreach>
                </if>
                )a
            left join(
                select nsrmc,parkid from tb_dw_cyjs_nsrmczjb_cfg
                where 1=1
                <if test="fqyrk !=null and fqyrk !=''">
                    and year=#{fqyrk}
                </if>
                )b
            on a.fid=b.parkid
            )a<!--企业名单-->
        left join(
        select 纳税人名称
        ,sum(fshjjn)fshjjn
        ,sum(fcljn)fcljn
        ,sum(fshjqn)fshjqn
        ,sum(fclqn) fclqn
        ,sum(fshjjn)-sum(fshjqn)fhjtqzj
        ,sum(fcljn)-sum(fclqn)fcltqzj
        ,sum(fhykjjn) fhykjjn
        ,sum(fhykjqn) fhykjqn
        ,sum(fjnhj) fjnhj
        ,sum(fdfhjjn) fdfhjjn
        from (
        select c.fnsrmc as 纳税人名称 ,sum(fshj) fshjjn,sum(fshj-case when fyskm like '%留抵退%' then fhj else 0 end) fhykjjn,sum(fqxj+fdsj) fcljn,0 fshjqn,0 as fhykjqn,0
        fclqn,sum(fhj) as fjnhj,sum(fqxj+fdsj) as fdfhjjn from tb_dm_cyjs_ssmd_main c
        where 1=1
        and frkrq between TO_DATE(CONCAT(#{start_time},'-01'), 'YYYY-MM-DD') and LAST_DAY(TO_DATE(#{end_time}, 'YYYY-MM'))
            and c.fnsrmc in(
                select nsrmc from tb_dw_cyjs_nsrmczjb_cfg
                where parkid in(
                select fid from tb_dw_cyjs_park_cfg
                where 1=1
                <if test="fyqmc != null and fyqmc != ''">
                    and fid in
                    <foreach item='fyqmc' index="fyqmc" collection="fyqmc.split(',')" open="(" separator="," close=")">
                        #{fyqmc}
                    </foreach>
                </if>
                )
            <if test="fqyrk !=null and fqyrk !=''">
                and year=#{fqyrk}
            </if>
            )
        <if test="fsz != null and fsz != ''">
            and fzsxm in
            <foreach item='fsz' index="fsz" collection="fsz.split(',')" open="(" separator="," close=")">
                #{fsz}
            </foreach>
        </if>
        group by c.fnsrmc<!--今年年税收-->

        union all

        select c.fnsrmc as 纳税人名称,0 fshjjn,0 fhykjjn,0 fcljn,sum(fshj) fshjqn,sum(fshj-case when fyskm like '%留抵退%' then fhj else 0 end) fhykjqn,sum(fqxj+fdsj) fclqn,0 as
        fjnhj,0 as fdfhjjn from
        tb_dm_cyjs_ssmd_main c
        where 1=1
        and frkrq between ADD_MONTHS(TO_DATE(CONCAT(#{start_time},'-01'), 'YYYY-MM-DD'), -12) and ADD_MONTHS(LAST_DAY(TO_DATE(#{end_time}, 'YYYY-MM')), -12)
            and c.fnsrmc in(
            select nsrmc from tb_dw_cyjs_nsrmczjb_cfg
                where parkid in(
                select fid from tb_dw_cyjs_park_cfg
                where 1=1
                <if test="fyqmc != null and fyqmc != ''">
                    and fid in
                    <foreach item='fyqmc' index="fyqmc" collection="fyqmc.split(',')" open="(" separator="," close=")">
                        #{fyqmc}
                    </foreach>
                </if>
                )
            <if test="fqyrk !=null and fqyrk !=''">
                and year=#{fqyrk}
            </if>
            )
        <if test="fsz != null and fsz != ''">
            and fzsxm in
            <foreach item='fsz' index="fsz" collection="fsz.split(',')" open="(" separator="," close=")">
                #{fsz}
            </foreach>
        </if>
        group by c.fnsrmc<!--去年税收-->
        )a
        group by 纳税人名称
        ) b
        on a.nsrmc=b.纳税人名称
        where fshjjn is not null
        group by aliasname
        )a
        order by fshjjn desc
    </select>

    <select id="getyqkh_mx" resultType="map" parameterType="map">
        SELECT
        ifnull(nsrmc,'合计') nsrmc
        ,case when nsrmc is null then '' else max(fnsrsbh) end fnsrsbh
        ,round(ifnull(sum(fshjjn),0),2)fshjjn
        ,round(ifnull(sum(fcljn),0),2)fcljn
        ,round(ifnull(sum(fshjqn),0),2)fshjqn
        ,round(ifnull(sum(fclqn),0),2)fclqn
        ,round(ifnull(sum(fhjtqzj),0),2)fhjtqzj
        ,round(decode(sum(fshjqn),0,0,(sum(fshjjn)-sum(fshjqn))/sum(fshjqn)*100),2)fhjtqzjb
        ,round(ifnull(sum(fcltqzj),0),2)fcltqzj
        ,round(decode(sum(fclqn),0,0,(sum(fcljn)-sum(fclqn))/sum(fclqn)*100),2)fcltqzjb
        FROM (
            SELECT aliasname, nsrmc
            FROM (
            SELECT aliasname, fid
            FROM tb_dw_cyjs_park_cfg
            WHERE 1 = 1
            <if test="fyqmc!= null and fyqmc!= ''">
                and aliasname=#{fyqmc}
            </if>
            ) a
            LEFT JOIN (
            SELECT nsrmc, parkid
            FROM tb_dw_cyjs_nsrmczjb_cfg
            WHERE 1 = 1
            <if test="fqyrk!=null and fqyrk!=''">
                AND year = #{fqyrk}
            </if>
            ) b ON a.fid = b.parkid
        ) a
        LEFT JOIN (
        SELECT 纳税人名称,fnsrsbh, SUM(fshjjn) AS fshjjn, SUM(fcljn) AS fcljn
        , SUM(fshjqn) AS fshjqn, SUM(fclqn) AS fclqn
        , SUM(fshjjn) - SUM(fshjqn) AS fhjtqzj
        , SUM(fcljn) - SUM(fclqn) AS fcltqzj
        FROM (
        SELECT fnsrmc as 纳税人名称,fnsrsbh, round(sum(fshj)/10000,2) fshjjn,round(sum(fqxj+fdsj)/10000,2) fcljn, 0 AS fshjqn
        , 0 AS fclqn
        FROM tb_dm_cyjs_ssmd_main
        WHERE 1 = 1
        and frkrq between TO_DATE(CONCAT(#{start_time},'-01'), 'YYYY-MM-DD') and LAST_DAY(TO_DATE(#{end_time}, 'YYYY-MM'))
            and fnsrmc in(
            select nsrmc from tb_dw_cyjs_nsrmczjb_cfg
                where parkid in(
                select fid from tb_dw_cyjs_park_cfg
                where 1=1
                <if test="fyqmc!= null and fyqmc!= ''">
                    and aliasname=#{fyqmc}
                </if>
                )
            <if test="fqyrk!=null and fqyrk!=''">
                AND year = #{fqyrk}
            </if>
            )
        <if test="fsz != null and fsz != ''">
            and fzsxm in
            <foreach item='fsz' index="fsz" collection="fsz.split(',')" open="(" separator="," close=")">
                #{fsz}
            </foreach>
        </if>

        GROUP BY fnsrmc,fnsrsbh
        UNION all
        SELECT fnsrmc as 纳税人名称,fnsrsbh, 0 AS fshjjn, 0 AS fcljn, round(sum(fshj)/10000,2)fshjqn,round(sum(fqxj+fdsj)/10000,2) fclqn
        FROM tb_dm_cyjs_ssmd_main
        WHERE 1 = 1
        and frkrq between ADD_MONTHS(TO_DATE(CONCAT(#{start_time},'-01'), 'YYYY-MM-DD'), -12) and ADD_MONTHS(LAST_DAY(TO_DATE(#{end_time}, 'YYYY-MM')), -12)
            and fnsrmc in(
            select nsrmc from tb_dw_cyjs_nsrmczjb_cfg
                where parkid in(
                select fid from tb_dw_cyjs_park_cfg
                where 1=1
                <if test="fsdmc!= null and fsdmc!= ''">
                    and aliasname=#{fsdmc}
                </if>
                )
            <if test="fqyrk!=null and fqyrk!=''">
                AND year = #{fqyrk}
            </if>
            )
        <if test="fsz != null and fsz != ''">
            and fzsxm in
            <foreach item='fsz' index="fsz" collection="fsz.split(',')" open="(" separator="," close=")">
                #{fsz}
            </foreach>
        </if>
        GROUP BY fnsrmc,fnsrsbh
        )a
        where 1=1
        <if test="fnsrmc!=null and fnsrmc!=''">
            and 纳税人名称 =#{fnsrmc}
        </if>
        GROUP BY 纳税人名称,fnsrsbh
        ) b ON a.nsrmc = b.纳税人名称
        GROUP BY rollup((nsrmc))
        order by case when nsrmc='合计' then 0 else 1 end,fhjtqzj desc
    </select>

    <select id="getAllYq" resultType="map">
        SELECT FID value, ALIASNAME name FROM TB_DW_CYJS_PARK_CFG WHERE ALIASNAME IS NOT NULL AND ALIASNAME != ''
    </select>
</mapper>
