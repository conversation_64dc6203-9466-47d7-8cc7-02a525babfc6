<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.yjfx.mapper.HotelIndustryAlertsMapper">

    <!--酒店预警  getHotelData-->
    <select id="getHotelData_fb" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select decode(grouping(fnsrmc),1,'合计',fnsrmc) fnsrmc
        ,decode(grouping(fnsrmc),1,'',max(fswsbh)) fswsbh
        ,decode(grouping(fnsrmc),1,'',max(fswssjg)) fswssjg
        ,decode(grouping(fnsrmc),1,'',max(fscjydz)) fscjydz
        ,round(ifnull(sum(fzsrc),0),2) fzsrc
        ,round(ifnull(sum(fcws),0),2) fcws
        ,round(ifnull(sum(fyjys),0),2) fyjys
        ,round(ifnull(sum(fyjzzs),0),2) fyjzzs
        ,round(ifnull(sum(fyjqysds),0),2) fyjqysds
        ,round(ifnull(sum(fyjhjbhs),0),2) fyjhjbhs
        ,round(ifnull(sum(frkzzs),0),2) frkzzs
        ,round(ifnull(sum(frkqysds),0),2) frkqysds
        ,round(ifnull(sum(frkhjbhs),0),2) frkhjbhs
        ,round(ifnull(sum(fyjzzs),0)-ifnull(sum(frkzzs),0),2) frkzzsce
        ,round(ifnull(sum(fyjqysds),0)-ifnull(sum(frkqysds),0),2) frkqysdsce
        ,round(ifnull(sum(fyjhjbhs),0)-ifnull(sum(frkhjbhs),0),2) frkhjbhsce
        ,round(ifnull(sum(fsbzzs),0),2) fsbzzs
        ,round(ifnull(sum(fsbzzs),0)-ifnull(sum(fyjys),0),2) fsbce from
        (select fswdjmc fnsrmc,max(fswnsrsbh) fswsbh,sum(fzsrc) fzsrc,
        sum(fcws) fcws,sum(fzsrc)/2*IFNULL(scjg.fscjg,${frjy}) fyjys,
        sum(fzsrc)/2*IFNULL(scjg.fscjg,${frjy})/(1+decode(fnsrlx,'一般纳税人',${fzdysl},${fzdyslxgm})/100.0)*decode(fnsrlx,'一般纳税人',${fzdysl},${fzdyslxgm})/100.0 fyjzzs,
        sum(fzsrc)/2*IFNULL(scjg.fscjg,${frjy})*${fzdysl}/100.0 fyjqysds,
        (((#{fyear}-#{fyear_e})*12)+(#{fmonth_e}-#{fmonth_s}+1))*sum(fcws)*${fhbsse} fyjhjbhs
        from tb_dw_yj_bzjdxx_gen jdxx
        left join tb_ods_zhzs_ldscjgxgxx scjg on scjg.fdjmc = jdxx.fswdjmc
        WHERE CONCAT(fnf, '-', fyf) between concat(#{fyear},'-',#{fmonth_s}) and
        concat(#{fyear_e},'-',#{fmonth_e})
        <if test="fnsrmc != null and fnsrmc != ''">and
            fswdjmc like CONCAT('%' , #{fnsrmc} , '%')
        </if>
        <if test="fshxydm != null and fshxydm != ''">and
            fswnsrsbh like CONCAT('%' , #{fshxydm} , '%')
        </if>
        group by fswdjmc)mdb
        left join
        (select fnsrmc 纳税人名称,sum(fzzs) frkzzs,sum(fqysds) frkqysds,sum(fhjbhs) frkhjbhs from
        tb_dm_zhzs_ssmd_main
        WHERE fskssqq BETWEEN to_date(concat(#{fyear},'-',#{fmonth_s},'-01'), 'yyyy-mm-dd') AND
        last_day(to_date(concat(#{fyear_e},'-',#{fmonth_e},'-01'), 'yyyy-mm-dd'))
        /*酒店预警:取征收品目为住宿服务*/
        /*and fzspm = '住宿服务'*/
        group by fnsrmc)rk
        on mdb.fnsrmc=rk.纳税人名称
        left join
        (select fsbnsrmc 纳税人名称,sum(fzzs) fsbzzs from tb_dm_yj_sb_main
        WHERE fskssqq BETWEEN to_date(concat(#{fyear},'-',#{fmonth_s},'-01'), 'yyyy-mm-dd') AND
        last_day(to_date(concat(#{fyear_e},'-',#{fmonth_e},'-01'), 'yyyy-mm-dd'))
        group by fsbnsrmc)sb
        on mdb.fnsrmc=sb.纳税人名称
        left join tb_dm_yj_swdj_main dj on mdb.fnsrmc=dj.fswdjnsrmc
        where 1=1
            <if test="fqzd != null and fqzd != ''">and
                fyjys &gt;= ${fqzd}
            </if>
            <if test="fssqyStr != null and fssqyStr != ''">and
                fswssjg in (${fssqyStr})
            </if>

        group by rollup(fnsrmc)
        order by decode(fnsrmc,'合计',1,2),CASE WHEN fswsbh IS NULL THEN 1 WHEN fswsbh = '' THEN 1 ELSE 0 END,case substr(fnsrmc, -4, 4) when '_未匹配' then 2 else 1 end,fyjys desc

    </select>


    <select id="getHotelPriceData" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
            flgmc,flgdm,fscjg,fdjmc,fdjdm,fjddz,frzrc, f_sys_year fnf , f_sys_month fyf
        from tb_ods_zhzs_ldscjgxgxx
        <where>
            <if test="flgmc != null and flgmc != ''">
                and flgmc like CONCAT('%' , #{flgmc} , '%')
            </if>
            <if test="fscjg != null and fscjg != ''">
                and fscjg >= ${fscjg}
            </if>
        </where>
    </select>
</mapper>
