<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.yjfx.mapper.DrugstoreIndustryAlertsMapper">

    <sql id="getYdSubjectList_template">
        select * from (
        select decode(grouping(fnsrmc),1,'合计',fnsrmc) fnsrmc
        ,decode(grouping(fnsrmc),1,'',max(fyfzd)) fyfzd
        ,decode(grouping(fnsrmc),1,'',max(fswsbh)) fswsbh
        ,decode(grouping(fnsrmc),1,'',max(fswssjg)) fswssjg
        ,decode(grouping(fnsrmc),1,'',max(fscjydz)) fscjydz
        ,round(ifnull(sum(fskje),0),2) fskje
        ,round(ifnull(sum(fyjys),0),2) fyjys
        ,round(ifnull(sum(fyjzzs),0),2) fyjzzs
        ,round(ifnull(sum(frkzzs),0),2) frkzzs
        ,round(ifnull(sum(fyjzzs),0)-ifnull(sum(frkzzs),0),2) frkce
        ,round(ifnull(sum(fsbzzs),0),2) fsbzzs
        ,round(ifnull(sum(fsbzzs),0)-ifnull(sum(fyjys),0),2) fsbce from
        (select fnsrmc fnsrmc,ifnull(fyfzd,'其他') fyfzd,max(fswnsrsbh) fswsbh,sum(ifnull(fskjey,0)) fskje,
         case when ${fskzb}=0 then 0 else sum(ifnull(fskjey,0))/(${fskzb}/100.00) end fyjys,
         case when ${fskzb}=0 or ${fzdysl}=0 then 0 else sum(ifnull(fskjey,0))/(${fskzb}/100.00)/(1+${fzdysl}/100.0)*${fzdysl}/100.0 end fyjzzs
        from  (
           select distinct fnsrmc,fyfzd
           from (
             select fswdjmc fnsrmc,fyfzd from tb_dw_yj_bzydxx_gen
             left join tb_ods_zhzs_lzfzydxxb on fswdjmc = fydfd
             union all
             select fydfd fnsrmc,fyfzd from tb_ods_zhzs_lzfzydxxb
           )
        )
        left join tb_dw_yj_bzydxx_gen on fnsrmc = fswdjmc
        WHERE (CONCAT(fnf, '-', fyf) between concat(#{fyear},'-',#{fmonth_s}) and
        concat(#{fyear_e},'-',#{fmonth_e})  or fnf is null or fyf is null)
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc like CONCAT('%' , #{fnsrmc} , '%')
        </if>
        <if test="fzdmcList != null and  fzdmcList.size>0">and
            fnsrmc in (
                select FYDFD from TB_ODS_ZHZS_LZFZYDXXB
                where FYFZD in
                <foreach collection="fzdmcList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            )
        </if>
        <if test="fshxydm != null and fshxydm != ''">and
            fswnsrsbh like CONCAT('%' , #{fshxydm} , '%')
        </if>
        group by fnsrmc)mdb
        left join
        (select fnsrmc 纳税人名称,sum(fzzs) frkzzs from tb_dm_zhzs_ssmd_main
        WHERE fskssqq BETWEEN to_date(concat(#{fyear},'-',#{fmonth_s},'-01'), 'yyyy-mm-dd') AND
        last_day(to_date(concat(#{fyear_e},'-',#{fmonth_e},'-01'), 'yyyy-mm-dd'))
        group by fnsrmc)rk
        on mdb.fnsrmc=rk.纳税人名称
        left join
        (select fsbnsrmc 纳税人名称,sum(fzzs) fsbzzs from tb_dm_yj_sb_main
        WHERE fskssqq BETWEEN to_date(concat(#{fyear},'-',#{fmonth_s},'-01'), 'yyyy-mm-dd') AND
        last_day(to_date(concat(#{fyear_e},'-',#{fmonth_e},'-01'), 'yyyy-mm-dd'))
        group by fsbnsrmc)sb
        on mdb.fnsrmc=sb.纳税人名称
        left join tb_dm_yj_swdj_main dj on mdb.fnsrmc=dj.fswdjnsrmc
        where 1=1
            <if test="fqzd != null and fqzd != ''">and
                fyjys &gt;= ${fqzd}
            </if>
            <if test="fssqyList != null and fssqyList.size>0">
                and fswssjg in
                <foreach collection="fssqyList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="fyjkzs != null and fyjkzs != '' ">and
                round(ifnull(fyjzzs,0)-ifnull(frkzzs,0),2) &gt;= ${fyjkzs}
            </if>
        group by rollup(fnsrmc)
        ) tmp where 1=1

        order by decode(fnsrmc,'合计',1,2),CASE WHEN fswsbh IS NULL THEN 1 WHEN fswsbh = '' THEN 1 ELSE 0 END,case substr(fnsrmc, -4, 4) when '_未匹配' then 2 else 1 end,frkce desc

    </sql>


    <!--药店预警  -->
    <select id="getYdSubject_fb" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        with t_ydyjfd as (
            <include refid="getYdSubjectList_template"/>
        )
        select * from t_ydyjfd
        where 1=1
        <if test="queryzd == null or queryzd == '' ">
          <!--非总店查询时不包含 纳税人识别号非空的  -->
            and fswsbh != '' and fswsbh is not null
        </if>
    </select>

    <!--药店预警 根据总店查询 -->
    <select id="getYdSubjectByZd_fb" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        with t_ydyjfd as (
            <include refid="getYdSubjectList_template"/>
        )
        select IF(fnsrmc='合计',fnsrmc,FYFZD) fnsrmc,
               '' fswsbh,
               '' fswssjg,
               '' fscjydz,
               sum(fskje) fskje,
               sum(fyjys) fyjys,
               sum(fyjzzs) fyjzzs,
               sum(frkzzs) frkzzs,
               sum(frkce) frkce,
               sum(fsbzzs) fsbzzs,
               sum(fsbce) fsbce
        from t_ydyjfd
        group by FYFZD

        order by decode(fnsrmc, '合计', 1, 2),decode(fnsrmc, '其他', 2, 1), CASE WHEN fswsbh IS NULL THEN 1 WHEN fswsbh = '' THEN 1 ELSE 0 END,
                 case substr(fnsrmc, -4, 4) when '_未匹配' then 2 else 1 end, frkce desc
    </select>


</mapper>
