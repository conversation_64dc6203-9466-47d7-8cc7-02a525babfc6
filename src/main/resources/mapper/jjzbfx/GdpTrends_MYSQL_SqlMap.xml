<?xml version="1.0" encoding="UTF-8"?><!--

    Copyright 2004-2024 the original author or authors.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

--><!--Converted at: Thu Sep 12 15:51:19 CST 2024-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.jjzbfx.mapper.GdpTrendsMapper">

    <select id="getGdplnjjzbdbtlx" parameterType="com.hnbp.local.jjzbfx.model.GdpParameterQuery"
            resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        SELECT replace(fxm, '（亿元）', '') legend,
        sum(fkmz) yAxis,
        fnf xAxis,
        'bar' type
        FROM tb_dw_zhzs_hgjjzbxm_gen
        WHERE fcsjc in ('本地区', '同类型' )
        and fnf &gt;= #{start_year}
        and fnf &lt;= #{fyear}
        <if test="fqx == null or fqx == ''">
            AND fcsjc = '本地区'
        </if>
        <if test="fregion != null and fregion != ''">
            and fcs in
            <foreach collection="fregionList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fjjzb != null ">
            and fxm in
            <foreach collection="fjjzb" item="item" separator="," close=")" open="(">
                concat(#{item},'（亿元）')
            </foreach>
        </if>
        <if test="fjjzb == null or fjjzb == ''">
            and fxm in ('GDP（亿元）', '税收收入（亿元）', '地方一般公共预算收入（亿元）', '第一产业（亿元）', '第二产业（亿元）',
            '第三产业（亿元）')
        </if>
        GROUP BY fnf, fxm
        union all
        SELECT '地方一般公共预算收入占比' legend,
        round(sum(case when fxm = '地方一般公共预算收入（亿元）' then fkmz else 0 end) / sum(case
        when fxm = 'GDP（亿元）'
        then fkmz
        else 0 end) * 100,
        2) yAxis,
        fnf xAxis,
        'line' type
        FROM tb_dw_zhzs_hgjjzbxm_gen
        WHERE fcsjc in ('本地区', '同类型' )
        and fxm in ('GDP（亿元）', '地方一般公共预算收入（亿元）')
        <if test="fjjzb != null ">and
            '地方一般公共预算收入占比' in
            <foreach collection="fjjzb" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fnf &gt;= #{start_year}
        and fnf &lt;= #{fyear}
        <if test="fqx == null or fqx == ''">
            and fcsjc = '本地区'
        </if>
        <if test="fregion != null and fregion != ''">
            and fcs in
            <foreach collection="fregionList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        GROUP BY fnf,fcs
        order by xAxis,legend
    </select>

    <select id="getGdpcyjgtlx" parameterType="com.hnbp.local.jjzbfx.model.GdpParameterQuery" resultType="java.util.HashMap">
        select tmp.*,
               row_number() over(order by tmp.fgdp desc) pm
        from (select fcs,
                     sum(case when fxm = 'GDP（亿元）' then fkmz else 0 end)                  fgdp,
                     sum(case when fxm = 'GDP（亿元）' then fzs else 0 end)                   fgdpzs,
                     sum(case when fxm = '第一产业（亿元）' then fkmz else 0 end)             fdy,
                     sum(case when fxm = '第二产业（亿元）' then fkmz else 0 end)             fde,
                     sum(case when fxm = '第三产业（亿元）' then fkmz else 0 end)             fds,
                     sum(case when fxm = '地方一般公共预算收入（亿元）' then fkmz else 0 end) fyssr,
                     sum(case when fxm = '税收收入（亿元）' then fkmz else 0 end) fsssr,
                     sum(case when fxm = '税占比' then fkmz else 0 end) fszb,
                     round(sum(case when fxm = '地方一般公共预算收入（亿元）' then fkmz else 0 end) / sum(case
                                                                                                when fxm = 'GDP（亿元）'
                                                                                                    then fkmz
                                                                                                else 0 end) *
                           100, 2)                                                          fyssrzb,
                     sum(case when fxm = '常住人口（万人）' then fkmz else 0 end)             fczrk,
                     sum(case when fxm = '人均（元）' then fkmz else 0 end)                   frjgdp
              from tb_dw_zhzs_hgjjzbxm_gen
              where fnf = #{ftime}
                and fcsjc in ('本地区', '同类型')
                <if test="fregion != null and fregion != ''">
                    and fcs in
                    <foreach collection="fregionList" item="item" separator="," close=")" open="(">
                        #{item}
                    </foreach>
                </if>
              group by fcs) tmp
        order by fgdp desc
    </select>

    <select id="getGdpdqjjzbdbtlx" parameterType="com.hnbp.local.jjzbfx.model.GdpParameterQuery"
            resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        SELECT replace(fxm, '（亿元）', '') legend,
               sum(fkmz)                  yAxis,
               fcs                        xAxis,
               'bar'                      type
        FROM tb_dw_zhzs_hgjjzbxm_gen
        WHERE fcsjc in ('本地区', '同类型')
          AND fnf = #{ftime}
          AND fxm in ('GDP（亿元）', '税收收入（亿元）', '地方一般公共预算收入（亿元）', '第一产业（亿元）', '第二产业（亿元）',
                      '第三产业（亿元）')
        <if test="fregion != null and fregion != ''">
            and fcs in
            <foreach collection="fregionList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        GROUP BY fcs, fxm
        union all
        SELECT '地方一般公共预算收入占比' legend,
               round(sum(case when fxm = '地方一般公共预算收入（亿元）' then fkmz else 0 end) / sum(case
                                                                                          when fxm = 'GDP（亿元）'
                                                                                              then fkmz
                                                                                          else 0 end) * 100,
                     2)                   yAxis,
               fcs                        xAxis,
               'line'                     type
        FROM tb_dw_zhzs_hgjjzbxm_gen
        WHERE fcsjc in ('本地区', '同类型')
          AND fnf = #{ftime}
          AND fxm in ('GDP（亿元）', '地方一般公共预算收入（亿元）')
        <if test="fregion != null and fregion != ''">
            and fcs in
            <foreach collection="fregionList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        GROUP BY fcs
        order by xAxis, legend
    </select>

    <select id="getGdplnjjzbdbtlx2" parameterType="com.hnbp.local.jjzbfx.model.GdpParameterQuery"
            resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        SELECT fcs legend,
        sum(fkmz) yAxis,
        fnf xAxis,
        'bar' type
        FROM tb_dw_zhzs_hgjjzbxm_gen
        WHERE fcsjc in ('本地区', '同类型' )
        and fnf &gt;= #{start_year}
        and fnf &lt;= #{fyear}
        and fxm = concat(#{fqx},'（亿元）')
        <if test="fregion != null and fregion != ''">
            and fcs in
            <foreach collection="fregionList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        GROUP BY fnf, fcs
        union all
        SELECT fcs legend,
        case when sum(case
        when fxm = 'GDP（亿元）'
        then fkmz
        else 0 end) = 0 then 100 else
        round(sum(case when fxm = '地方一般公共预算收入（亿元）' then fkmz else 0 end) / sum(case
        when fxm = 'GDP（亿元）'
        then fkmz
        else 0 end) * 100,
        2) end yAxis,
        fnf xAxis,
        'line' type
        FROM tb_dw_zhzs_hgjjzbxm_gen
        WHERE fcsjc in ('本地区', '同类型' )
        and fnf &gt;= #{start_year}
        and fnf &lt;= #{fyear}
        and '地方一般公共预算收入占比' = #{fqx}
        <if test="fregion != null and fregion != ''">
            and fcs in
            <foreach collection="fregionList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        GROUP BY fnf, fcs
        order by xAxis,legend
    </select>

    <select id="getGdphyjjsjtlx" parameterType="com.hnbp.local.jjzbfx.model.GdpParameterQuery"
            resultType="java.util.HashMap">
        select tmp.fcs,
               sum(case when fxmmc = '工业增加值' then fkmz else 0 end)         fgy,
               sum(case when fxmmc = 'GDP（亿元）' then fkmz else 0 end)          fgdp,
               sum(case when fxmmc = '建筑业增加值' then fkmz else 0 end)       fjzy,
               sum(case when fxmmc = '房地产业增加值' then fkmz else 0 end)     ffdcy,
               sum(case when fxmmc = '批发零售业增加值' then fkmz else 0 end)   fpflsy,
               sum(case when fxmmc = '金融业增加值' then fkmz else 0 end)       fjry,
               sum(case when fxmmc = '住宿和餐饮业增加值' then fkmz else 0 end) fzscyy,
               sum(case when fxmmc = '房地产销售额（亿元）' then fkmz else 0 end) ffdcxse,
               sum(case when fxmmc = '车辆保有量（万辆）' then fkmz else 0 end)   fclbyl
        from (select concat(fhyml, '增加值') fxmmc,
                     fkmz,
                     fcs
              from tb_dw_zhzs_hgjjzbhy_gen
              where fhyml in ('工业', '建筑业', '房地产业', '批发零售业', '金融业', '住宿和餐饮业')
                and fnf = #{ftime}
                and fcsjc in ('本地区', '同类型')
                <if test="fregion != null and fregion != ''">
                    and fcs in
                    <foreach collection="fregionList" item="item" separator="," close=")" open="(">
                        #{item}
                    </foreach>
                </if>
              union all
              select fxm fxmmc,
                     fkmz,
                     fcs
              from tb_dw_zhzs_hgjjzbxm_gen
              where fxm in ('房地产销售额（亿元）', '车辆保有量（万辆）', 'GDP（亿元）')
                and fnf = #{ftime}
                and fcsjc in ('本地区', '同类型')
                <if test="fregion != null and fregion != ''">
                    and fcs in
                    <foreach collection="fregionList" item="item" separator="," close=")" open="(">
                        #{item}
                    </foreach>
                </if>
              ) tmp
        group by fcs
        ORDER BY fgdp DESC
    </select>

    <select id="getGdpdqhyjjzbdbtlx" parameterType="com.hnbp.local.jjzbfx.model.GdpParameterQuery"
            resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        select concat(fhyml, '增加值') legend,
               fkmz                    yAxis,
               fcs                     xAxis,
               'bar'                   type
        from tb_dw_zhzs_hgjjzbhy_gen
        where fhyml in ('工业', '建筑业', '房地产业', '批发零售业', '金融业', '住宿和餐饮业')
          and fnf = #{ftime}
          and fcsjc in ('本地区', '同类型')
          <if test="fregion != null and fregion != ''">
              and fcs in
              <foreach collection="fregionList" item="item" separator="," close=")" open="(">
                  #{item}
              </foreach>
          </if>
        union all
        select fxm   legend,
               fkmz  yAxis,
               fcs   xAxis,
               'bar' type
        from tb_dw_zhzs_hgjjzbxm_gen
        where fxm in ('房地产销售额', '车辆保有量')
          and fnf = #{ftime}
          and fcsjc in ('本地区', '同类型')
          <if test="fregion != null and fregion != ''">
              and fcs in
              <foreach collection="fregionList" item="item" separator="," close=")" open="(">
                  #{item}
              </foreach>
          </if>
        order by xAxis, legend
    </select>

    <select id="getGdplnhyjjzbdbtlx" parameterType="com.hnbp.local.jjzbfx.model.GdpParameterQuery"
            resultType="com.hnbp.local.util.echarts.Bar.BarSeriesInitData">
        select concat(fhyml,'增加值') legend,
        fkmz yAxis,
        fnf xAxis,
        'bar' type
        from tb_dw_zhzs_hgjjzbhy_gen
        where fcsjc in ('本地区', '同类型' )
        <if test="fjjzb != null">and
            fhyml in
            <foreach collection="fjjzb" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fjjzb == null or fjjzb == ''">and
            fhyml in ('工业','建筑业','房地产业','批发零售业','金融业','住宿和餐饮业')
        </if>
        and fnf &gt;= #{start_year}
        and fnf &lt;= #{fyear}
        <if test="fqx == null or fqx == ''">AND
            fcsjc = '本地区'
        </if>
        <if test="fregion != null and fregion != ''">
            and fcs in
            <foreach collection="fregionList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        union all
        select fxm legend,
        fkmz yAxis,
        fnf xAxis,
        'bar' type
        from tb_dw_zhzs_hgjjzbxm_gen
        where fnf &gt;= #{start_year}
        and fnf &lt;= #{fyear}
        <if test="fjjzb != null ">and
            fxm in
            <foreach collection="fjjzb" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fjjzb == null or fjjzb == ''">and
            fxm in ('房地产销售额','车辆保有量')
        </if>

        <if test="fqx == null or fqx == ''">AND
            fcsjc = '本地区'
        </if>
        <if test="fregion != null and fregion != ''">
            and fcs in
            <foreach collection="fregionList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fcsjc in ('本地区', '同类型' )
        order by xAxis,legend
    </select>

</mapper>
