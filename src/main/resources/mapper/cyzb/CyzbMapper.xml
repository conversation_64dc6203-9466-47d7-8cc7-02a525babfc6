<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.cyzb.mapper.CyzbMapper">
    <select id="getIndustrial" parameterType="map" resultType="map">
		WITH
		ss AS (
			SELECT
				CASE WHEN FHYML IN ('制造业', '采矿业', '电力、热力、燃气及水生产和供应业') THEN '工业' ELSE FHYML END FHYML
				, SUM(CASE WHEN FNF = #{fyear} THEN FHJ END) FSSJN
				, SUM(CASE WHEN FNF = #{fyear}-1 THEN FHJ END) FSSQN
			FROM tb_dw_srfx_srfx_main
			WHERE FZSXM IN ('增值税', '企业所得税', '契税', '土地增值税', '耕地占用税', '资源税', '房产税','城市维护建设税', '车辆购置税', '城镇土地使用税', '车船税', '个人所得税','烟叶税', '印花税', '船舶吨税', '消费税', '环境保护税', '营业税')
			AND FHYML IN ('制造业', '采矿业', '电力、热力、燃气及水生产和供应业', '建筑业', '批发和零售业', '交通运输、仓储和邮政业', '住宿和餐饮业', '金融业', '房地产业')
			AND FNF IN (#{fyear}, #{fyear}-1)
			AND FYF &lt;= <choose>
							<when test='fjd==1'>'03'</when>
							<when test='fjd==2'>'06'</when>
							<when test='fjd==3'>'09'</when>
							<when test='fjd==4'>'12'</when>
						</choose>
			<if test='fssqy != null and fssqy!= "" and fssqy.split(",").size()>0'>
				AND FSKGK IN
				<foreach collection='fssqy.split(",")' item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			GROUP BY CASE WHEN FHYML IN ('制造业', '采矿业', '电力、热力、燃气及水生产和供应业') THEN '工业' ELSE FHYML END
		)
		, cyzb AS (
			SELECT FCYMC
				, SUM(CASE WHEN FNF = #{fyear} THEN FCYZZ ELSE 0 END) FZLJN
				, SUM(CASE WHEN FNF = #{fyear}-1 THEN FCYZZ ELSE 0 END) FZLQN
			FROM TB_DW_CYJS_CYZB_GEN
			WHERE FNF IN (#{fyear}, #{fyear}-1)
			AND FYF = <choose>
							<when test='fjd==1'>'03'</when>
							<when test='fjd==2'>'06'</when>
							<when test='fjd==3'>'09'</when>
							<when test='fjd==4'>'12'</when>
					</choose>
			GROUP BY FCYMC
		)
		SELECT
			FCYMC
		 , ROUND(FSSJN, 2)           FSSHJJN
		 , ROUND(FSSQN, 2)           FSSHJQN
		 , ROUND(FZLJN, 2)           FZLJN
		 , ROUND(FZLQN, 2)           FZLQN
		 , ROUND(FJNZB, 2)           FHSLJN
		 , ROUND(FQNZB, 2)           FHSLQN
		 , ROUND((FJNZB - FQNZB), 2) ZBZF
		FROM (
		SELECT
			FCYMC, FSSJN, FSSQN, FZLJN, FZLQN
		   , CASE WHEN FZLJN = 0 THEN NULL ELSE FSSJN / FZLJN * 100 END FJNZB
		   , CASE WHEN FZLQN = 0 THEN NULL ELSE FSSQN / FZLQN * 100 END FQNZB
		FROM (
			SELECT
				CASE WHEN GROUPING(cyzb.FCYMC)=1 THEN '合计' ELSE cyzb.FCYMC END FCYMC
				, SUM(FSSJN)/100000000     FSSJN
				, SUM(FSSQN)/100000000     FSSQN
				, SUM(NVL(FZLJN, 0))       FZLJN
				, SUM(NVL(FZLQN, 0))       FZLQN
			FROM cyzb LEFT JOIN ss ON cyzb.FCYMC = ss.FHYML
			GROUP BY ROLLUP(cyzb.FCYMC)
			ORDER BY GROUPING(cyzb.FCYMC) DESC,
				CASE
					WHEN cyzb.FCYMC='工业' THEN 1
					WHEN cyzb.FCYMC='建筑业' THEN 2
					WHEN cyzb.FCYMC='批发和零售业' THEN 3
					WHEN cyzb.FCYMC='交通运输、仓储和邮政业' THEN 4
					WHEN cyzb.FCYMC='住宿和餐饮业' THEN 5
					WHEN cyzb.FCYMC='金融业' THEN 6
					WHEN cyzb.FCYMC='房地产业' THEN 7
				END ASC
		))
    </select>

	<select id="getjczbGDPbar" parameterType="map" resultType="map">
		WITH
		ss AS (
			SELECT
				CASE WHEN FHYML IN ('制造业', '采矿业', '电力、热力、燃气及水生产和供应业') THEN '工业' ELSE FHYML END FHYML
				, FNF
				, SUM(FHJ) FHJ
			FROM tb_dw_srfx_srfx_main
			WHERE FZSXM IN ('增值税', '企业所得税', '契税', '土地增值税', '耕地占用税', '资源税', '房产税','城市维护建设税', '车辆购置税', '城镇土地使用税', '车船税', '个人所得税','烟叶税', '印花税', '船舶吨税', '消费税', '环境保护税', '营业税')
			AND FHYML IN ('制造业', '采矿业', '电力、热力、燃气及水生产和供应业', '建筑业', '批发和零售业', '交通运输、仓储和邮政业', '住宿和餐饮业', '金融业', '房地产业')
			<if test="fsshy != null and fsshy != ''">
				<choose>
					<when test='fsshy == "工业"'>
						AND FHYML IN ('制造业', '采矿业', '电力、热力、燃气及水生产和供应业')
					</when>
					<otherwise>
						AND FHYML = #{fsshy}
					</otherwise>
				</choose>
			</if>
			AND FNF BETWEEN #{fyear}-4 AND #{fyear}
			AND FYF &lt;= <choose>
							<when test='fjd==1'>'03'</when>
							<when test='fjd==2'>'06'</when>
							<when test='fjd==3'>'09'</when>
							<when test='fjd==4'>'12'</when>
					</choose>
			GROUP BY CASE WHEN FHYML IN ('制造业', '采矿业', '电力、热力、燃气及水生产和供应业') THEN '工业' ELSE FHYML END, FNF
		)
		, cyzb AS (
			SELECT FCYMC, FNF, SUM(FCYZZ) FCYZZ
			FROM TB_DW_CYJS_CYZB_GEN
			WHERE FCYMC IN ('工业', '建筑业', '批发和零售业', '交通运输、仓储和邮政业', '住宿和餐饮业', '金融业', '房地产业')
			<if test="fsshy != null and fsshy != ''">
				AND FCYMC = #{fsshy}
			</if>
			AND FNF BETWEEN #{fyear}-4 AND #{fyear}
			AND FYF = <choose>
							<when test='fjd==1'>'03'</when>
							<when test='fjd==2'>'06'</when>
							<when test='fjd==3'>'09'</when>
							<when test='fjd==4'>'12'</when>
					</choose>
			GROUP BY FCYMC, FNF
		)
		SELECT ss.FNF 年份, ROUND(ss.FHJ/100000000, 2) 税收完成情况, ROUND(cyzb.FCYZZ, 2) GDP, CASE WHEN FCYZZ=0 THEN NULL ELSE ROUND((FHJ/100000000)/FCYZZ*100, 2) END 含税量
		FROM ss FULL JOIN cyzb ON ss.FHYML=cyzb.FCYMC AND ss.FNF=cyzb.FNF
		ORDER BY ss.FNF ASC
	</select>
</mapper>
