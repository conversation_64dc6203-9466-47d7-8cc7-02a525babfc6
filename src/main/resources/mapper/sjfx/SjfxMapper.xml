<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.sjfx.mapper.SjfxAnalysisMapper">


    <!-- 房产税-表格-->
    <select id="getFcssjfx" resultType="java.util.HashMap" parameterType="com.hnbp.local.sjfx.model.FTlsDTO">
        SELECT
            GROUPING(FSKGK) FGROUPING
            , CASE WHEN GROUPING(FSKGK)=1 THEN '合计' ELSE MAX(FSKGK) END FSKGK
            , SUM(JNNSHS) JNNSHS, SUM(NSHSJN) NSHSJN, ROUND(SUM(FSSJEJN), 2) <PERSON><PERSON>JEJN
            , SUM(QNNSHS) QNNSHS, SUM(NSHSQN) NSHSQN, ROUND(SUM(FSSJEQN), 2) FSSJEQN
            , SUM(DQNNSHS) DQNNSHS, SUM(NSHSDQN) NSHSDQN, ROUND(SUM(FSSJEDQN), 2) FSSJEDQN, ROUND(FDQNHSZB, 2) FDQNHSZB
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND(SUM(NSHSJN) / SUM(JNNSHS)*100, 2) ELSE ROUND(FJNHSZB, 2) END FJNHSZB
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND(SUM(NSHSQN) / SUM(QNNSHS)*100, 2) ELSE ROUND(FQNHSZB, 2) END FQNHSZB
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND((SUM(JNNSHS) - SUM(QNNSHS))/SUM(QNNSHS)*100, 2) ELSE MAX(JNZHSTB) END JNZHSTB
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND((SUM(NSHSJN) - SUM(NSHSQN))/SUM(NSHSQN)*100, 2) ELSE MAX(NSHSBJN) END NSHSBJN
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND((SUM(FSSJEJN) - SUM(FSSJEQN))/SUM(FSSJEQN)*100, 2) ELSE MAX(FCSBJN) END FCSBJN
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND((SUM(QNNSHS) - SUM(DQNNSHS))/SUM(QNNSHS)*100, 2) ELSE MAX(QNZHSTB) END QNZHSTB
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND((SUM(NSHSQN) - SUM(NSHSDQN))/SUM(NSHSQN)*100, 2) ELSE MAX(NSHSBQN) END NSHSBQN
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND((SUM(FSSJEQN) - SUM(FSSJEDQN))/SUM(FSSJEDQN)*100, 2) ELSE MAX(FCSBQN) END FCSBQN
        FROM (
        SELECT *
            , ROUND((JNNSHS - QNNSHS)/QNNSHS*100, 2) JNZHSTB
            , ROUND((NSHSJN - NSHSQN)/NSHSQN*100, 2) NSHSBJN
            , ROUND((FSSJEJN - FSSJEQN)/FSSJEQN*100, 2) FCSBJN
            , ROUND((QNNSHS - DQNNSHS)/QNNSHS*100, 2) QNZHSTB
            , ROUND((NSHSQN - NSHSDQN)/NSHSQN*100, 2) NSHSBQN
            , ROUND((FSSJEQN - FSSJEDQN)/FSSJEDQN*100, 2) FCSBQN
        FROM (
        SELECT
            FSKGK,
            SUM(CASE WHEN FRKRQ = #{ftime} THEN znshs ELSE NULL END) AS JNNSHS,
            SUM(CASE WHEN FRKRQ = #{ftime} THEN fcshs ELSE NULL END) AS NSHSJN,
            SUM(CASE WHEN FRKRQ = #{ftime} THEN ROUND(fcsje/10000,2) ELSE NULL END) AS FSSJEJN,
            SUM(CASE WHEN FRKRQ = #{ftime} THEN fzb ELSE NULL END) AS FJNHSZB,
            SUM(CASE WHEN FRKRQ = #{ftime}-1 THEN znshs ELSE NULL END) AS QNNSHS,
            SUM(CASE WHEN FRKRQ = #{ftime}-1 THEN fcshs ELSE NULL END) AS NSHSQN,
            SUM(CASE WHEN FRKRQ = #{ftime}-1 THEN ROUND(fcsje/10000,2) ELSE NULL END) AS FSSJEQN,
            SUM(CASE WHEN FRKRQ = #{ftime}-1 THEN fzb ELSE NULL END) AS FQNHSZB,
            SUM(CASE WHEN FRKRQ = #{ftime}-2 THEN znshs ELSE NULL END) AS DQNNSHS,
            SUM(CASE WHEN FRKRQ = #{ftime}-2 THEN fcshs ELSE NULL END) AS NSHSDQN,
            SUM(CASE WHEN FRKRQ = #{ftime}-2 THEN ROUND(fcsje/10000,2) ELSE NULL END) AS FSSJEDQN,
            SUM(CASE WHEN FRKRQ = #{ftime}-2 THEN fzb ELSE NULL END) AS FDQNHSZB
        FROM (
            SELECT
                zhs.fskgk, zhs.FRKRQ, znshs, fcsje, zhs.fcshs, ROUND(zhs.fcshs / znshs * 100, 2) AS fzb
            FROM (
                SELECT
                    a.fskgk AS fskgk, b.FRKRQ, COUNT(DISTINCT a.fnsrmc) AS znshs,
                               COUNT(DISTINCT CASE WHEN FCS_COUNT >= 1 THEN a.FNSRMC END) fcshs
                FROM TB_DM_ZHZS_NSRMCSKGK_GEN a
                INNER JOIN (
                    SELECT
                        fnsrmc,
                        YEAR(FRKRQ) AS FRKRQ,
                        SUM(CASE WHEN FZSXM = '房产税' THEN 1 ELSE 0 END) FCS_COUNT
                    FROM LANZHOU.TB_DM_ZHZS_DQSZHYNYMC_MAIN
                    WHERE LENGTH(FNSRMC) > 4
                    AND FRKRQ BETWEEN TO_DATE(CONCAT(#{ftime}-2,'-01-01'), 'YYYY-MM-DD') AND LAST_DAY(TO_DATE(CONCAT(#{ftime},'-12'), 'YYYY-MM'))
                    GROUP BY fnsrmc, YEAR(FRKRQ)
                    <if test="fzhsss!=null and fzhsss!=''">
                        HAVING SUM(fshj) > ${fzhsss}*10000
                    </if>
                ) b ON a.fnsrmc = b.fnsrmc
                <where>
                    <if test="fssqy != null and fssqy !=''">
                        AND a.fskgk IN
                        <foreach item="fssqy" collection="fssqy.split(',')" index="index" open="(" separator="," close=")">
                            #{fssqy}
                        </foreach>
                    </if>
                </where>
                GROUP BY a.fskgk, FRKRQ
            ) zhs
            LEFT JOIN (
                SELECT
                    a.fskgk,
                    YEAR(a.FRKRQ) AS FRKRQ,
                    SUM(fhj) AS fcsje,
                    COUNT(DISTINCT fnsrmc) AS fcshs
                FROM TB_DM_ZHZS_DQSZHYNYMC_MAIN a
                WHERE a.FRKRQ BETWEEN TO_DATE(CONCAT(#{ftime}-2,'-01-01'), 'YYYY-MM-DD') AND LAST_DAY(TO_DATE(CONCAT(#{ftime},'-12'), 'YYYY-MM'))
                AND fzsxm = '房产税'
                <if test="fssqy != null and fssqy !=''">
                    AND a.fskgk IN
                    <foreach item="fssqy" collection="fssqy.split(',')" index="index" open="(" separator="," close=")">
                        #{fssqy}
                    </foreach>
                </if>
                GROUP BY a.fskgk, YEAR(a.FRKRQ)
            ) fcshs ON zhs.fskgk = fcshs.fskgk AND zhs.FRKRQ = fcshs.FRKRQ
        )
        GROUP BY FSKGK
        )
        ) GROUP BY ROLLUP(FSKGK) ORDER BY FGROUPING DESC
    </select>

    <!-- 城镇土地使用税-表格-->
    <select id="getCztdsyssjfx" resultType="java.util.HashMap" parameterType="com.hnbp.local.sjfx.model.FTlsDTO">
        SELECT
            GROUPING(FSKGK) FGROUPING
            , CASE WHEN GROUPING(FSKGK)=1 THEN '合计' ELSE MAX(FSKGK) END FSKGK
            , SUM(JNNSHS) JNNSHS, SUM(NSHSJN) NSHSJN, ROUND(SUM(FSSJEJN), 2) FSSJEJN
            , SUM(QNNSHS) QNNSHS, SUM(NSHSQN) NSHSQN, ROUND(SUM(FSSJEQN), 2) FSSJEQN
            , SUM(DQNNSHS) DQNNSHS, SUM(NSHSDQN) NSHSDQN, ROUND(SUM(FSSJEDQN), 2) FSSJEDQN, ROUND(FDQNHSZB, 2) FDQNHSZB
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND(SUM(NSHSJN) / SUM(JNNSHS)*100, 2) ELSE ROUND(FJNHSZB, 2) END FJNHSZB
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND(SUM(NSHSQN) / SUM(QNNSHS)*100, 2) ELSE ROUND(FQNHSZB, 2) END FQNHSZB
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND((SUM(JNNSHS) - SUM(QNNSHS))/SUM(QNNSHS)*100, 2) ELSE MAX(JNZHSTB) END JNZHSTB
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND((SUM(NSHSJN) - SUM(NSHSQN))/SUM(NSHSQN)*100, 2) ELSE MAX(NSHSBJN) END NSHSBJN
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND((SUM(FSSJEJN) - SUM(FSSJEQN))/SUM(FSSJEQN)*100, 2) ELSE MAX(FCSBJN) END FCSBJN
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND((SUM(QNNSHS) - SUM(DQNNSHS))/SUM(QNNSHS)*100, 2) ELSE MAX(QNZHSTB) END QNZHSTB
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND((SUM(NSHSQN) - SUM(NSHSDQN))/SUM(NSHSQN)*100, 2) ELSE MAX(NSHSBQN) END NSHSBQN
            , CASE WHEN GROUPING(FSKGK)=1 THEN ROUND((SUM(FSSJEQN) - SUM(FSSJEDQN))/SUM(FSSJEDQN)*100, 2) ELSE MAX(FCSBQN) END FCSBQN
        FROM (
        SELECT *
            , ROUND((JNNSHS - QNNSHS)/QNNSHS*100, 2) JNZHSTB
            , ROUND((NSHSJN - NSHSQN)/NSHSQN*100, 2) NSHSBJN
            , ROUND((FSSJEJN - FSSJEQN)/FSSJEQN*100, 2) FCSBJN
            , ROUND((QNNSHS - DQNNSHS)/QNNSHS*100, 2) QNZHSTB
            , ROUND((NSHSQN - NSHSDQN)/NSHSQN*100, 2) NSHSBQN
            , ROUND((FSSJEQN - FSSJEDQN)/FSSJEDQN*100, 2) FCSBQN
        FROM (
        SELECT
            FSKGK,
            SUM(CASE WHEN FRKRQ = #{ftime} THEN znshs ELSE NULL END) AS JNNSHS,
            SUM(CASE WHEN FRKRQ = #{ftime} THEN fcshs ELSE NULL END) AS NSHSJN,
            SUM(CASE WHEN FRKRQ = #{ftime} THEN ROUND(fcsje/10000,2) ELSE NULL END) AS FSSJEJN,
            SUM(CASE WHEN FRKRQ = #{ftime} THEN fzb ELSE NULL END) AS FJNHSZB,
            SUM(CASE WHEN FRKRQ = #{ftime}-1 THEN znshs ELSE NULL END) AS QNNSHS,
            SUM(CASE WHEN FRKRQ = #{ftime}-1 THEN fcshs ELSE NULL END) AS NSHSQN,
            SUM(CASE WHEN FRKRQ = #{ftime}-1 THEN ROUND(fcsje/10000,2) ELSE NULL END) AS FSSJEQN,
            SUM(CASE WHEN FRKRQ = #{ftime}-1 THEN fzb ELSE NULL END) AS FQNHSZB,
            SUM(CASE WHEN FRKRQ = #{ftime}-2 THEN znshs ELSE NULL END) AS DQNNSHS,
            SUM(CASE WHEN FRKRQ = #{ftime}-2 THEN fcshs ELSE NULL END) AS NSHSDQN,
            SUM(CASE WHEN FRKRQ = #{ftime}-2 THEN ROUND(fcsje/10000,2) ELSE NULL END) AS FSSJEDQN,
            SUM(CASE WHEN FRKRQ = #{ftime}-2 THEN fzb ELSE NULL END) AS FDQNHSZB
        FROM (
            SELECT
                zhs.fskgk, zhs.FRKRQ, znshs, fcsje, zhs.fcshs, ROUND(zhs.fcshs / znshs  * 100, 2) AS fzb
            FROM (
                SELECT
                    a.fskgk, b.FRKRQ, COUNT(DISTINCT a.fnsrmc) AS znshs,
                               COUNT(DISTINCT CASE WHEN CZTDSYS_COUNT >= 1 THEN a.FNSRMC END) fcshs
                FROM TB_DM_ZHZS_NSRMCSKGK_GEN a
                INNER JOIN (
                    SELECT
                        fnsrmc,
                        YEAR(FRKRQ) AS FRKRQ,
                        SUM(CASE WHEN FZSXM = '城镇土地使用税' THEN 1 ELSE 0 END) CZTDSYS_COUNT
                    FROM LANZHOU.TB_DM_ZHZS_DQSZHYNYMC_MAIN
                    WHERE LENGTH(FNSRMC) > 4
                    AND FRKRQ BETWEEN TO_DATE(CONCAT(#{ftime}-2,'-01-01'), 'YYYY-MM-DD') AND LAST_DAY(TO_DATE(CONCAT(#{ftime},'-12'), 'YYYY-MM'))
                    GROUP BY fnsrmc, YEAR(FRKRQ)
                    <if test="fzhsss!=null and fzhsss!=''">
                        HAVING SUM(fshj) > ${fzhsss}*10000
                    </if>
                ) b ON a.fnsrmc = b.fnsrmc
                <where>
                    <if test="fssqy != null and fssqy !=''">
                        AND a.fskgk IN
                        <foreach item="fssqy" collection="fssqy.split(',')" index="index" open="(" separator="," close=")">
                            #{fssqy}
                        </foreach>
                    </if>
                </where>
                GROUP BY a.fskgk, FRKRQ
            ) zhs
            LEFT JOIN (
                SELECT
                    a.fskgk,
                    YEAR(a.FRKRQ) AS FRKRQ,
                    SUM(fhj) AS fcsje,
                    COUNT(DISTINCT fnsrmc) AS fcshs
                FROM TB_DM_ZHZS_DQSZHYNYMC_MAIN a
                WHERE a.FRKRQ BETWEEN TO_DATE(CONCAT(#{ftime}-2,'-01-01'), 'YYYY-MM-DD') AND LAST_DAY(TO_DATE(CONCAT(#{ftime},'-12'), 'YYYY-MM'))
                AND fzsxm = '城镇土地使用税'
                <if test="fssqy != null and fssqy !=''">
                    AND a.fskgk IN
                    <foreach item="fssqy" collection="fssqy.split(',')" index="index" open="(" separator="," close=")">
                        #{fssqy}
                    </foreach>
                </if>
                GROUP BY a.fskgk, YEAR(a.FRKRQ)
            ) fcshs ON zhs.fskgk = fcshs.fskgk AND zhs.FRKRQ = fcshs.FRKRQ
        )
        GROUP BY FSKGK
        )
        ) GROUP BY ROLLUP(FSKGK) ORDER BY FGROUPING DESC
    </select>

    <select id="getCcssjfx" resultType="java.util.HashMap" parameterType="com.hnbp.local.sjfx.model.FTlsDTO">
        WITH
        base AS (
        SELECT
            1 AS FPX
            , '≤1.0L' AS FPL
            , #{ftime} AS FNF
            , (SELECT SUM(FYSYX) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FSPSL
            , 240 AS FGDSE
            , 240*(SELECT SUM(FYSYX) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FYJSE
        UNION ALL
        SELECT
            2 AS FPX
            , '＞1.0L-1.66L≤' AS FPL
            , #{ftime} AS FNF
            , (SELECT SUM(FYDLS) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FSPSL
            , 420 AS FGDSE
            , 420*(SELECT SUM(FYDLS) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FYJSE
        UNION ALL
        SELECT
            3 AS FPX
            , '＞1.6L-2.0L≤' AS FPL
            , #{ftime} AS FNF
            , (SELECT SUM(FES) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FSPSL
            , 480 AS FGDSE
            , 480*(SELECT SUM(FES) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FYJSE
        UNION ALL
        SELECT
            4 AS FPX
            , '＞2.0L-2.5L≤' AS FPL
            , #{ftime} AS FNF
            , (SELECT SUM(FEDWS) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FSPSL
            , 720 AS FGDSE
            , 720*(SELECT SUM(FEDWS) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FYJSE
        UNION ALL
        SELECT
            5 AS FPX
            , '＞2.5L-3.0L≤' AS FPL
            , #{ftime} AS FNF
            , (SELECT SUM(FSS) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FSPSL
            , 1800 AS FGDSE
            , 1800*(SELECT SUM(FSS) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FYJSE
        UNION ALL
        SELECT
            6 AS FPX
            , '＞3.0L-4.0L≤' AS FPL
            , #{ftime} AS FNF
            , (SELECT SUM(FSDSS) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FSPSL
            , 3000 AS FGDSE
            , 3000*(SELECT SUM(FSDSS) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FYJSE
        UNION ALL
        SELECT
            7 AS FPX
            , '＞4.0L' AS FPL
            , #{ftime} AS FNF
            , (SELECT SUM(FSSYS) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FSPSL
            , 4500 AS FGDSE
            , 4500*(SELECT SUM(FSSYS) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('大型汽车', '小型汽车')) AS FYJSE
        UNION ALL
        SELECT
            8 AS FPX
            , '挂车' AS FPL
            , #{ftime} AS FNF
            , (SELECT SUM(FSPSLDWL) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('挂车')) AS FSPSL
            , 480 AS FGDSE
            , 480*(SELECT SUM(FSPSLDWL) FROM TB_ODS_ZHZS_RHJDCPLTJ_GEN WHERE F_SYS_YEAR &lt;= #{ftime} AND FHPZL IN ('挂车')) AS FYJSE
        )
        , rolluped AS (
            SELECT
                GROUPING(FPL) FGROUPING
                , CASE WHEN GROUPING(FPL)=1 THEN NULL ELSE MAX(FPX) END FPX
                , CASE WHEN GROUPING(FPL)=1 THEN '合计' ELSE MAX(FPL) END FPL
                , MAX(FNF) FNF
                , SUM(FSPSL) FSPSL
                , CASE WHEN GROUPING(FPL)=1 THEN NULL ELSE FGDSE END FGDSE
                , SUM(FYJSE) FYJSE
            FROM base GROUP BY ROLLUP(FPL)
        )
        , ss AS (
            SELECT FNF, SUM(FCCS) AS FCCS FROM tb_dw_srfx_srfx_main WHERE FNF = #{ftime}
        )
        SELECT r.FNF, FPX, FPL, FSPSL, FGDSE, ROUND(FYJSE/10000, 2) FYJSE, ROUND(s.FCCS/10000, 2) FCCS
        FROM rolluped r LEFT JOIN ss s ON r.FNF = s.FNF
        ORDER BY FGROUPING DESC, FPX ASC
    </select>
</mapper>
