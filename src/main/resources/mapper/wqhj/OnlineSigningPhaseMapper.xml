<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.wqhj.mapper.OnlineSigningPhaseMapper">
    <select id="overviewWithoutLoan"
            parameterType="com.hnbp.local.wqhj.model.OnlineSigningPhaseQUERY"
            resultType="com.hnbp.local.wqhj.model.OnlineSigningPhaseVO">
        <bind name="taxDataType"
              value="@com.hnbp.common.core.utils.SpringUtils@getBean(@com.hnbp.local.config.BizConfiguration@class).getTaxDataType()"/>
        WITH
        fdcqy_wqht_bzxzfExcluded AS (
            SELECT -- 计算不含税价
                ffdcqyId, ffdcqy, fnsrsbh, fnsrgm, fnsrszdlx, fhtzje, fyjzys, fzwqmj,
                fyjzysPtzz_before2025, fyjzysFptzz_before2025, fyjzysPtzz_after2025,
                fyjzysPtzz_after2025, fyjzysFptzz_after2025, fyjzysFzj_after2025,
                fmin_fhtqdrq, fbzxzfJe, fyjzysExcludedBzxzf,
                CASE fnsrgm
                WHEN 1 THEN ROUND(fyjzysExcludedBzxzf/( 1 + 0.05 ), 2) -- 小规模纳税人
                WHEN 2 THEN ROUND(fyjzysExcludedBzxzf/( 1 + 0.09 ), 2) -- 一般纳税人
                END AS fyjzys_bhs,
                <!-- 2025以前营收 -->
                CASE fnsrgm
                WHEN 1 THEN fyjzysPtzz_before2025/( 1 + 0.05 )
                WHEN 2 THEN fyjzysPtzz_before2025/( 1 + 0.09 )
                END AS fyjzysPtzz_before2025_bhs,
                CASE fnsrgm
                WHEN 1 THEN fyjzysFptzz_before2025/( 1 + 0.05 )
                WHEN 2 THEN fyjzysFptzz_before2025/( 1 + 0.09 )
                END AS fyjzysFptzz_before2025_bhs,
                CASE fnsrgm
                WHEN 1 THEN fyjzysFzj_before2025/( 1 + 0.05 )
                WHEN 2 THEN fyjzysFzj_before2025/( 1 + 0.09 )
                END AS fyjzysFzj_before2025_bhs,
                <!-- 2025以后营收 -->
                CASE fnsrgm
                WHEN 1 THEN fyjzysPtzz_after2025/( 1 + 0.05 )
                WHEN 2 THEN fyjzysPtzz_after2025/( 1 + 0.09 )
                END AS fyjzysPtzz_after2025_bhs,
                CASE fnsrgm
                WHEN 1 THEN fyjzysFptzz_after2025/( 1 + 0.05 )
                WHEN 2 THEN fyjzysFptzz_after2025/( 1 + 0.09 )
                END AS fyjzysFptzz_after2025_bhs,
                CASE fnsrgm
                WHEN 1 THEN fyjzysFzj_after2025/( 1 + 0.05 )
                WHEN 2 THEN fyjzysFzj_after2025/( 1 + 0.09 )
                END AS fyjzysFzj_after2025_bhs
            FROM (
                SELECT -- 剔除保障性住房
                    fdcqy_wq.ffdcqyId, fdcqy_wq.ffdcqy, fdcqy_wq.fnsrsbh, fdcqy_wq.fnsrgm, fdcqy_wq.fnsrszdlx, fdcqy_wq.fhtzje,
                    fdcqy_wq.fyjzys, fdcqy_wq.fzwqmj,
                    fdcqy_wq.fyjzysPtzz_before2025, fdcqy_wq.fyjzysFptzz_before2025, fdcqy_wq.fyjzysFzj_before2025,
                    fdcqy_wq.fyjzysPtzz_after2025, fdcqy_wq.fyjzysFptzz_after2025, fdcqy_wq.fyjzysFzj_after2025,
                    fdcqy_wq.fmin_fhtqdrq,
                    bzxzf.fbzxzfJe,
                    fdcqy_wq.fyjzys - COALESCE(bzxzf.fbzxzfJe, 0) AS fyjzysExcludedBzxzf
                    FROM (
                    SELECT
                    fdcqy.fid AS ffdcqyId, fdcqy.fqymc AS ffdcqy, fdcqy.fnsrsbh, fdcqy.fnsrgm, fdcqy.fnsrszdlx,
                    SUM(COALESCE(fhtje, 0)) / 10000 AS fhtzje,
                    SUM( COALESCE(fyjys, 0) ) / 10000 AS fyjzys,
                    SUM( COALESCE(fhsmj, 0) ) AS fzwqmj,
                    SUM( CASE WHEN ffwyt=1 OR ffwyt IS NULL AND fhtqdrq &lt; TO_DATE('2025-01-01', 'YYYY-MM-DD') THEN COALESCE(fyjys, 0) ELSE 0 END) / 10000 AS fyjzysPtzz_before2025,
                    SUM( CASE WHEN ffwyt=2 AND fhtqdrq &lt; TO_DATE('2025-01-01', 'YYYY-MM-DD') THEN COALESCE(fyjys, 0) ELSE 0 END) / 10000 AS fyjzysFptzz_before2025,
                    SUM( CASE WHEN ffwyt=3 AND fhtqdrq &lt; TO_DATE('2025-01-01', 'YYYY-MM-DD') THEN COALESCE(fyjys, 0) ELSE 0 END) / 10000 AS fyjzysFzj_before2025,

                    SUM( CASE WHEN ffwyt=1 OR ffwyt IS NULL AND fhtqdrq &gt;= TO_DATE('2025-01-01', 'YYYY-MM-DD') THEN COALESCE(fyjys, 0) ELSE 0 END) / 10000 AS fyjzysPtzz_after2025,
                    SUM( CASE WHEN ffwyt=2 AND fhtqdrq &gt;= TO_DATE('2025-01-01', 'YYYY-MM-DD') THEN COALESCE(fyjys, 0) ELSE 0 END) / 10000 AS fyjzysFptzz_after2025,
                    SUM( CASE WHEN ffwyt=3 AND fhtqdrq &gt;= TO_DATE('2025-01-01', 'YYYY-MM-DD') THEN COALESCE(fyjys, 0) ELSE 0 END) / 10000 AS fyjzysFzj_after2025,
                    MIN(fhtqdrq) AS fmin_fhtqdrq
                FROM ( -- 房地产企业库
                    SELECT fid, sourceId, create_time, fqymc, fnsrsbh, fqydz, fnsrgm, fnsrszdlx, fdataSourceType, batch_no, status
                    FROM tb_dw_jafdc_fdcqy_gen
                    <where>
                        <if test="fdcqymc != null and fdcqymc != ''">
                            fqymc LIKE CONCAT('%', #{fdcqymc}, '%')
                        </if>
                    </where>
                ) fdcqy
                INNER JOIN ( -- 网签合同
                    SELECT
                        fkfs_id, fhtje, fhsmj, fhtqdrq, ffwyt,
                        CASE
                            <!-- 一次性付款 => 合同金额 -->
                            WHEN ffkfs = 1 THEN fhtje
                            <!-- 按揭贷款、其他方式，且当前时间过了预计时间 => 合同金额 -->
                            WHEN ffkfs IN (2, 3) AND ((ADD_MONTHS(fhtqdrq, ${yjdzsj}) - NOW()) &lt; 0) THEN fhtje
                            <!-- 按揭贷款、其他方式，且当前时间未到预计时间 => 首付款 -->
                            WHEN ffkfs IN (2, 3) AND ((ADD_MONTHS(fhtqdrq, ${yjdzsj}) - NOW()) &gt; 0) THEN fsfk
                            ELSE fsfk
                        END AS fyjys
                    FROM tb_dw_jafdc_wqhtxx_gen
                    <where>
                        <if test="fywtjrqStart != null">
                            AND fhtqdrq &gt;= #{fywtjrqStart}
                        </if>
                        <if test="fywtjrqEnd != null">
                            AND fhtqdrq &lt;= #{fywtjrqEnd}
                        </if>
                    </where>
                ) wqhtxx ON fdcqy.fid = wqhtxx.fkfs_id
                GROUP BY fdcqy.fid, fdcqy.fqymc, fdcqy.fnsrsbh, fdcqy.fnsrgm, fdcqy.fnsrszdlx
                ) fdcqy_wq
                LEFT JOIN ( -- 保障性住房
                    SELECT ffdcqy_id AS ffdcqyId_bzxzf, SUM(COALESCE(fje, 0)) AS fbzxzfJe
                    FROM tb_dw_jafdc_bzxzfxx_gen
                    <where>
                        <if test="fywtjrqStart != null">
                            AND fywsj &gt;= #{fywtjrqStart}
                        </if>
                        <if test="fywtjrqEnd != null">
                            AND fywsj &lt;= #{fywtjrqEnd}
                        </if>
                    </where>
                    GROUP BY ffdcqy_id
                ) bzxzf ON fdcqy_wq.ffdcqyId = ffdcqyId_bzxzf
            ) tmp1
        ),
        ss_predict AS ( <!-- 测算预缴增值税、土地增值税、附加税 -->
            SELECT ffdcqyId_ssp, fcszzs, fcsfjs, fcstdzzs, ROUND( ( fcszzs + fcsfjs + fcstdzzs ), 2 ) AS fcshj FROM (
                SELECT
                    fdcqy.ffdcqyId AS ffdcqyId_ssp,
                    ROUND(fyjzys_bhs * #{zslYjZzs}, 2) AS fcszzs,
                    CASE
                    WHEN fnsrszdlx = 1 THEN ROUND(fyjzys_bhs * #{zslYjZzs} * #{zslFjsCq}, 2)
                    WHEN fnsrszdlx = 2 THEN ROUND(fyjzys_bhs * #{zslYjZzs} * #{zslFjsXq}, 2)
                    ELSE ROUND(fyjzys_bhs * #{zslYjZzs} * #{zslFjsCq}, 2)
                    END AS fcsfjs,
                    ROUND((
                    fyjzysPtzz_before2025_bhs * #{zslTdzzsPtzz}     + fyjzysPtzz_after2025_bhs*0.005
                    + fyjzysFptzz_before2025_bhs * #{zslTdzzsFptzz} + fyjzysFptzz_after2025_bhs*0.005
                    + fyjzysFzj_before2025_bhs * #{zslTdzzsFzz}     + fyjzysFzj_after2025_bhs*0.005
                    ), 2) AS fcstdzzs
                FROM fdcqy_wqht_bzxzfExcluded fdcqy
            ) AS t1
        ),
        ss AS (
            SELECT
                ffdcqyId AS ffdcqyId_ss,
                SUM(CASE WHEN ftax_type = 'zzs' THEN frkhj ELSE 0 END) AS frkzzs,
                SUM(CASE WHEN ftax_type = 'zzs' THEN ffqyjZzs1 ELSE 0 END) AS frkfqyjZzs,
                SUM(CASE WHEN ftax_type = 'fjs' THEN frkhj ELSE 0 END) AS frkfjs,
                SUM(CASE WHEN ftax_type = 'tdzzs' THEN frkhj ELSE 0 END) AS frktdzzs,
                SUM(CASE
                        WHEN ftax_type IN ('fjs', 'tdzzs') THEN frkhj
                        WHEN ftax_type = 'zzs' THEN ffqyjZzs1
                        ELSE 0
                END ) AS frkhj
            FROM (
                SELECT
                    ffdcqyId,
                    CASE
                    WHEN fzsxm = '增值税' <if test='taxDataType == "sw"'>AND fzspm LIKE '%增量房%'</if> THEN 'zzs'
                    WHEN fzsxm IN ('城市维护建设税','教育费附加','地方教育附加') THEN 'fjs'
                    WHEN fzsxm = '土地增值税' <if test='taxDataType == "sw"'>AND fzspm LIKE '%预征%'</if> THEN 'tdzzs'
                    END AS ftax_type,
                    ROUND( SUM( CASE WHEN fzsxm = '增值税' <if test='taxDataType == "sw"'>AND fzspm LIKE '%增量房%' AND (fsksx = '分期预缴税款')</if> THEN COALESCE(fhj,0) ELSE 0 END )/10000 , 2 ) AS ffqyjZzs1,
                    ROUND( SUM(COALESCE(fhj, 0))/10000, 2 ) AS frkhj
                FROM (
                    SELECT t1.ffdcqyId, t1.ffdcqy, t1.fnsrsbh, t1.fnsrgm, t1.fnsrszdlx, t1.fhtzje, t1.fyjzys, t1.fzwqmj,
                    <!--t1.fyjzysPtzz, t1.fyjzysFptzz, t1.fyjzysFzj, -->t1.fmin_fhtqdrq, t1.fbzxzfJe, t1.fyjzysExcludedBzxzf,
                    t1.fyjzys_bhs, <!--t1.ffyjzysPtzz_bhs, t1.fyjzysFptzz_bhs, t1.fyjzysFzj_bhs,-->COALESCE(t2.fhj, 0) AS fhj, t2.fzsxm, t2.fzspm, t2.fsksx
                    FROM fdcqy_wqht_bzxzfExcluded t1
                    LEFT JOIN tb_dm_jafdc_ssmd_main t2
                    ON (
                        t1.fnsrsbh = t2.fnsrsbh
                        AND fzsxm IN ('增值税', '土地增值税', '城市维护建设税','教育费附加','地方教育附加')
                        <choose>
                            <when test='taxDataType == "sw"'>AND fzspm NOT IN ('滞纳金')</when>
                            <when test='taxDataType == "hl"'>AND fyskm NOT LIKE '%滞纳%'</when>
                        </choose>
                        AND t2.fskssqq >= CASE WHEN t1.fmin_fhtqdrq IS NOT NULL THEN CONCAT(DATE_FORMAT(t1.fmin_fhtqdrq, 'YYYY-MM'), '-01 00:00:00') ELSE NULL END
                        AND t2.fskssqq &lt;= COALESCE(#{fywtjrqEnd}, NOW())
                        <if test="ldts != null and ldts == 0">
                            AND (fyskm NOT LIKE '%留抵退%' AND fyskm NOT IN ('*********','*********','*********','*********','*********'))
                        </if>
                    )
                ) t3
                GROUP BY ffdcqyId,
                CASE
                    WHEN fzsxm = '增值税' <if test='taxDataType == "sw"'>AND fzspm LIKE '%增量房%'</if> THEN 'zzs'
                    WHEN fzsxm IN ('城市维护建设税','教育费附加','地方教育附加') THEN 'fjs'
                    WHEN fzsxm = '土地增值税' <if test='taxDataType == "sw"'>AND fzspm LIKE '%预征%'</if> THEN 'tdzzs'
                END
            ) tmp1
            GROUP BY ffdcqyId
        ),
        ce_calculate AS (
            SELECT
                ss.ffdcqyId_ss AS ffdcqyId_ce,
                ROUND(fcszzs - frkfqyjZzs, 2) AS fcezzs,
                ROUND(fcstdzzs - frktdzzs, 2) AS fcetdzzs,
                ROUND(fcsfjs - frkfjs, 2) AS fcefjs,
                ROUND(
                (fcszzs - frkfqyjZzs) + (fcstdzzs - frktdzzs) + (fcsfjs - frkfjs)
                , 2
                ) AS fcehj
            FROM ss
            INNER JOIN ss_predict ssp ON ss.ffdcqyId_ss = ssp.ffdcqyId_ssp
        )
        SELECT <!-- 计算比例 -->
        grouping(ffdcqyId) AS fgrouping
        , ffdcqyId
        , CASE grouping(ffdcqyId) WHEN 1 THEN '合计' ELSE MAX(ffdcqy) END AS ffdcqy
        , CASE grouping(ffdcqyId) WHEN 1 THEN NULL ELSE MAX(fnsrsbh) END AS fnsrsbh
        , CASE grouping(ffdcqyId) WHEN 1 THEN NULL ELSE MAX(fnsrgm) END AS fnsrgm
        , CASE grouping(ffdcqyId) WHEN 1 THEN NULL ELSE CASE MAX(fnsrgm) WHEN 1 THEN '小规模纳税人' WHEN 2 THEN '一般纳税人' END END AS fnsrgmTran
        , CASE grouping(ffdcqyId) WHEN 1 THEN NULL ELSE MAX(fnsrszdlx) END AS fnsrszdlx
        , SUM(fhtzje) AS fhtzje
        , SUM(fyjzys) AS fyjzys
        , SUM(fzwqmj) AS fzwqmj
        <!--
        , SUM(fyjzysPtzz) AS fyjzysPtzz
        , SUM(fyjzysFptzz) AS fyjzysFptzz
        , SUM(fyjzysFzj) AS fyjzysFzj
        -->
        , CASE grouping(ffdcqyId) WHEN 1 THEN NULL ELSE MAX(fmin_fhtqdrq) END AS fmin_fhtqdrq
        , SUM(fbzxzfJe) AS fbzxzfJe
        , SUM(fyjzysExcludedBzxzf) AS fyjzysExcludedBzxzf
        , SUM(fyjzys_bhs) AS fyjzysBhs
        <!--
        , SUM(ffyjzysPtzz_bhs) AS ffyjzysPtzz_bhs
        , SUM(fyjzysFptzz_bhs) AS fyjzysFptzz_bhs
        , SUM(fyjzysFzj_bhs) AS fyjzysFzj_bhs
        -->
        , SUM(fcszzs) AS fcszzs
        , SUM(fcsfjs) AS fcsfjs
        , SUM(fcstdzzs) AS fcstdzzs
        , SUM(fcshj) AS fcshj
        , SUM(frkzzs) AS frkzzs
        , SUM(frkfqyjZzs) AS frkfqyjZzs
        , SUM(frkfjs) AS frkfjs
        , SUM(frktdzzs) AS frktdzzs
        , SUM(frkhj) AS frkhj
        , SUM(fcezzs) AS fcezzs
        , SUM(fcetdzzs) AS fcetdzzs
        , SUM(fcefjs) AS fcefjs
        , SUM(fcehj) AS fcehj
        , CASE
            WHEN SUM(fyjzys_bhs) = 0 THEN 0
            ELSE SUM(frkfqyjZzs) / SUM(fyjzys_bhs)
        END AS fyjzsbl
        , CASE
            WHEN SUM(fyjzys_bhs) = 0 THEN 0
            ELSE SUM(frkzzs) / SUM(fyjzys_bhs)
        END AS fztsfl
        FROM (
        SELECT ffdcqyId, ffdcqy, fnsrsbh, fnsrgm, fnsrszdlx, fhtzje, fyjzys, fzwqmj, <!--fyjzysPtzz, fyjzysFptzz, fyjzysFzj,-->
        fmin_fhtqdrq, fbzxzfJe, fyjzysExcludedBzxzf, fyjzys_bhs, <!--ffyjzysPtzz_bhs, fyjzysFptzz_bhs, fyjzysFzj_bhs,-->
        ffdcqyId_ssp, fcszzs, fcsfjs, fcstdzzs, fcshj, ffdcqyId_ss, frkzzs, frkfqyjZzs, frkfjs, frktdzzs, frkhj,
        ffdcqyId_ce, fcezzs, fcetdzzs, fcefjs, fcehj
        FROM fdcqy_wqht_bzxzfExcluded t1
        LEFT JOIN ss_predict t2 ON t1.ffdcqyId= t2.ffdcqyId_ssp
        LEFT JOIN ss t3 ON t1.ffdcqyId = t3.ffdcqyId_ss
        LEFT JOIN ce_calculate t4 ON t1.ffdcqyId = t4.ffdcqyId_ce
        ) tmp1
        GROUP BY ROLLUP(ffdcqyId)
        ORDER BY CASE fgrouping WHEN 1 THEN 0 ELSE 1 END, fcehj DESC
    </select>
</mapper>
