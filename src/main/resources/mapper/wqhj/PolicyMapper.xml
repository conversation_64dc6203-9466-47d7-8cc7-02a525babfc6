<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hnbp.local.wqhj.mapper.PolicyMapper">
    <resultMap id="policyDOResultMap" type="com.hnbp.local.wqhj.model.PolicyDO">
        <result column="fid" property="fid" jdbcType="VARCHAR" />
        <result column="fname" property="fname" jdbcType="VARCHAR" />
        <result column="fdescription" property="fdescription" jdbcType="VARCHAR" />
        <result column="fcomputation_rule" property="fcomputationRule" jdbcType="DECIMAL" />
        <result column="ftax_type" property="ftaxType" jdbcType="VARCHAR" />
        <result column="ftax_type_name" property="ftaxTypeName" jdbcType="VARCHAR" />
        <result column="factive_start_time" property="factiveStartTime" jdbcType="VARCHAR" />
        <result column="factive_end_time" property="factiveEndTime" jdbcType="VARCHAR" />
        <result column="fphase" property="fphase" jdbcType="VARCHAR" />
        <result column="fphase_name" property="fphaseName" jdbcType="VARCHAR" />
    </resultMap>


    <select id="findPolicyQuery" parameterType="com.hnbp.local.wqhj.model.PolicyQuery"
            resultMap="policyDOResultMap">
        SELECT fid,fname,fdescription,fcomputation_rule,ftax_type,ftax_type_name,
        factive_start_time,factive_end_time,fphase,fphase_name FROM tb_dw_jafdc_policy_main
        WHERE 1=1
        <if test="fphase != null and fphase != ''">
            and fphase = #{fphase}
        </if>
        <if test="ftaxTypeList != null and ftaxTypeList != ''">
            and ftax_type in
            <foreach  item="item" collection="ftaxTypeList" index="index"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="fywtjrqStart != null">
            and factive_start_time &lt;= #{fywtjrqStart}
        </if>
        <if test="fywtjrqEnd != null">
            and
                CASE
                    WHEN (factive_end_time = 'NOW') THEN 1
                    WHEN (factive_end_time >= #{fywtjrqEnd}) THEN 1
                    ELSE 0
                END = 1
        </if>
    </select>

    <select id="queryPolicyList" parameterType="com.hnbp.local.wqhj.model.PolicyQuery"
            resultMap="policyDOResultMap">
        SELECT fid,fname,fdescription,fcomputation_rule,ftax_type,ftax_type_name,
        factive_start_time,factive_end_time,fphase,fphase_name FROM tb_dw_jafdc_policy_main
        WHERE 1=1
        <if test="fname != null and fname != ''">
            and fname like concat(concat(%,#{fname}),%)
        </if>
        <if test="fphaseName != null and fphaseName != ''">
            and fphaseName like concat(concat(%,#{fphaseName}),%)
        </if>
        <if test="fname != null and fname != ''">
            and ftaxTypeName like concat(concat(%,#{ftaxTypeName}),%)
        </if>
        <if test="fywtjrqStart != null">
            and factive_start_time >= #{fywtjrqStart}
        </if>
        <if test="fywtjrqEnd != null">
            and
                CASE
                    WHEN (factive_end_time = 'NOW') THEN 1
                    WHEN (factive_end_time >= #{fywtjrqEnd}) THEN 1
                    ELSE 0
                END = 1
        </if>
    </select>

    <select id="queryTaxType" parameterType="com.hnbp.local.wqhj.model.PolicyQuery"
            resultMap="policyDOResultMap">
        SELECT DISTINCT ftax_type,ftax_type_name FROM tb_dw_jafdc_policy_main
        WHERE 1=1
        <if test="fphase != null and fphase != ''">
            and fphase = #{fphase}
        </if>
    </select>

</mapper>
