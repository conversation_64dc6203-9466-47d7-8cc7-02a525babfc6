<?xml version="1.0" encoding="UTF-8"?><!--

    Copyright 2004-2024 the original author or authors.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

--><!--Converted at: Sat Sep 14 10:20:55 CST 2024-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.sszbfx.mapper.IndustryAnalysisMapper">

    <select id="revenueByTaxTypeMxTable" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT FNSRSBH, FNSRMC 纳税人名称, fhyml, fpx,
        ROUND(FHJ_JN / 10000, 2) fhj_jn,
        ROUND(FHJ_QN / 10000, 2) fhj_qn,
        ROUND((FHJ_JN - FHJ_QN) / 10000, 2) fzje_hj,
        ROUND(CASE WHEN FHJ_QN = 0 THEN 0
        ELSE (FHJ_JN - FHJ_QN) / ABS(FHJ_QN) * 100 END, 2) fzjb_hj
        FROM (
        SELECT
            GROUPING(FNSRMC) fgropuping,
            MAX(FNSRSBH) FNSRSBH,
            CASE WHEN GROUPING(FNSRMC) = 1 THEN NULL ELSE FPX END AS FPX,
            CASE WHEN GROUPING(FNSRMC) = 1 THEN '合计' ELSE FNSRMC END AS FNSRMC,
            CASE WHEN GROUPING(FNSRMC) = 1 THEN '' ELSE fhyml END AS fhyml,
            SUM(FHJ_JN) FHJ_JN,
            SUM(FHJ_QN) FHJ_QN
        FROM (
        SELECT tmp1.FNSRSBH, tmp1.FNSRMC,
        FIRST_VALUE(tmp1.fhyml) OVER(PARTITION BY tmp1.FNSRSBH, tmp1.FNSRMC ORDER BY FHJ_JN DESC ) fhyml,
        DENSE_RANK() OVER(ORDER BY FHJ_JN DESC) AS FPX,
        FHJ_JN,
        FHJ_QN
        FROM
        (SELECT max(FNSRSBH) FNSRSBH, FNSRMC, fhyml,
        SUM(CASE WHEN #{ftype} = 'dflc' THEN FQXJ ELSE FHJ END) FHJ_JN
        FROM TB_DW_SRFX_SRFX_MAIN tdssm
        WHERE FZSXM IN ('教育费附加','地方教育附加','增值税','企业所得税','契税',
        '土地增值税','耕地占用税','资源税','房产税','城市维护建设税',
        '车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税',
        '印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fStartRkrq != null and fStartRkrq != ''">
            <if test="fEndRkrq != null and fEndRkrq != ''">
                AND FRKRQ BETWEEN TO_DATE(#{fStartRkrq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndRkrq},'YYYY-MM'))
          </if>
        </if>
        <if test="fStartDjrq != null and fStartDjrq != ''">
            <if test="fEndDjrq != null and fEndDjrq != ''">
                AND EXISTS (SELECT 1 FROM TB_DW_SRFX_SWDJXX_MAIN tmp WHERE tdssm.FNSRMC = tmp.FNSRMC)
                AND FKYSLRQ BETWEEN TO_DATE(#{fStartDjrq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndDjrq},'YYYY-MM'))
          </if>
        </if>
        <if test="fStartSkssqq != null and fStartSkssqq != ''">
           <if test="fEndSkssqq != null and fEndSkssqq != ''">
                AND FSKSSQQQ BETWEEN TO_DATE(#{fStartSkssqq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndSkssqq},'YYYY-MM'))
           </if>
        </if>
        <if test="fssqyList != null">
        AND
            FSKGK IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">AND
            FZSXM IN (${fzsxmStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">AND
            fhyml in (${fhydlStr}) <!-- FHYDL in (${fhydlStr}) -->
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">AND
            FDJZCLX IN (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            FYSKM NOT LIKE '%留抵退%'
        </if>
        <if test="fyskmStr != null and fyskmStr != ''">and
            FYSKM in (${fyskmStr})
        </if>
        GROUP BY FNSRMC, fhyml
        )tmp1
        left join(
        SELECT FNSRMC, fhyml,
        SUM(CASE WHEN #{ftype} = 'dflc' THEN FQXJ ELSE FHJ END) FHJ_QN
        FROM TB_DW_SRFX_SRFX_MAIN tdssm
        WHERE FZSXM IN ('教育费附加','地方教育附加','增值税','企业所得税','契税',
        '土地增值税','耕地占用税','资源税','房产税','城市维护建设税',
        '车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税',
        '印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fStartRkrqLastYear != null and fStartRkrqLastYear != ''">
            <if test="fEndRkrqLastYear != null and fEndRkrqLastYear != ''">
                AND FRKRQ BETWEEN TO_DATE(#{fStartRkrqLastYear},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndRkrqLastYear},'YYYY-MM'))
            </if>
        </if>
        <if test="fStartDjrq != null and fStartDjrq != ''">
            <if test="fEndDjrq != null and fEndDjrq != ''">
                AND EXISTS (SELECT 1 FROM TB_DW_SRFX_SWDJXX_MAIN tmp WHERE tdssm.FNSRMC = tmp.FNSRMC
                AND FKYSLRQ BETWEEN TO_DATE(#{fStartDjrq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndDjrq},'YYYY-MM'))
                )
            </if>
        </if>
        <if test="fStartSkssqqLastYear != null and fStartSkssqqLastYear != ''">
            <if test="fEndSkssqqLastYear != null and fEndSkssqqLastYear != ''">
                AND FSKSSQQQ BETWEEN TO_DATE(#{fStartSkssqqLastYear},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndSkssqqLastYear},'YYYY-MM'))
            </if>
        </if>
        <if test="fssqyList != null">AND
            FSKGK IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">AND
            FZSXM IN (${fzsxmStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">AND
            fhyml in (${fhydlStr}) <!-- FHYDL in (${fhydlStr}) -->
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">AND
            FDJZCLX IN (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            FYSKM NOT LIKE '%留抵退%'
        </if>
        <if test="fyskmStr != null and fyskmStr != ''">AND
            FYSKM in (${fyskmStr})
        </if>
        GROUP BY FNSRMC, fhyml) tmp
        on tmp1.fnsrmc=tmp.fnsrmc
        WHERE 1=1
        <if test="fsrlx == &quot;大于一亿&quot;">AND
            FHJ_JN &gt;= 100000000
        </if>
        <if test="fsrlx == &quot;五千万至一亿&quot;">AND
            FHJ_JN &gt;= 50000000 AND FHJ_JN &lt;= 100000000
        </if>
        <if test="fsrlx == &quot;一千万至五千万&quot;">AND
            FHJ_JN &gt;= 10000000 AND FHJ_JN &lt;= 50000000
        </if>
        <if test="fsrlx == &quot;五百万至一千万&quot;">AND
            FHJ_JN &gt;= 5000000 AND FHJ_JN &lt;= 10000000
        </if>
        <if test="fsrlx == &quot;五十万至五百万&quot;">AND
            FHJ_JN &gt;= 500000 AND FHJ_JN &lt;= 5000000
        </if>
        <if test="fsrlx == &quot;小于五十万&quot;">AND
            FHJ_JN &lt;= 500000
        </if>
        )
        <where>
            <if test="fpmLimit != null and fpmLimit != ''">
               and FPX &lt;= #{fpmLimit}
            </if>
        </where>
        GROUP BY ROLLUP ((FNSRSBH, FNSRMC, fhyml))
        ORDER BY fgropuping DESC, FPX ASC
        )
    </select>

    <select id="revenueByTaxTypeMxTable2" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select * from (
        SELECT FNSRSBH, FNSRMC 纳税人名称, fhyml,
        ROW_NUMBER() OVER (ORDER BY FHJ_JN DESC,FNSRSBH,FNSRMC,fhyml) - 1 AS fpx,
        ROUND(FHJ_JN / 10000, 2) fhj_jn,
        ROUND(FHJ_QN / 10000, 2) fhj_qn,
        ROUND((FHJ_JN - FHJ_QN) / 10000, 2) fzje_hj,
        ROUND(CASE WHEN FHJ_QN = 0 THEN 0
        ELSE (FHJ_JN - FHJ_QN) / ABS(FHJ_QN) * 100 END, 2) fzjb_hj
        FROM (
        SELECT FNSRSBH,
        CASE WHEN GROUPING(FNSRMC) = 1 THEN '合计'
        ELSE FNSRMC END AS
        FNSRMC,
        CASE WHEN GROUPING(FNSRMC) = 1 THEN ''
        ELSE fhyml END AS fhyml,
        SUM(FHJ_JN) FHJ_JN,
        SUM(FHJ_QN) FHJ_QN
        FROM (SELECT tmp1.FNSRSBH, tmp1.FNSRMC,
                     FIRST_VALUE(tmp1.fhyml) OVER(PARTITION BY tmp1.FNSRSBH, tmp1.FNSRMC ORDER BY FHJ_JN DESC ) fhyml,
        FHJ_JN,
        FHJ_QN
        FROM
        (SELECT max(FNSRSBH) FNSRSBH, FNSRMC, fhyml,
        SUM(CASE WHEN #{ftype} = 'dflc' THEN FQXJ ELSE FHJ END) FHJ_JN
        FROM TB_DW_SRFX_SRFX_MAIN tdssm
        WHERE FZSXM IN ('教育费附加','地方教育附加','增值税','企业所得税','契税',
        '土地增值税','耕地占用税','资源税','房产税','城市维护建设税',
        '车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税',
        '印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fStartRkrq != null and fStartRkrq != ''">
            <if test="fEndRkrq != null and fEndRkrq != ''">
                AND FRKRQ BETWEEN TO_DATE(#{fStartRkrq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndRkrq},'YYYY-MM'))
          </if>
        </if>
        <if test="fStartDjrq != null and fStartDjrq != ''">
          <if test="fEndDjrq != null and fEndDjrq != ''">
              AND EXISTS (SELECT 1 FROM TB_DW_SRFX_SWDJXX_MAIN tmp WHERE tdssm.FNSRMC = tmp.FNSRMC)
              AND FKYSLRQ BETWEEN TO_DATE(#{fStartDjrq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndDjrq},'YYYY-MM'))
          </if>
        </if>
        <if test="fStartSkssqq != null and fStartSkssqq != ''">
           <if test="fEndSkssqq != null and fEndSkssqq != ''">
              AND FSKSSQQQ BETWEEN TO_DATE(#{fStartSkssqq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndSkssqq},'YYYY-MM'))
           </if>
        </if>
        <if test="fssqyList != null">
        AND
            FSKGK IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">AND
            FZSXM IN (${fzsxmStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">AND
            fhyml in (${fhydlStr}) <!-- FHYDL in (${fhydlStr}) -->
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">AND
            FDJZCLX IN (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            FYSKM NOT LIKE '%留抵退%'
        </if>
        <if test="fyskmStr != null and fyskmStr != ''">and
            FYSKM in (${fyskmStr})
        </if>

        GROUP BY FNSRMC, fhyml
        )tmp1
        left join(
        SELECT FNSRMC, fhyml,
        SUM(CASE WHEN #{ftype} = 'dflc' THEN FQXJ ELSE FHJ END) FHJ_QN
        FROM TB_DW_SRFX_SRFX_MAIN tdssm
        WHERE FZSXM IN ('教育费附加','地方教育附加','增值税','企业所得税','契税',
        '土地增值税','耕地占用税','资源税','房产税','城市维护建设税',
        '车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税',
        '印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fStartRkrqLastYear != null and fStartRkrqLastYear != ''">
            <if test="fEndRkrqLastYear != null and fEndRkrqLastYear != ''">
                AND FRKRQ BETWEEN TO_DATE(#{fStartRkrqLastYear},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndRkrqLastYear},'YYYY-MM'))
            </if>
        </if>
        <if test="fStartDjrq != null and fStartDjrq != ''">
            <if test="fEndDjrq != null and fEndDjrq != ''">
                AND EXISTS (SELECT 1 FROM TB_DW_SRFX_SWDJXX_MAIN tmp WHERE tdssm.FNSRMC = tmp.FNSRMC
                AND FKYSLRQ BETWEEN TO_DATE(#{fStartDjrq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndDjrq},'YYYY-MM'))
                )
            </if>
        </if>
        <if test="fStartSkssqqLastYear != null and fStartSkssqqLastYear != ''">
            <if test="fEndSkssqqLastYear != null and fEndSkssqqLastYear != ''">
                AND FSKSSQQQ BETWEEN TO_DATE(#{fStartSkssqqLastYear},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndSkssqqLastYear},'YYYY-MM'))
            </if>
        </if>
        <if test="fssqyList != null">AND
            FSKGK IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">AND
            FZSXM IN (${fzsxmStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">AND
            fhyml in (${fhydlStr}) <!-- FHYDL in (${fhydlStr}) -->
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">AND
            FDJZCLX IN (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            FYSKM NOT LIKE '%留抵退%'
        </if>
        <if test="fyskmStr != null and fyskmStr != ''">AND
            FYSKM in (${fyskmStr})
        </if>
        GROUP BY FNSRMC, fhyml) tmp
        on tmp1.fnsrmc=tmp.fnsrmc
        ) t1
        WHERE 1=1
        <if test="fsrlx == &quot;大于一亿&quot;">AND
            FHJ_JN &gt;= 100000000
        </if>
        <if test="fsrlx == &quot;五千万至一亿&quot;">AND
            FHJ_JN &gt;= 50000000 AND FHJ_JN &lt;= 100000000
        </if>
        <if test="fsrlx == &quot;一千万至五千万&quot;">AND
            FHJ_JN &gt;= 10000000 AND FHJ_JN &lt;= 50000000
        </if>
        <if test="fsrlx == &quot;五百万至一千万&quot;">AND
            FHJ_JN &gt;= 5000000 AND FHJ_JN &lt;= 10000000
        </if>
        <if test="fsrlx == &quot;五十万至五百万&quot;">AND
            FHJ_JN &gt;= 500000 AND FHJ_JN &lt;= 5000000
        </if>
        <if test="fsrlx == &quot;小于五十万&quot;">AND
            FHJ_JN &lt;= 500000
        </if>
        GROUP BY ROLLUP ((FNSRSBH, FNSRMC, fhyml))) t2
        ORDER BY fpx
        ) where 1=1
        <if test="fpmLimit != null and fpmLimit != ''">
           and fpx &lt;= #{fpmLimit}
        </if>
    </select>

</mapper>
