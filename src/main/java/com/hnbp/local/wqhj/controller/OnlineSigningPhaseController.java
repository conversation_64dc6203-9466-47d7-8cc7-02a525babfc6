package com.hnbp.local.wqhj.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;

import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.wqhj.model.OnlineSigningPhaseQUERY;
import com.hnbp.local.wqhj.model.OnlineSigningPhaseVO;
import com.hnbp.local.wqhj.service.OnlineSigningPhaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/onlineSigningPhase")
@Tag(name = "网签环节")
public class OnlineSigningPhaseController {
    @Autowired
    private OnlineSigningPhaseService onlineSigningPhaseService;

    @Operation(summary = "网签环节列表")
    @PostMapping("/overview_bzb")
    public ResultMsg overview_bzb(@RequestBody OnlineSigningPhaseQUERY query) {
        Page<OnlineSigningPhaseVO> overview = onlineSigningPhaseService.overview(query, true);
        return ResultMsg.success(overview.getResult(), overview.getTotal());
    }

}
