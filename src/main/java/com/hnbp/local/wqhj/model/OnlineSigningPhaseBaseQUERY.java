package com.hnbp.local.wqhj.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
// @Builder
@NoArgsConstructor
@AllArgsConstructor
public class OnlineSigningPhaseBaseQUERY extends BaseQUERY {
    /** sl: 税率，zsl：征收率，Bd：本地，Wd：外地*/

    @ApiModelProperty(name = "附加税征收率-城区")
    private BigDecimal zslFjsCq;

    @ApiModelProperty(name = "附加税征收率-县区")
    private BigDecimal zslFjsXq;

    @ApiModelProperty(name = "增值税征收率")
    private BigDecimal zslYjZzs;

    private BigDecimal zslTdzzsPtzz;

    private BigDecimal zslTdzzsFptzz;

    private BigDecimal zslTdzzsFzz;
}
