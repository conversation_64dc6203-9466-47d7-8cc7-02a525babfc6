package com.hnbp.local.wqhj.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OnlineSigningPhaseDetailQUERY extends OnlineSigningPhaseBaseQUERY {
    @ApiModelProperty(name = "房地产企业ID", required = true)
    private String fdcqyId;

    @ApiModelProperty(name = "房地产企业名称")
    private String fdcqymc;

    @ApiModelProperty(name = "预计到账时间")
    private Integer yjdzsj;

    @ApiModelProperty(name = "是否包含留抵退税", notes = "0 = 不包含, 1 = 包含")
    private Integer ldts;

    @ApiModelProperty(name = "是否存在商贷、公积金贷款数据")
    private Boolean loanFlag;


    /** sl: 税率，zsl：征收率，Bd：本地，Wd：外地*/

    @ApiModelProperty(name = "附加税征收率-城区")
    private BigDecimal zslFjsCq;

    @ApiModelProperty(name = "附加税征收率-县区")
    private BigDecimal zslFjsXq;

    @ApiModelProperty(name = "增值税征收率")
    private BigDecimal zslZzs;

    private BigDecimal zslTdzzsPtzz;

    private BigDecimal zslTdzzsFptzz;

    private BigDecimal zslTdzzsFzj;

    /**
     * 征收项目
     */
    private String fzsxm;
    private List<String> fzsxmList;

    public OnlineSigningPhaseDetailQUERY handleConditionFzsxm() {
        Optional.ofNullable(this.fzsxm)
                .filter(StringUtils::isNotBlank)
                .map(zsxmStr -> zsxmStr.split(","))
                .map(Arrays::asList)
                .ifPresent(this::setFzsxmList);

        return this;
    }

}
