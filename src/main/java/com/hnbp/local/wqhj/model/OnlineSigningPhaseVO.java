package com.hnbp.local.wqhj.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hnbp.local.wqhj.tool.BigDecimalSerializerScale2;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OnlineSigningPhaseVO implements Serializable {
    /** 用于判断是否为合计行 */
    private Integer fgrouping;

    private String ffdcqyId;
    private String ffdcqy;

    private Integer fnsrgm;
    private String  fnsrgmTran;

    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal fzwqmj;
    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal fhtzje;
    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal fyjzysBhs; // 剔除保障性住房总营收，且不含税

    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal fyjzys;    // 预计总营收，含税
    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal fbzxzfJe;  // 保障性住房总营收

    private BigDecimal fyjzsbl;
    private BigDecimal fztsfl;

    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal fcsZzs;
    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal frkZzs;
    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal frkfqyjZzs;
    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal fceZzs;

    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal fcsTdzzs;
    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal frkTdzzs;
    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal fceTdzzs;

    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal fcsFjs;
    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal frkFjs;
    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal fceFjs;

    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal fcsHj;
    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal frkHj;
    @JsonSerialize(using = BigDecimalSerializerScale2.class)
    private BigDecimal fceHj;
}
