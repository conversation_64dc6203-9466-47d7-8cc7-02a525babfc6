package com.hnbp.local.wqhj.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @version 1.0.0
 * @Annotation:
 * @Author: sheng_huang
 * @Data: 2023/10/21 16:55
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PolicyQuery extends BaseQUERY implements Serializable {

    /** 政策id */
    private String fid;

    /** 政策名称 */
    private String fname;

    /** 所属环节 */
    private String fphase;

    /** 所属环节(中文) */
    private String fphaseName;

    /** 单个税种 */
    private String ftaxType;

    /** 单个税种(中文) */
    private String ftaxTypeName;

    /** 多个税种 */
    private List<String> ftaxTypeList;

    /** 计算规则 */
    private BigDecimal fcomputationRule;

    /** 政策起始时间 */
    private String factiveStartTime;

    /** 政策终止时间 */
    private String factiveEndTime;

    /** 政策描述 */
    private String fdescription;
}
