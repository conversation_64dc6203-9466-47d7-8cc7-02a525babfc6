package com.hnbp.local.wqhj.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.java.Log;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Year;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Optional;

@Log
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseQUERY implements Serializable {
    public String fid;
    /**
     * LayUI 的默认分页字段
     */
    public Integer page;

    public Integer limit;

    /**
     * LayUI 表格表头，用于导出 Excel
     */
    private String exportCols;

    @ApiModelProperty(name = "搜索框关键词")
    public String keyword;

    @ApiModelProperty(name = "业务统计日期")
    public String fywtjrq;

    @ApiModelProperty(name = "业务统计日期起")

    public LocalDateTime fywtjrqStart;

    @ApiModelProperty(name = "业务统计日期止")

    public LocalDateTime fywtjrqEnd;

    @ApiModelProperty(name = "税款所属日期")

    public String fskssrq;

    @ApiModelProperty(name = "税款所属日期起")

    public LocalDateTime fskssrqStart;

    @ApiModelProperty(name = "税款所属日期止")

    public LocalDateTime fskssrqEnd;

    @ApiModelProperty(name = "预售许可证日期",notes = "")
    private String fysxkzrq;

    @ApiModelProperty(name = "预售许可证日期起",notes = "")

    public LocalDateTime fysxkzrqStart;

    @ApiModelProperty(name = "预售许可证日期止",notes = "")

    public LocalDateTime fysxkzrqEnd;


    /****************************************/
    public static String defaultRangeJoiner = " ~ ";
    public static String defaultSeparator = ",";

    /**
     * 处理日期区间类型的查询条件。默认使用<code> ~ </code>作为分割符。
     * <p>
     * 目前支持如下的日期格式：yyyy-MM，yyyy-MM-dd，yyyy-MM-dd HH:mm:ss。
     * 格式不正确时不会抛出异常，并将对应的字段值置为 null。
     * <p>
     * 重载方法 {@link #handleDateCondition(String)} 可以自定义分割符。
     */
    public void handleDateCondition() {
        this.handleDateCondition(defaultRangeJoiner);
    }

    public void handleDateCondition(String delimiter) {
        Optional.ofNullable(this.fywtjrq)
                .filter(StringUtils::isNotBlank)
                .map(dateRangeStr -> dateRangeStr.split(delimiter))
                .filter(ywrqqz -> ywrqqz.length == 2)
                .ifPresent(ywrqqz -> {
                    this.fywtjrqStart = this.convertToStartDate(ywrqqz[0]);
                    this.fywtjrqEnd = this.convertToEndDate(ywrqqz[1]);
                });

        Optional.ofNullable(this.fskssrq)
                .filter(StringUtils::isNotBlank)
                .map(dateRangeStr -> dateRangeStr.split(delimiter))
                .filter(skssqqz -> skssqqz.length == 2)
                .ifPresent(skssqqz -> {
                    this.fskssrqStart = this.convertToStartDate(skssqqz[0]);
                    this.fskssrqEnd = this.convertToEndDate(skssqqz[1]);
                });

        Optional.ofNullable(this.fysxkzrq)
                .filter(StringUtils::isNotBlank)
                .map(dateRangeStr -> dateRangeStr.split(delimiter))
                .filter(skssqqz -> skssqqz.length == 2)
                .ifPresent(skssqqz -> {
                    this.fysxkzrqStart = this.convertToStartDate(skssqqz[0]);
                    this.fysxkzrqEnd = this.convertToEndDate(skssqqz[1]);
                });
    }

    private static String yyyy = "yyyy";
    private static String yyyy_MM = "yyyy-MM";
    private static String yyyy_MM_dd = "yyyy-MM-dd";
    private static String yyyy_MM_dd__HH_mm_ss = "yyyy-MM-dd HH:mm:ss";

    public LocalDateTime convertToStartDate(String dateString) {
        try {
            DateTimeFormatter formatter;
            LocalDateTime result;
            if (dateString.length() == 4) {
                formatter = DateTimeFormatter.ofPattern(yyyy);
                result = Year.parse(dateString, formatter).atDay(1).atStartOfDay();
            } else if (dateString.length() == 7) {
                formatter = DateTimeFormatter.ofPattern(yyyy_MM);
                result = YearMonth.parse(dateString, formatter).atDay(1).atStartOfDay();
            } else if (dateString.length() == 10) {
                formatter = DateTimeFormatter.ofPattern(yyyy_MM_dd);
                result = LocalDate.parse(dateString, formatter).atStartOfDay();
            } else {
                formatter = DateTimeFormatter.ofPattern(yyyy_MM_dd__HH_mm_ss);
                result = LocalDateTime.parse(dateString, formatter);
            }
            return result;
        } catch (DateTimeParseException e) {
            log.warning("日期转换失败！" + e.getMessage());
            return null;
        }
    }

    /**
     * 23:59:59
     * @param dateString
     * @return
     */
    public LocalDateTime convertToEndDate(String dateString) {
        try {
            DateTimeFormatter formatter;
            LocalDateTime result;
            if (dateString.length() == 4) {
                formatter = DateTimeFormatter.ofPattern(yyyy);
                result = Year.parse(dateString, formatter).plusYears(1).atDay(1).minusDays(1).atTime(23, 59, 59);
            } else if (dateString.length() == 7) {
                formatter = DateTimeFormatter.ofPattern(yyyy_MM);
                result = YearMonth.parse(dateString, formatter).atEndOfMonth().atTime(23, 59, 59);
            } else if (dateString.length() == 10) {
                formatter = DateTimeFormatter.ofPattern(yyyy_MM_dd);
                result = LocalDate.parse(dateString, formatter).atTime(23, 59, 59);
            } else {
                formatter = DateTimeFormatter.ofPattern(yyyy_MM_dd__HH_mm_ss);
                result = LocalDateTime.parse(dateString, formatter);
            }
            return result;
        } catch (DateTimeParseException e) {
            log.warning("日期转换失败！" + e.getMessage());
            return null;
        }
    }

    public String toSqlLike(String str) {
        return "%" + str + "%";
    }

    public static void main(String[] args) {
        System.out.println(Year.parse("2022").plusYears(1).atDay(1).minusDays(1).atTime(23, 59, 59));
    }

}
