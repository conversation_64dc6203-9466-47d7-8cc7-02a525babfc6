package com.hnbp.local.wqhj.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 政策表;
 * <AUTHOR>
 * @date : 2023-10-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name="tb_dw_jafdc_policy_main")
public class PolicyDO {

    /** 主键id */
    @Id
    @Column(name = "fid")
    private String fid ;

    /** 政策名称 */
    @Column(name = "fname")
    private String fname ;

    /** 政策描述 */
    @Column(name = "fdescription")
    private String fdescription ;

    /** 计算规则 */
    @Column(name = "fcomputation_rule")
    private BigDecimal fcomputationRule ;

    /** 税种 */
    @Column(name = "ftax_type")
    private String ftaxType ;

    /** 税种 */
    @Column(name = "ftax_type_name")
    private String ftaxTypeName ;

    /** 政策实施起始年月 */
    @Column(name = "factive_start_time")
    private String factiveStartTime ;

    /** 政策实施结束年月 */
    @Column(name = "factive_end_time")
    private String factiveEndTime ;

    /** 所属环节 */
    @Column(name = "fphase")
    private String fphase ;

    /** 所属环节-中文 */
    @Column(name = "fphase_name")
    private String fphaseName ;


}
