package com.hnbp.local.wqhj.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OnlineSigningPhaseQUERY extends OnlineSigningPhaseBaseQUERY {
    @ApiModelProperty(name = "房地产企业名称")
    private String fdcqymc;

    @ApiModelProperty(name = "预计到账时间")
    private Integer yjdzsj;

    @ApiModelProperty(name = "是否包含留抵退税", notes = "0 = 不包含, 1 = 包含")
    private Integer ldts;

    @ApiModelProperty(name = "是否存在商贷、公积金贷款数据")
    private Boolean loanFlag;

    /** sl: 税率，zsl：征收率，Bd：本地，Wd：外地*/

    @ApiModelProperty(name = "附加税征收率-城区")
    private BigDecimal zslFjsCq;

    @ApiModelProperty(name = "附加税征收率-县区")
    private BigDecimal zslFjsXq;

    @ApiModelProperty(name = "增值税征收率")
    private BigDecimal zslYjZzs;

    private BigDecimal zslTdzzsPtzz;

    private BigDecimal zslTdzzsFptzz;

    private BigDecimal zslTdzzsFzz;
}
/**
 * 预计营收测算规则：
 * 有无贷款数据：
 *      无贷款数据：
 *          按付款方式：
 *              一次性付款（1） -> 网签金额
 *              按揭贷款（2）、其他方式（3） -> 过了预计时间就取网签金额，没过预计时间就只取首付款
 *      有贷款数据：
 *          首付款 + 商贷金额 + 公积金贷款金额
 *
 */
