package com.hnbp.local.wqhj.mapper;

import com.github.pagehelper.Page;

import com.hnbp.local.wqhj.model.PolicyDO;
import com.hnbp.local.wqhj.model.PolicyQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


import java.util.List;

@Mapper
@Repository
public interface PolicyMapper extends tk.mybatis.mapper.common.Mapper<PolicyDO> {

    /**
     * 查询个环节查询条件中的政策信息
     * @param query
     * @return
     */
    List<PolicyDO> findPolicyQuery(PolicyQuery query);

    /**
     *查询所有政策信息
     * @param query
     * @return
     */
    Page<PolicyDO> queryPolicyList(PolicyQuery query);

    /**
     * 查询所属环节的税种或计算标准
     * @param query
     * @return
     */
    List<PolicyDO> queryTaxType(PolicyQuery query);
}
