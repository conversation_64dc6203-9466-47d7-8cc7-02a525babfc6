package com.hnbp.local.wqhj.tool;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;

import java.io.IOException;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 序列化 BigDecimal。四舍五入，保留两位小数，不去掉尾部多余的 0。
 */
public class BigDecimalSerializerScale2 extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal fieldValue, JsonGenerator jsonGenerator,
                          SerializerProvider serializerProvider) throws IOException {
        if (null != fieldValue) {
            BigDecimal result = fieldValue.setScale(2, RoundingMode.HALF_UP);
            jsonGenerator.writeString(result.toPlainString());
        } else {
            jsonGenerator.writeNull();
        }
    }

    /**
     * 可能前端也会 pretty ？
     * @param args
     * @throws IOException
     */
    public static void main(String[] args) throws IOException {
        BigDecimal var1 = new BigDecimal("100.00");
        BigDecimal var2 = new BigDecimal("123.0000");

        ObjectMapper mapper = new ObjectMapper();
        JsonNodeFactory f = JsonNodeFactory.withExactBigDecimals(true);
        mapper.setNodeFactory(f);

        StringWriter sw = new StringWriter();
        mapper.writeValue(sw, var1);
        sw.append("\n");
        mapper.writeValue(sw, var2);
        System.out.println(sw); // 100.00 和 123.0000
    }
}
