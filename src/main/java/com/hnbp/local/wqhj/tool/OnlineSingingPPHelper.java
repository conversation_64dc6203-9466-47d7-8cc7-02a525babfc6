package com.hnbp.local.wqhj.tool;

import com.hnbp.local.wqhj.model.OnlineSigningPhaseBaseQUERY;
import com.hnbp.local.wqhj.model.PolicyDO;
import com.hnbp.local.wqhj.service.PolicyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 施工阶段政策选择
 */
@Component
public class OnlineSingingPPHelper {
    @Autowired
    private PolicyService policyService;

    private static final String phase = "onlineSigning_phase";

    private static final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 默认的政策匹配规则
    BiPredicate<PolicyDO, OnlineSigningPhaseBaseQUERY> defaultPredict = (policy, query) -> {
        String activeStartTime = policy.getFactiveStartTime().length() > 10 ? policy.getFactiveStartTime() : policy.getFactiveStartTime() + " 00:00:00";
        String activeEndTime = policy.getFactiveEndTime();
        LocalDateTime activeStart = LocalDateTime.parse(activeStartTime, dtf);
        LocalDateTime activeEnd;
        if (Objects.equals(activeEndTime, "NOW")) {
            activeEnd = LocalDateTime.now();
        } else {
            activeEnd = LocalDateTime.parse(activeEndTime, dtf);
        }
        LocalDateTime fywtjrqStart = Optional.ofNullable(query.getFywtjrqStart()).orElse(LocalDateTime.MIN);
        LocalDateTime fywtjrqEnd = Optional.ofNullable(query.getFywtjrqEnd()).orElse(LocalDateTime.MAX);
        // [政策有效期起 -> 业务日期起 -> 业务日期止 -> 政策有效期止]
        return activeStart.isBefore(fywtjrqStart) && activeEnd.isAfter(fywtjrqEnd);
    };

    public Map<TaxType, BigDecimal> predictQualifiedPolicy(OnlineSigningPhaseBaseQUERY query) {
        List<PolicyDO> policyDOList = policyService.listByPhase(phase);

        // 匹配规则，获取各个税种的征收率
        Map<String, List<PolicyDO>> taxTypePoliciesMap = policyDOList.stream().collect(Collectors.groupingBy(PolicyDO::getFtaxType));
        HashMap<TaxType, BigDecimal> result = new HashMap<>();
        for (TaxType taxType : TaxType.values()) {
            String name = taxType.getName();
            List<PolicyDO> policies = taxTypePoliciesMap.get(name) == null
                                        ? Collections.emptyList()
                                        : taxTypePoliciesMap.get(name);
            BigDecimal taxRate = policies.stream()
                    .filter(policy -> defaultPredict.test(policy, query))
                    .findFirst()
                    .map(PolicyDO::getFcomputationRule)
                    .orElse(taxType.getFallbackValue());
            result.put(taxType, taxRate);
        }

        return result;
    }

    public enum TaxType {
        ZslYjZzs("zslYjZzs", "预缴增值税征收率", new BigDecimal("0.03")),
        ZslFjsCq("zslFjsCq", "附加税征收率（城区）", new BigDecimal("0.12")),
        ZslFjsXq("zslFjsXq", "附加税征收率（县区）", new BigDecimal("0.10")),
        ZslTdzzsPtzz("zslTdzzsPtzz", "土地增值税征收率（普通住宅）", new BigDecimal("0.15")),
        ZslTdzzsFptzz("zslTdzzsFptzz", "土地增值税征收率（非普通住宅）", new BigDecimal("0.02")),
        ZslTdzzsFzz("zslTdzzsFzz", "土地增值税征收率（非住宅）", new BigDecimal("0.03")),
        ;

        private final String name;
        private final String desc;
        private final BigDecimal fallbackValue;

        TaxType(String name, String desc, BigDecimal fallbackValue) {
            this.name = name;
            this.desc = desc;
            this.fallbackValue = fallbackValue;
        }

        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }

        public BigDecimal getFallbackValue() {
            return fallbackValue;
        }

        public TaxType codeOf(String name) {
            for (TaxType value : TaxType.values()) {
                if (Objects.equals(name, value.getName())) {
                    return value;
                }
            }
            throw new RuntimeException("没有找到对应的枚举");
        }
    }

    private static void generateInsertSql() {
        Function<Object, String> append = (item) -> "'" + item + "'";

        String insertPart =
                "INSERT INTO zhzs_bill_jafdc_policy\n" +
                "    (fid, fphase, ftax_type, fcomputation_rule, fname, fdescription, factive_start_name, factive_end_time)\n" +
                "VALUES\n";
        String valuePart = Stream.of(TaxType.values())
                .map(item -> {
                    String name = item.getName();
                    String desc = item.getDesc();
                    BigDecimal fallbackValue = item.getFallbackValue();
                    UUID uuid = UUID.randomUUID();
                    String str = "(" + append.apply(uuid) + ", "
                            + append.apply(phase) + ", "
                            + append.apply(name) + ", "
                            + append.apply(fallbackValue) + ", "
                            + append.apply(desc) + ","
                            + append.apply(desc + "的描述") + ", "
                            + append.apply("2018-01-01 00:00:00") + ", "
                            + append.apply("NOW") + ")";
                    return str;
                })
                .collect(Collectors.joining(",\n"));

        System.out.println(insertPart + valuePart);
    }

    public static void main(String[] args) {
        generateInsertSql();
    }
}
