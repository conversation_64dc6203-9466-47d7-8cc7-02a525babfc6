package com.hnbp.local.wqhj.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.hnbp.local.wqhj.mapper.PolicyMapper;
import com.hnbp.local.wqhj.model.PolicyDO;
import com.hnbp.local.wqhj.model.PolicyQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class PolicyService {
    @Autowired
    private PolicyMapper policyMapper;


    public List<PolicyDO> listByPhase(String phase) {
        PolicyQuery query = PolicyQuery.builder().fphase(phase).build();

        return policyMapper.findPolicyQuery(query);
    }

    /**
     * 查询个环节查询条件中的政策信息（税率政策）
     * @param policyQuery
     * @return
     */
    public Map<String, List<PolicyDO>> queryPolicy(PolicyQuery policyQuery) {
        policyQuery.handleDateCondition();
        List<PolicyDO> policyList = policyMapper.findPolicyQuery(policyQuery);
        Map<String, List<PolicyDO>> result = policyList.stream().collect(Collectors.groupingBy(PolicyDO::getFtaxType));
        return result;
    }

    /**
     * 查询所有政策信息
     * @param policyQuery
     * @return
     */
    public Page queryPolicyList(PolicyQuery policyQuery) {
        policyQuery.handleDateCondition();
        PageHelper.startPage(policyQuery.getPage(),policyQuery.getLimit());
        Page policyList = policyMapper.queryPolicyList(policyQuery);
        return policyList;
    }




    /**
     * 查询所属环节的税种或计算标准
     * @param policyQuery
     * @return
     */
    public List<PolicyDO> queryTaxType(PolicyQuery policyQuery) {
        return policyMapper.queryTaxType(policyQuery);
    }
}
