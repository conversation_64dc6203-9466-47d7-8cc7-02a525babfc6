package com.hnbp.local.wqhj.service;

import cn.hutool.core.util.IdUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.hnbp.local.wqhj.mapper.OnlineSigningPhaseMapper;
import com.hnbp.local.wqhj.model.OnlineSigningPhaseDetailQUERY;
import com.hnbp.local.wqhj.model.OnlineSigningPhaseQUERY;
import com.hnbp.local.wqhj.model.OnlineSigningPhaseVO;
import com.hnbp.local.wqhj.tool.OnlineSingingPPHelper;
import lombok.extern.java.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;


@Log
@Service
public class OnlineSigningPhaseService {
    @Autowired
    private OnlineSigningPhaseMapper onlineSigningPhaseMapper;

    @Autowired
    private OnlineSingingPPHelper policyHelper;

    public Page<OnlineSigningPhaseVO> overview(OnlineSigningPhaseQUERY query, boolean pageable) {
        // 处理查询条件
        query.handleDateCondition();

        // todo 查询政策，默认值替换
        this.replaceTaxRate(query);

        Page<OnlineSigningPhaseVO> result;
        // 根据有无商贷信息走不同的查询
//        if (query.getLoanFlag()) {
//            // 有商贷数据
//            result = this.overviewWithLoan(query, pageable);
//        } else {
            // 没有商贷数据
            result = this.overviewWithoutLoan(query, pageable);
//        }

        BigDecimal base = BigDecimal.valueOf(100);
        result.forEach(record -> {
            Optional.ofNullable(record.getFyjzsbl()).map(yjzsbl -> yjzsbl.multiply(base).setScale(2, RoundingMode.HALF_UP)).ifPresent(record::setFyjzsbl);
            Optional.ofNullable(record.getFztsfl()).map(ztsfl -> ztsfl.multiply(base).setScale(2, RoundingMode.HALF_UP)).ifPresent(record::setFztsfl);
        });

        return result;
    }

    public Page<OnlineSigningPhaseVO> overviewWithoutLoan(OnlineSigningPhaseQUERY query, boolean pageable) {
        // 查询数据
        if (pageable) {
            PageHelper.startPage(query.getPage(), query.getLimit());
        }
        Page<OnlineSigningPhaseVO> result = onlineSigningPhaseMapper.overviewWithoutLoan(query);

        return result;
    }

    private void replaceTaxRate(OnlineSigningPhaseQUERY query) {
        Map<OnlineSingingPPHelper.TaxType, BigDecimal> taxRates = policyHelper.predictQualifiedPolicy(query);

        if (query.getZslFjsCq() == null) {
            query.setZslFjsCq(taxRates.get(OnlineSingingPPHelper.TaxType.ZslFjsCq));
        }
        if (query.getZslFjsXq() == null) {
            query.setZslFjsXq(taxRates.get(OnlineSingingPPHelper.TaxType.ZslFjsXq));
        }
        if (query.getZslYjZzs() == null) {
            query.setZslYjZzs(taxRates.get(OnlineSingingPPHelper.TaxType.ZslYjZzs));
        }
        if (query.getZslTdzzsPtzz() == null) {
            query.setZslTdzzsPtzz(taxRates.get(OnlineSingingPPHelper.TaxType.ZslTdzzsPtzz));
        }
        if (query.getZslTdzzsFptzz() == null) {
            query.setZslTdzzsFptzz(taxRates.get(OnlineSingingPPHelper.TaxType.ZslTdzzsFptzz));
        }
        if (query.getZslTdzzsFzz() == null) {
            query.setZslTdzzsFzz(taxRates.get(OnlineSingingPPHelper.TaxType.ZslTdzzsFzz));
        }
    }

    private void replaceTaxRate(OnlineSigningPhaseDetailQUERY query) {
        Map<OnlineSingingPPHelper.TaxType, BigDecimal> taxRates = policyHelper.predictQualifiedPolicy(query);

        if (query.getZslFjsCq() == null) {
            query.setZslFjsCq(taxRates.get(OnlineSingingPPHelper.TaxType.ZslFjsCq));
        }
        if (query.getZslFjsXq() == null) {
            query.setZslFjsXq(taxRates.get(OnlineSingingPPHelper.TaxType.ZslFjsXq));
        }
        if (query.getZslYjZzs() == null) {
            query.setZslYjZzs(taxRates.get(OnlineSingingPPHelper.TaxType.ZslYjZzs));
        }
        if (query.getZslTdzzsPtzz() == null) {
            query.setZslTdzzsPtzz(taxRates.get(OnlineSingingPPHelper.TaxType.ZslTdzzsPtzz));
        }
        if (query.getZslTdzzsFptzz() == null) {
            query.setZslTdzzsFptzz(taxRates.get(OnlineSingingPPHelper.TaxType.ZslTdzzsFptzz));
        }
        if (query.getZslTdzzsFzz() == null) {
            query.setZslTdzzsFzz(taxRates.get(OnlineSingingPPHelper.TaxType.ZslTdzzsFzz));
        }
    }
}
