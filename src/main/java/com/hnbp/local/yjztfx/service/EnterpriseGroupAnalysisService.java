package com.hnbp.local.yjztfx.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.utils.StringUtils;
import com.hnbp.local.yjztfx.mapper.EnterpriseGroupAnalysisMapper;
import com.hnbp.local.yjztfx.model.EnterpriseGroupAnalysis;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EnterpriseGroupAnalysisService {

    @Autowired
    private EnterpriseGroupAnalysisMapper enterpriseGroupAnalysisMapper;


    /**
     * 企业集团明细列表
     */
    public PageInfo getEnterpriseDetailsList(Map<String, Object> params) {
        String page = params.getOrDefault("page", "1").toString();
        String limit = params.getOrDefault("limit", "100").toString();
        PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(limit));
        return new PageInfo<>(enterpriseGroupAnalysisMapper.getEnterpriseDetailsList(params));
    }

    /**
     * 企业集团分总列表
     */
    public List<EnterpriseGroupAnalysis> getEnterpriseGroupList(Map<String, Object> params) {
        // 只查询父级
        params.put("treeShow", "true");
        List<EnterpriseGroupAnalysis> totalList = enterpriseGroupAnalysisMapper.getEnterpriseGroupList(params);
        if (totalList != null && !totalList.isEmpty()) {
            // 查询所有企业明细
            List<EnterpriseGroupAnalysis> detailsList = enterpriseGroupAnalysisMapper.getEnterpriseDetailsList(params);
            // 根据 集团(fqymc)分组
            Map<Object, List<EnterpriseGroupAnalysis>> groupList = detailsList.stream().collect(Collectors.groupingBy(EnterpriseGroupAnalysis::getFqymc));

            // 递归查找下级
            totalList = findChildrenList(totalList,groupList);

            // 递归计算集团合计
            computeChildrenSum(totalList);
        }
        return totalList;
    }


    /** 递归查找子集企业列表 */
    public List<EnterpriseGroupAnalysis> findChildrenList(List<EnterpriseGroupAnalysis> totalList, Map<Object, List<EnterpriseGroupAnalysis>> groupList) {
        if  (totalList == null ) return null;
        totalList.forEach(item -> {
            String parentName = item.getFjtcy();
            if (StringUtils.isBlank(parentName)) {
                // 集团成员名称为空时 取集团本身名称
                parentName = item.getFqymc();
            }
            List<EnterpriseGroupAnalysis> childrenList = groupList.get(parentName);
            if (childrenList != null && !childrenList.isEmpty() && !"集团本身".equals(item.getFcycj())) {
                childrenList = findChildrenList(childrenList,groupList);
                item.setChildren(childrenList);
            }
        });
        return totalList;
    }
    
    /** 递归计算 集团的子集企业 合计 */
    public void computeChildrenSum(List<EnterpriseGroupAnalysis> totalList) {
        totalList.forEach(item -> {
            List<EnterpriseGroupAnalysis> childrenList = item.getChildren();
            if (childrenList != null && !childrenList.isEmpty()) {
                computeChildrenSum(childrenList);
                Double fhjShjSum = childrenList.stream().mapToDouble(e -> e.getFhj_shj() == null ? 0.0d : e.getFhj_shj()).sum();
                Double fhjZzsSum = childrenList.stream().mapToDouble(e -> e.getFhj_zzs() == null ? 0.0d : e.getFhj_zzs()).sum();
                Double fhjQysdsSum = childrenList.stream().mapToDouble(e -> e.getFhj_qysds() == null ? 0.0d : e.getFhj_qysds()).sum();
                Double fhjqnShjSum = childrenList.stream().mapToDouble(e -> e.getFhjqn_shj() == null ? 0.0d : e.getFhjqn_shj()).sum();
                Double fhjqnZzsSum = childrenList.stream().mapToDouble(e -> e.getFhjqn_zzs() == null ? 0.0d : e.getFhjqn_zzs()).sum();
                Double fhjqnQysdsSum = childrenList.stream().mapToDouble(e -> e.getFhjqn_qysds() == null ? 0.0d : e.getFhjqn_qysds()).sum();

                item.setFhj_shj(Math.round(fhjShjSum * 100) / 100.0);
                item.setFhj_zzs(Math.round(fhjZzsSum * 100) / 100.0);
                item.setFhj_qysds(Math.round(fhjQysdsSum * 100) / 100.0);
                item.setFhjqn_shj(Math.round(fhjqnShjSum * 100) / 100.0);
                item.setFhjqn_zzs(Math.round(fhjqnZzsSum * 100) / 100.0);
                item.setFhjqn_qysds(Math.round(fhjqnQysdsSum * 100) / 100.0);

                Integer fqyslSum = childrenList.stream().mapToInt(e -> e.getFqysl() == null ? 1 : e.getFqysl()).sum();
                // 集团成员数量
                item.setFqysl(fqyslSum);

                // 增加额 保留两位小数
                item.setFzje_shj(Math.round((item.getFhj_shj() - item.getFhjqn_shj()) * 100 )/ 100.0);
                // 增减率 = 今年增加额 / 去年 * 100 保留两位小数
                if (item.getFhjqn_shj() == null || Math.abs(item.getFhjqn_shj()) < 0.0001) {
                    item.setFzjl_shj(0.0);
                } else {
                    item.setFzjl_shj(Math.round(item.getFzje_shj() / item.getFhjqn_shj() * 100 * 100) / 100.0);
                }
            }
        });
    }

}
