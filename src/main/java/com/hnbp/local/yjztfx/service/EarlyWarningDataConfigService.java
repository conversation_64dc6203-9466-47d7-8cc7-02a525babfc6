package com.hnbp.local.yjztfx.service;

import com.hnbp.local.yjztfx.mapper.EarlyWarningDataConfigMapper;
import com.hnbp.local.yjztfx.model.DataTableNameConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class EarlyWarningDataConfigService {

    @Autowired
    private EarlyWarningDataConfigMapper earlyWarningDataConfigMapper;

    /** 获取数据源的表名配置 */
    public List<DataTableNameConfig> getDataTableNameConfigList(HashMap<String, Object> params) {
        List<DataTableNameConfig> dataList = earlyWarningDataConfigMapper.getDataTableNameConfigList(params);
        // for (DataTableNameConfig item : dataList) {
        //     // 非贴源层表(以TB_ODS_开头的) 没有调度周期
        //     if (!item.getTableName().toLowerCase().startsWith("tb_ods_")) {
        //         continue;
        //     }
        //     HashMap<String, Object> paramsMap = MapUtil.newHashMap();
        //     paramsMap.put("tableName", item.getTableName());
        //     try {
        //         Map<String, Object> tableDataTime = earlyWarningDataConfigMapper.findTableDataTime(paramsMap);
        //         item.setTableDate(tableDataTime);
        //     } catch (Exception e) {
        //         log.error("获取数据源表数据最新/最旧时间周期异常：{}", e.getMessage());
        //     }
        // }
        return dataList;
    }

     /** 获取数据源表数据最新/最旧时间周期 */
    public Map<String, Object> findTableDataTime(HashMap<String, Object> params) {
        return earlyWarningDataConfigMapper.findTableDataTime(params);
    }

}
