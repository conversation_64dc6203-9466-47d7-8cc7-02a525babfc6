package com.hnbp.local.yjztfx.service;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.utils.uuid.UUID;
import com.hnbp.local.yjztfx.mapper.EnterpriseManageMapper;
import com.hnbp.local.yjztfx.model.EnterpriseInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * 企业集团名管理
 */
@Service
public class EnterpriseManageService {

    @Autowired
    private EnterpriseManageMapper enterpriseManageMapper;


    /**
     * 保存企业信息（新增或更新）
     *
     * @param enterpriseInfo 企业信息对象
     * @return 操作结果
     */
    public int saveEnterpriseInfo(EnterpriseInfo enterpriseInfo) {
        if (StrUtil.isBlank(enterpriseInfo.getFid())) {
            return insertEnterpriseInfo(enterpriseInfo);
        } else {
            return updateEnterpriseInfo(enterpriseInfo);
        }
    }

    /**
     * 插入企业信息
     *
     * @param enterpriseInfo 企业信息对象
     * @return 插入结果
     */
    public int insertEnterpriseInfo(EnterpriseInfo enterpriseInfo) {
        enterpriseInfo.setFid(UUID.fastUUID().toString());
        return enterpriseManageMapper.insertEnterpriseInfo(enterpriseInfo);
    }

    /**
     * 更新企业信息
     *
     * @param enterpriseInfo 企业信息对象
     * @return 更新结果
     */
    public int updateEnterpriseInfo(EnterpriseInfo enterpriseInfo) {
        return enterpriseManageMapper.updateEnterpriseInfo(enterpriseInfo);
    }

    /**
     * 删除企业信息
     *
     * @param fid 主键ID
     * @return 删除结果
     */
    public int deleteEnterpriseInfo(String fid) {
        return enterpriseManageMapper.deleteEnterpriseInfo(fid);
    }

    /**
     * 查询所有企业信息
     *
     * @return 企业信息列表
     */
    public PageInfo<EnterpriseInfo> selectAllEnterpriseInfo(HashMap<String, Object> params) {
        String page = params.getOrDefault("page", "1").toString();
        String limit = params.getOrDefault("limit", "100").toString();
        PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(limit));

        List<EnterpriseInfo> dataList = enterpriseManageMapper.selectAllEnterpriseInfo(params);
        return new PageInfo<>(dataList);
    }

    /**
     * 根据主键查询企业信息
     *
     * @param fid 主键ID
     * @return 企业信息对象
     */
    public EnterpriseInfo selectEnterpriseInfoById(String fid) {
        return enterpriseManageMapper.selectEnterpriseInfoById(fid);
    }
}