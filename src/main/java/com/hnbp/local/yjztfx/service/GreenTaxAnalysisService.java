package com.hnbp.local.yjztfx.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.local.util.echarts.Bar.BarChart;
import com.hnbp.local.util.echarts.Bar.BarSeriesInitData;
import com.hnbp.local.util.util.ChartUtil;
import com.hnbp.local.yjztfx.mapper.GreenTaxAnalysisMapper;
import com.hnbp.local.yjztfx.model.AlertItemsConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GreenTaxAnalysisService {

    @Autowired
    private GreenTaxAnalysisMapper greenTaxAnalysisMapper;

    @Autowired
    private AlertItemsConfigService alertItemsConfigService;

    /**
     * 施工许可证明细列表
     */
    public PageInfo getConstructionPermitsDetailsList(Map<String, Object> params) {
        String page = params.getOrDefault("page", "1").toString();
        String limit = params.getOrDefault("limit", "100").toString();
        PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(limit));
        return new PageInfo<>(greenTaxAnalysisMapper.getConstructionPermitsDetailsList(params));
    }

    /**
     * 施工许可证分总列表
     */
    public PageInfo getConstructionPermitsTotalList(Map<String, Object> params) {
        String page = params.getOrDefault("page", "1").toString();
        String limit = params.getOrDefault("limit", "100").toString();
        PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(limit));
        List<Map<String, Object>> totalList = greenTaxAnalysisMapper.getConstructionPermitsTotalList(params);
        if (totalList != null && !totalList.isEmpty()) {
            // 取 map 中的fsgdw值
            List<Object> fsgdwList = totalList.stream().map(map -> map.get("fsgdw")).collect(Collectors.toList());
            params.put("fsgdwList", fsgdwList);
            List<Map<String, Object>> detailsList = greenTaxAnalysisMapper.getConstructionPermitsDetailsList(params);


            // 纳税人名称List根据 施工单位(fsgdw)分组
            Map<Object, List<Map<String, Object>>> groupList = detailsList.stream().collect(Collectors.groupingBy(map -> map.get("fsgdw")));
            totalList.forEach(map -> {
                map.put("children", groupList.get(map.get("fsgdw")));
                // map.put("isParent", "true");
            });
        }
        return new PageInfo<>(totalList);
    }



    /** 分属地类型施工企业施工及环保税缴纳情况 */
    public BarChart getTerritorialConstructionThings(Map<String, Object> params) {
        List<BarSeriesInitData> barSeriesInitDataList = greenTaxAnalysisMapper.getTerritorialConstructionThings(params);
        return ChartUtil.BieCharDataAssembly(barSeriesInitDataList, new HashMap());
    }



    /** 项目营收分析列表 */
    public PageInfo getProjectRevenueList(Map<String, Object> params) {
        String page = params.getOrDefault("page", "1").toString();
        String limit = params.getOrDefault("limit", "100").toString();
        PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(limit));
        List<Map<String, Object>> totalList = greenTaxAnalysisMapper.getProjectRevenueList(params);
        return new PageInfo<>(totalList);
    }


    /** 项目进度分析列表 */
    public PageInfo getProjectProgressList(Map<String, Object> params) {
        String page = params.getOrDefault("page", "1").toString();
        String limit = params.getOrDefault("limit", "100").toString();
        PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(limit));
        List<Map<String, Object>> totalList = greenTaxAnalysisMapper.getProjectProgressList(params);
        List<AlertItemsConfig> configList = alertItemsConfigService.selectAllAlertItemsConfig(new HashMap());
        Map<Object, List<AlertItemsConfig>> groupList = configList.stream().collect(Collectors.groupingBy(AlertItemsConfig::getFsgxkz_fid));
        totalList.forEach(map -> {
            String fsgxkz_fid = map.getOrDefault("fid","").toString();
            List<AlertItemsConfig> list = groupList.get(fsgxkz_fid);
            if (list != null && !list.isEmpty()) {
                list.forEach(item -> {
                    switch (item.getFyjx()) {
                        case "增值税":
                            map.put("fzt_zzs", item.getFzt());
                            map.put("fjzrq_zzs", item.getFjzrq());
                            break;
                        case "环保税":
                            map.put("fzt_hbs", item.getFzt());
                            map.put("fjzrq_hbs", item.getFjzrq());
                            break;
                        case "企业所得税":
                            map.put("fzt_qysds", item.getFzt());
                            map.put("fjzrq_qysds", item.getFjzrq());
                            break;
                        case "印花税":
                            map.put("fzt_yhs", item.getFzt());
                            map.put("fjzrq_yhs", item.getFjzrq());
                            break;
                    }
                });
            }
        });

        return new PageInfo<>(totalList);
    }
}
