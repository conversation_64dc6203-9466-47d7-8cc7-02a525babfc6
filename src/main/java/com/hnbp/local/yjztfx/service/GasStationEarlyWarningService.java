package com.hnbp.local.yjztfx.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.local.util.echarts.Bar.BarChart;
import com.hnbp.local.util.echarts.Bar.BarSeriesInitData;
import com.hnbp.local.util.util.ChartUtil;
import com.hnbp.local.yjztfx.mapper.GasStationEarlyWarningMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class GasStationEarlyWarningService {


    @Autowired
    private GasStationEarlyWarningMapper gasStationEarlyWarningMapper;

    /**
     * 获取加油站区域汇总列表
     */
    public List<Map<String, Object>> getGasStationRegionRollupList(Map<String, Object> params) {
        return gasStationEarlyWarningMapper.getGasStationRegionRollupList(params);
    }
    /**
     * 加油站明细列表
     */
    public PageInfo getGasStationDetailsList(Map<String, Object> params) {
        String page = params.getOrDefault("page", "1").toString();
        String limit = params.getOrDefault("limit", "100").toString();
        PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(limit));
        return new PageInfo<>(gasStationEarlyWarningMapper.getGasStationDetailsList(params));
    }

    /**
     * 加油站明细平均线
     */
    public List<Map<String, Object>> getGasStationDetailsAverages(Map<String, Object> params) {
        return gasStationEarlyWarningMapper.getGasStationDetailsAverages(params);
    }

    /**
     * 获取加油站房土两税区域对比数据
     */
    public BarChart getHousingAndLandTaxRegionContrast(Map<String, Object> params) {
        List<BarSeriesInitData> barSeriesInitDataList = gasStationEarlyWarningMapper.getHousingAndLandTaxRegionContrast(params);
        return ChartUtil.BieCharDataAssembly(barSeriesInitDataList, new HashMap());
    }

    /**
     * 获取增值税平均每吨油税额对比数据
     */
    public BarChart getIncrementTaxAvgOilContrast(Map<String, Object> params) {
        List<BarSeriesInitData> barSeriesInitDataList = gasStationEarlyWarningMapper.getIncrementTaxAvgOilContrast(params);
        return ChartUtil.BieCharDataAssembly(barSeriesInitDataList, new HashMap());
    }

    /**
     * 获取增值税可征收空间对比数据
     */
    public BarChart getIncrementTaxExpropriableContrast(Map<String, Object> params) {
        List<BarSeriesInitData> barSeriesInitDataList = gasStationEarlyWarningMapper.getIncrementTaxExpropriableContrast(params);
        return ChartUtil.BieCharDataAssembly(barSeriesInitDataList, new HashMap());
    }
}