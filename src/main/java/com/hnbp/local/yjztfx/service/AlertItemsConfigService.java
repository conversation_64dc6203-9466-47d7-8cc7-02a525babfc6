package com.hnbp.local.yjztfx.service;

import cn.hutool.core.map.MapUtil;
import com.hnbp.common.core.utils.uuid.UUID;
import com.hnbp.local.yjztfx.mapper.AlertItemsConfigMapper;
import com.hnbp.local.yjztfx.model.AlertItemsConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 施工许可证预警项配置
 */
@Service
public class AlertItemsConfigService {

    @Autowired
    private AlertItemsConfigMapper alertItemsConfigMapper;


    /**
     * 保存企业信息（新增或更新）
     *
     * @return 操作结果
     */
    public void batchSave(List<AlertItemsConfig> listData) {
        // 获取listData 的所有fsgxkz_fid
        String fsgxkzfid = listData.stream().map(AlertItemsConfig::getFsgxkz_fid).collect(Collectors.joining(","));
        HashMap<String, Object> params = MapUtil.newHashMap();
        params.put("fsgxkzfid", fsgxkzfid);
        deleteAlertItemsConfig(params);
        listData.forEach(item -> {
            item.setFid(UUID.fastUUID().toString());
            insertAlertItemsConfig(item);
        });
    }

    /**
     * 插入企业信息
     *
     * @param alertItemsConfig 企业信息对象
     * @return 插入结果
     */
    public int insertAlertItemsConfig(AlertItemsConfig alertItemsConfig) {
        alertItemsConfig.setFid(UUID.fastUUID().toString());
        return alertItemsConfigMapper.insertAlertItemsConfig(alertItemsConfig);
    }


    /**
     * 删除企业信息
     *
     * @return 删除结果
     */
    public int deleteAlertItemsConfig(HashMap<String, Object> params) {
        return alertItemsConfigMapper.deleteAlertItemsConfig(params);
    }

    /**
     * 查询所有企业信息
     *
     * @return 企业信息列表
     */
    public List<AlertItemsConfig> selectAllAlertItemsConfig(HashMap<String, Object> params) {
        return alertItemsConfigMapper.selectAllAlertItemsConfig(params);
    }

}