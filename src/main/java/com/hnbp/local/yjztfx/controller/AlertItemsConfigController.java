package com.hnbp.local.yjztfx.controller;

import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.yjztfx.model.AlertItemsConfig;
import com.hnbp.local.yjztfx.service.AlertItemsConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 施工许可证预警项配置控制器
 */
@Controller
@RequestMapping("/yjzt/hbs")
@ResponseBody
public class AlertItemsConfigController {

    @Autowired
    private AlertItemsConfigService alertItemsConfigService;

    /**
     * 批量保存预警项配置
     *
     * @return 操作结果
     */
    @RequestMapping("/batchSaveAlertItemsConfig")
    public ResultMsg batchSave(@RequestBody List<AlertItemsConfig> listData) {
        alertItemsConfigService.batchSave(listData);
        return ResultMsg.success();
    }

}