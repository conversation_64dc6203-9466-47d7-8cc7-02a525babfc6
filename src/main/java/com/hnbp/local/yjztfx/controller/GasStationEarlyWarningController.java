package com.hnbp.local.yjztfx.controller;

import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.yjztfx.service.GasStationEarlyWarningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

/**
 * 加油站预警控制器
 */
@Controller
@RequestMapping("/yjzt/jyz")
@ResponseBody
public class GasStationEarlyWarningController {

    @Autowired
    private GasStationEarlyWarningService gasStationEarlyWarningService;

    /**
     * 获取加油站区域汇总列表
     */
    @RequestMapping("/getGasStationRegionRollupList")
    public ResultMsg getGasStationRegionRollupList(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(gasStationEarlyWarningService.getGasStationRegionRollupList(params));
    }

    /**
     * 加油站明细列表
     */
    @RequestMapping("/getGasStationDetailsList")
    public ResultMsg getGasStationDetailsList(@RequestParam Map<String, Object> params) {
        PageInfo pageInfo = gasStationEarlyWarningService.getGasStationDetailsList(params);
        return ResultMsg.success(pageInfo.getList(), pageInfo.getTotal());
    }


    /**
     * 加油站明细平均线
     */
    @RequestMapping("/getGasStationDetailsAverages")
    public ResultMsg getGasStationDetailsAverages(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(gasStationEarlyWarningService.getGasStationDetailsAverages(params));
    }


    /**
     * 获取加油站房土两税区域对比数据
     */
    @RequestMapping("/getHousingAndLandTaxRegionContrast")
    public ResultMsg getHousingAndLandTaxRegionContrast(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(gasStationEarlyWarningService.getHousingAndLandTaxRegionContrast(params));
    }

    /**
     * 获取增值税平均每吨油税额对比数据
     */
    @RequestMapping("/getIncrementTaxAvgOilContrast")
    public ResultMsg getIncrementTaxAvgOilContrast(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(gasStationEarlyWarningService.getIncrementTaxAvgOilContrast(params));
    }

    /**
     * 获取增值税可征收空间对比数据
     */
    @RequestMapping("/getIncrementTaxExpropriableContrast")
    public ResultMsg getIncrementTaxExpropriableContrast(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(gasStationEarlyWarningService.getIncrementTaxExpropriableContrast(params));
    }
}