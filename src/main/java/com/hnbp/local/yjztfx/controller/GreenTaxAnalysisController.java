package com.hnbp.local.yjztfx.controller;

import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.yjztfx.service.GreenTaxAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

/**
 * 环保税专题分析
 */
@Controller
@RequestMapping("/yjzt/hbs")
@ResponseBody
public class GreenTaxAnalysisController {

    @Autowired
    private GreenTaxAnalysisService greenTaxAnalysisService;

    /**
     * 施工许可证分总列表
     */
    @RequestMapping("/getConstructionPermitsTotalList")
    public ResultMsg getConstructionPermitsTotalList(@RequestParam Map<String, Object> params) {
        PageInfo pageInfo = null;
        // if ("1".equals(params.get("ffzhb"))){
            // 合并显示
            pageInfo = greenTaxAnalysisService.getConstructionPermitsTotalList(params);
        // }else {
        //     pageInfo = greenTaxAnalysisService.getConstructionPermitsDetailsList(params);
        // }
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }



    /**
     * 分属地类型施工企业施工及环保税缴纳情况
     */
    @RequestMapping("/getTerritorialConstructionThings")
    public ResultMsg getTerritorialConstructionThings(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(greenTaxAnalysisService.getTerritorialConstructionThings(params));
    }



    /** 项目营收分析 */
    @RequestMapping("/getProjectRevenueList")
    public ResultMsg getProjectRevenueList(@RequestParam Map<String, Object> params) {
        PageInfo pageInfo = greenTaxAnalysisService.getProjectRevenueList(params);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    /** 项目进度分析 */
    @RequestMapping("/getProjectProgressList")
    public ResultMsg getProjectProgressList(@RequestParam Map<String, Object> params) {
        PageInfo pageInfo = greenTaxAnalysisService.getProjectProgressList(params);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }
}
