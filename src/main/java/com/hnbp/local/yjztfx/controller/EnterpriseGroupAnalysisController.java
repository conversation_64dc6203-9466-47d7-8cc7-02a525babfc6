package com.hnbp.local.yjztfx.controller;

import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.yjztfx.model.EnterpriseGroupAnalysis;
import com.hnbp.local.yjztfx.service.EnterpriseGroupAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * 企业集团专题分析
 */
@Controller
@RequestMapping("/yjzt/qyjt")
@ResponseBody
public class EnterpriseGroupAnalysisController {

    @Autowired
    private EnterpriseGroupAnalysisService enterpriseGroupAnalysisService;

    /**
     * 企业集团分总列表
     */
    @RequestMapping("/getEnterpriseGroupList")
    public ResultMsg getEnterpriseGroupList(@RequestParam Map<String, Object> params) {
        String frkrq = params.getOrDefault("frkrq", "").toString();

        if (params.get("fstartdate")==null && params.get("fenddate")==null ){
            // frkrq = "2025-01 ~ 2025-05"; 截取开始和结束日期
            String[] times = frkrq.split(" ~ ");
            if (times.length>1){
                params.put("fstartdate", times[0]);
                params.put("fenddate", times[1]);
            }
        }
        List<EnterpriseGroupAnalysis> enterpriseGroupList = enterpriseGroupAnalysisService.getEnterpriseGroupList(params);
        return ResultMsg.success(enterpriseGroupList);
    }



}
