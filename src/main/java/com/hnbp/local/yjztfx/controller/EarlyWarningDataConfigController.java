package com.hnbp.local.yjztfx.controller;

import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.yjztfx.service.EarlyWarningDataConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

/**
 * 预警数据配置
 */
@Controller
@RequestMapping("/yjzt")
@ResponseBody
public class EarlyWarningDataConfigController {

    @Autowired
    private EarlyWarningDataConfigService earlyWarningDataConfigService;

    /** 获取数据源的表名配置 */
    @RequestMapping("/getDataTableNameConfigList")
    public ResultMsg getDataTableNameConfigList(@RequestParam HashMap<String, Object> params) {
        return ResultMsg.success(earlyWarningDataConfigService.getDataTableNameConfigList(params));
    }

    /** 获取数据源表数据最新/最旧时间周期 */
    @RequestMapping("/findTableDataTime")
    public ResultMsg findTableDataTime(@RequestParam HashMap<String, Object> params) {
        try {
            Map<String, Object> tableDataTime = earlyWarningDataConfigService.findTableDataTime(params);
            return ResultMsg.success(tableDataTime);
        }catch (Exception e) {
            e.printStackTrace();
            return ResultMsg.error("500","只能获取贴源层表的时间!");
        }
    }

}
