package com.hnbp.local.yjztfx.controller;

import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.yjztfx.model.EnterpriseInfo;
import com.hnbp.local.yjztfx.service.EnterpriseManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;

/**
 * 企业信息管理控制器
 */
@Controller
@RequestMapping("/yjzt/qyjt")
@ResponseBody
public class EnterpriseManageController {

    @Autowired
    private EnterpriseManageService enterpriseManageService;

    /**
     * 保存企业信息（新增或更新）
     *
     * @return 操作结果
     */
    @RequestMapping("/saveEnterpriseInfo")
    public ResultMsg saveEnterpriseInfo(@RequestBody EnterpriseInfo enterpriseInfo) {
        int result = enterpriseManageService.saveEnterpriseInfo(enterpriseInfo);
        return ResultMsg.success(result);
    }

    /**
     * 查询所有企业信息
     *
     * @param params 请求参数
     * @return 企业信息列表
     */
    @RequestMapping("/selectAllEnterpriseInfo")
    public ResultMsg selectAllEnterpriseInfo(@RequestParam HashMap<String, Object> params) {
        PageInfo<EnterpriseInfo> pageInfo = enterpriseManageService.selectAllEnterpriseInfo(params);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    /**
     * 根据主键查询企业信息
     *
     * @param fid 主键ID
     * @return 企业信息对象
     */
    @RequestMapping("/selectEnterpriseInfoById")
    public ResultMsg selectEnterpriseInfoById(@RequestParam String fid) {
        EnterpriseInfo enterpriseInfo = enterpriseManageService.selectEnterpriseInfoById(fid);
        return ResultMsg.success(enterpriseInfo);
    }

    /**
     * 删除企业信息
     *
     * @param fid 主键ID
     * @return 删除结果
     */
    @RequestMapping("/deleteEnterpriseInfo")
    public ResultMsg deleteEnterpriseInfo(@RequestParam String fid) {
        int result = enterpriseManageService.deleteEnterpriseInfo(fid);
        return ResultMsg.success(result);
    }
}