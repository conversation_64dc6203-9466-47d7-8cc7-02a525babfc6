package com.hnbp.local.yjztfx.mapper;


import com.hnbp.local.util.echarts.Bar.BarSeriesInitData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 环保税专题分析
 */
@Mapper
public interface GreenTaxAnalysisMapper {


    /**
     * 施工许可证明细列表
     */
    List<Map<String, Object>> getConstructionPermitsDetailsList(Map<String, Object> params);


    /**
     * 施工许可证分总列表
     */
    List<Map<String, Object>> getConstructionPermitsTotalList(Map<String, Object> params);



    /** 分属地类型施工企业施工及环保税缴纳情况 */
    List<BarSeriesInitData> getTerritorialConstructionThings(Map<String, Object> params);


    /**
     * 项目营收分析
     */
    List<Map<String, Object>> getProjectRevenueList(Map<String, Object> params);


    /**
     * 项目进度分析
     */
    List<Map<String, Object>> getProjectProgressList(Map<String, Object> params);
}
