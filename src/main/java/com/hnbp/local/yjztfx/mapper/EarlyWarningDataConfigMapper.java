package com.hnbp.local.yjztfx.mapper;


import com.hnbp.local.yjztfx.model.DataTableNameConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 预警数据配置
 */
@Mapper
public interface EarlyWarningDataConfigMapper {

    /** 获取数据源的表名配置 */
    List<DataTableNameConfig> getDataTableNameConfigList(Map<String,Object> params);

    /** 表中数据 最新/最旧时间 */
    Map<String,Object> findTableDataTime(Map<String,Object> params);

}
