package com.hnbp.local.yjztfx.mapper;

import com.hnbp.local.util.echarts.Bar.BarSeriesInitData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface GasStationEarlyWarningMapper {
    
    /**
     * 加油站区域汇总列表
     */
    List<Map<String, Object>> getGasStationRegionRollupList(Map<String, Object> params);

    /**
     * 加油站明细列表
     */
    List<Map<String, Object>> getGasStationDetailsList(Map<String, Object> params);

    /**
     * 加油站明细平均线
     */
    List<Map<String, Object>> getGasStationDetailsAverages(Map<String, Object> params);
    
    /**
     * 加油站房土两税区域对比
     */
    List<BarSeriesInitData> getHousingAndLandTaxRegionContrast(Map<String, Object> params);
    
    /**
     * 增值税平均每吨油税额对比
     */
    List<BarSeriesInitData> getIncrementTaxAvgOilContrast(Map<String, Object> params);
    
    /**
     * 增值税可征收空间对比
     */
    List<BarSeriesInitData> getIncrementTaxExpropriableContrast(Map<String, Object> params);
} 