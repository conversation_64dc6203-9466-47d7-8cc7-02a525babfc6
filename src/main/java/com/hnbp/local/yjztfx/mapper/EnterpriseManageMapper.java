package com.hnbp.local.yjztfx.mapper;


import com.hnbp.local.yjztfx.model.EnterpriseInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 企业集团名管理
 */
@Mapper
public interface EnterpriseManageMapper {

    /**
     * 插入企业信息
     * @param enterpriseInfo 企业信息对象
     * @return 插入结果
     */
    int insertEnterpriseInfo(EnterpriseInfo enterpriseInfo);

    /**
     * 更新企业信息
     * @param enterpriseInfo 企业信息对象
     * @return 更新结果
     */
    int updateEnterpriseInfo(EnterpriseInfo enterpriseInfo);

    /**
     * 删除企业信息
     * @param fid 主键ID
     * @return 删除结果
     */
    int deleteEnterpriseInfo(String fid);

    /**
     * 查询所有企业信息
     * @return 企业信息列表
     */
    List<EnterpriseInfo> selectAllEnterpriseInfo(Map<String, Object> params);

    /**
     * 根据主键查询企业信息
     * @param fid 主键ID
     * @return 企业信息对象
     */
    EnterpriseInfo selectEnterpriseInfoById(String fid);

}