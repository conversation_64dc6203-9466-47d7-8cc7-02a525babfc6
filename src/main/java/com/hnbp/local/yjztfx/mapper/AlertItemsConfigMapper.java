package com.hnbp.local.yjztfx.mapper;


import com.hnbp.local.yjztfx.model.AlertItemsConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 施工许可证预警项配置
 */
@Mapper
public interface AlertItemsConfigMapper {

    /**
     * 插入企业信息
     * @param alertItemsConfig 企业信息对象
     * @return 插入结果
     */
    int insertAlertItemsConfig(AlertItemsConfig alertItemsConfig);


    /**
     * 删除企业信息
     * @return 删除结果
     */
    int deleteAlertItemsConfig(Map<String, Object> params);

    /**
     * 查询所有企业信息
     * @return 企业信息列表
     */
    List<AlertItemsConfig> selectAllAlertItemsConfig(Map<String, Object> params);


}