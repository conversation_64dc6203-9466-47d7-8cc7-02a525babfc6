package com.hnbp.local.yjztfx.model;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 集团企业信息表
 */
@Data
public class EnterpriseInfo {
    // 创建时间
    private Timestamp create_time;
    // 更新时间
    private Timestamp update_time;
    // 主键
    private String fid;
    // 区域ID
    private String region_id;
    // 企业名称
    private String fqymc;
    // 集团简称
    private String fjtjc;
    // 集团成员
    private String fjtcy;
    // 产业层级
    private String fcycj;
    // 出资比例
    private String fcgbl;
    // 注册资本（万元）
    private String fzczbwy;
    // 注册资本币值
    private String fzczbbz;
    // 成立日期
    private String fclrq;
    // 法定代表人
    private String fddbr;
    // 所属省份
    private String fsssf;
    // 所属城市
    private String fsss;
    // 所属区县
    private String fssqx;
    // 国标行业
    private String fgbxy;
    // 申万行业
    private String fswxy;
    // 上市发债信息
    private String fsfzxx;
    // 企业性质
    private String fqyxz;
    // 科技型称号
    private String fkjxch;
    // 纳税人识别号
    private String fnsrsbh;

}