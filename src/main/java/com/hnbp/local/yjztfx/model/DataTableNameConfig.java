package com.hnbp.local.yjztfx.model;

import lombok.Data;

import java.util.Map;

/**
 * 预警数据配置信息
 * <AUTHOR>
 */
@Data
public class DataTableNameConfig {
    String fid;
    // 表层级
    String levelName;
    // 表名
    String tableName;
    // 中文名
    String aliasName;
    // 报送日期
    String submitDate;
    // 调度周期
    String scheduledCycle;

    // kettle文件
    String ktrFile;

    // 表中数据 最新 最旧时间
    Map<String, Object> tableDate;
}
