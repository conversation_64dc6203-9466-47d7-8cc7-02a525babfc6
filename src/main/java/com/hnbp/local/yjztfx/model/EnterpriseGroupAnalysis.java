package com.hnbp.local.yjztfx.model;

import lombok.Data;

import java.util.List;

@Data
public class EnterpriseGroupAnalysis {
    // 企业名称
    private String fjtcy;
    // 集团名称
    private String fqymc;
    // 集团简称
    private String fjtjc;
    // 企业层级
    private String fcycj;

    private Double fhj_shj;
    private Double fhj_zzs;
    private Double fhj_qysds;
    private Double fhjqn_shj;
    private Double fhjqn_zzs;
    private Double fhjqn_qysds;

    private Double fzje_shj;
    private Double fzjl_shj;
    // 是否下级为集团标识
    private Integer flag_jt;
    private String fnf;
    // 下级企业数量
    private Integer fqysl;
    // 下级企业/集团
    private List<EnterpriseGroupAnalysis> children;
}