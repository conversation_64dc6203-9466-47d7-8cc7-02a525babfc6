package com.hnbp.local.qyxxcx.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.local.qyxxcx.mapper.EnterpriseInformationMapper;
import com.hnbp.local.qyxxcx.model.QyxxcxInfo;
import com.hnbp.local.qyxxcx.service.EnterpriseInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;

/**
 * <AUTHOR>
 * @Description: ServiceImpl
 * @date 2024-09-14
 */
@Service
public class EnterpriseInformationServiceImpl implements EnterpriseInformationService {

    @Autowired
    private EnterpriseInformationMapper enterpriseInformationMapper;

    /**
     * 企业综合信息查询
     * @param pagingParameter
     * @return
     */
    @Override
    public PageInfo findQyxxcx(QyxxcxInfo qyxxcxInfo) throws SQLException {

        PageHelper.startPage(qyxxcxInfo.getPage(), qyxxcxInfo.getLimit());
        return new PageInfo(enterpriseInformationMapper.findQyxxcx(qyxxcxInfo));
    }
}
