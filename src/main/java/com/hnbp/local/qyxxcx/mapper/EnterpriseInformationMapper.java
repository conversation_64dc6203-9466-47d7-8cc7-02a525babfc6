package com.hnbp.local.qyxxcx.mapper;

import com.hnbp.common.core.utils.zhzs.QueryCondition;
import com.hnbp.local.qyxxcx.model.QyxxcxInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: Mapper
 * @date 2024-09-14
 */
@Mapper
public interface EnterpriseInformationMapper {
    List<Map<String,Object>> findQyxxcx(QyxxcxInfo qyxxcxInfo);

}
