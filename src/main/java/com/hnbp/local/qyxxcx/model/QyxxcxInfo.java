package com.hnbp.local.qyxxcx.model;

import com.hnbp.common.core.utils.StringUtils;
import lombok.Data;

import java.util.List;

/**
 * 企业信息查询实体
 *
 * <AUTHOR>
 */
@Data
public class QyxxcxInfo {
    /*查询条件*/
    private String fssqy;
    private List<String> fssqys;
    private String fhydl;
    private List<String> fhydls;
    private String fnsrmc;
    private String fskssqq_s;
    private String fskssqq_e;
    private String frkrq_s;
    private String frkrq_e;

    private String fdjrqqz;
    private String fdjrqqzks;
    private String fdjrqqzjs;
    /*查询条件*/

    /*页面展示*/
    //税务登记信息展示
    private String fnsrsbh;
    private String fnsrzt;

    private List<String> fnsrztList;
    private String fdjzclx;
    private String fhy;
    private String fdjrq;
    private String fzgswjg;
    private String fjdxz;
    private String fzczb;
    private String fzcdz;
    private Double fsjje;
    private String dbrxm;//法定代表人姓名
    private String dbrlxdh;//法定代表人联系电话
    private String cwfzrxm;//财务负责人姓名
    private String cwfzrlxdh;//财务负责人联系电话
    private String kqyglbh;//跨区域涉税事项报验管理编号

    private String fjydz;

    private String fjyfw;

    private Integer page;
    private Integer limit;


    public void paramProcess() {
        this.fssqys = StringUtils.convertStringToList(this.fssqy, String::valueOf);
        this.fhydls = StringUtils.convertStringToList(this.fhydl, String::valueOf);
        this.fnsrztList = StringUtils.convertStringToList(this.fnsrzt, String::valueOf);
    }

}
