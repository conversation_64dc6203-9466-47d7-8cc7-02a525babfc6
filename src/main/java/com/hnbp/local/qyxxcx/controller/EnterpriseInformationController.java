package com.hnbp.local.qyxxcx.controller;

import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.qyxxcx.model.QyxxcxInfo;
import com.hnbp.local.qyxxcx.service.EnterpriseInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 企业综合信息Controller
 * @date 2024-10-22
 */
@Controller
public class EnterpriseInformationController {

    @Autowired
    private EnterpriseInformationService enterpriseInformationService;

    /**
     * 企业综合信息查询 (列表)
     */
    @RequestMapping("findQyxx_bzb")
    @ResponseBody
    public ResultMsg findQyxx_bzb(QyxxcxInfo qyxxcxInfo) throws SQLException {
        //参数处理
        qyxxcxInfo.paramProcess();

        PageInfo pageInfo = enterpriseInformationService.findQyxxcx(qyxxcxInfo);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }
}
