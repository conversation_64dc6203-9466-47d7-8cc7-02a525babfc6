package com.hnbp.local.sszbfx.controller;

import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarChart;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineChart;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChart;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import com.hnbp.common.core.utils.zhzs.util.ParameterUtil;
import com.hnbp.local.sszbfx.service.IndustryAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 分行业分析--Controller
 * @date 2024-09-14
 */
@Controller
//@RequestMapping("/page/jjzbfxxt/sszbfx/")
public class IndustryAnalysisController {

    @Autowired
    private IndustryAnalysisService IndustryAnalysisService;


    @RequestMapping("revenueByTaxTypeMxTable_bzb")
    @ResponseBody
    public ResultMsg revenueByTaxTypeMxTable_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {

        // 请求参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");
        PageInfo pageInfo = IndustryAnalysisService.revenueByTaxTypeMxTable((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }
}
