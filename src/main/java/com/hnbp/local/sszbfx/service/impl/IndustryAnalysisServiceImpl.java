package com.hnbp.local.sszbfx.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.utils.zhzs.QueryCondition;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarChart;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesData;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData;
import com.hnbp.common.core.utils.zhzs.echarts.ChartUtil;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineChart;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineSeriesInitData;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChart;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import com.hnbp.local.sszbfx.mapper.IndustryAnalysisMapper;
import com.hnbp.local.sszbfx.service.IndustryAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @Description: service实现层
 * @date 2024-09-14
 */
@Service
public class IndustryAnalysisServiceImpl implements IndustryAnalysisService {

    @Autowired
    private IndustryAnalysisMapper industryAnalysisMapper;


    @Override
    public PageInfo revenueByTaxTypeMxTable(PageParameter pagingParameter) {
        HashMap params = (HashMap) pagingParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));

        //纳税规模分析跳转详情页
        return new PageInfo(industryAnalysisMapper.revenueByTaxTypeMxTable(params));
    }
}
