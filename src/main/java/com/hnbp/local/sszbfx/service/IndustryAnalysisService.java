package com.hnbp.local.sszbfx.service;

import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.utils.zhzs.QueryCondition;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarChart;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineChart;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChart;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: service
 * @date 2024-09-14
 */

public interface IndustryAnalysisService {
    public PageInfo revenueByTaxTypeMxTable(PageParameter pagingParameter);
}
