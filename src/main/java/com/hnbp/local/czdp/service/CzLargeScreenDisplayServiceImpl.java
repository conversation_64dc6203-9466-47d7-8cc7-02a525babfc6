package com.hnbp.local.czdp.service;


import com.hnbp.local.czdp.mapper.CzLargeScreenDisplayMapper;
import com.hnbp.local.util.echarts.Bar.BarChart;
import com.hnbp.local.util.echarts.Bar.BarSeriesInitData;
import com.hnbp.local.util.echarts.pie.PieChart;
import com.hnbp.local.util.echarts.pie.PieChartsSeriesData;
import com.hnbp.local.util.util.ChartUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @version 1.0.00
 * @Annotation:
 * @Author: sheng_huang
 * @Data: 2024/5/14 15:14
 */

@Service
public class CzLargeScreenDisplayServiceImpl implements CzLargeScreenDisplayService {

    @Autowired
    private CzLargeScreenDisplayMapper czlargeScreenDisplayMapper;


    /**
     * 西双版纳近十年财政情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getNearlyADecadeData(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> nearlyADecadeData;
        String defaultLegend = "完成数";
        if (Boolean.TRUE.equals(parameterMap.get("fkmmc_in"))){
            // 财政总收入 单独调用
            defaultLegend = "财政总收入";
            nearlyADecadeData = czlargeScreenDisplayMapper.getCzTotalIncomeBarData(parameterMap);
        }else {
            nearlyADecadeData = czlargeScreenDisplayMapper.getNearlyADecadeData(parameterMap);
        }

        // 查到的完成条数
        int count = 0;
        for (BarSeriesInitData bar : nearlyADecadeData) {
            if (defaultLegend.equals(bar.getLegend())) {
                count++;
            }
        }

        // 数据不够时填充
        if (count < 5){
            // x轴 存在的年份
            List<Integer> xAxis = nearlyADecadeData.stream().map(bar ->Integer.valueOf(bar.getxAxis()))
                    .collect(Collectors.toList());
            Integer yearStart = (Integer) parameterMap.get("yearStart");
            Integer yearEnd = (Integer) parameterMap.get("yearEnd");
            for (int i = yearEnd; i > yearStart; i--) {
                if (!xAxis.contains(i)) {
                    BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
                    barSeriesInitData.setxAxis(String.valueOf(i));
                    barSeriesInitData.setyAxis(0.0);
                    barSeriesInitData.setLegend(defaultLegend);
                    barSeriesInitData.setType("bar");
                    // 列表位置
                    if (xAxis.size()==0||i<xAxis.get(0)){
                        nearlyADecadeData.add(0,barSeriesInitData);
                    }else {
                        nearlyADecadeData.add(barSeriesInitData);
                    }
                }
            }

        }

        // 遍历list算出年份增减幅比
        // 上一年完成数
        Double lastYearNum = 0.0;
        List<BarSeriesInitData> ratioData = new ArrayList<>();
        for (BarSeriesInitData bar : nearlyADecadeData) {
            if (defaultLegend.equals(bar.getLegend())) {
                Double ratio = 0.0;
                if (lastYearNum != 0.0) ratio = (bar.getyAxis() - lastYearNum) * 100.0 / lastYearNum;
                lastYearNum = bar.getyAxis();
                BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
                barSeriesInitData.setxAxis(bar.getxAxis());
                barSeriesInitData.setyAxis(Math.round(ratio * 100.0) / 100.0);
                barSeriesInitData.setLegend("增减幅");
                barSeriesInitData.setType("bar");
                ratioData.add(barSeriesInitData);
            }
        }
        nearlyADecadeData.addAll(ratioData);

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(nearlyADecadeData, extraParameter);
    }

    /**
     * 西双版纳当年月份财政情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public List<Map<String, Object>> getMonthData(Map<String, Object> parameterMap) {
        if (Boolean.TRUE.equals(parameterMap.get("fkmmc_in"))){
            // 财政总收入 单独调用
            return czlargeScreenDisplayMapper.getCzTotalIncomeData(parameterMap);
        }
        List<Map<String, Object>> monthData = czlargeScreenDisplayMapper.getMonthData(parameterMap);
        for (Map<String, Object> dataMap : monthData) {
            Map<String, Object> paramMap = new HashMap<>(parameterMap);
            // 取年份 转为int 减1
            int fyearInt = Integer.parseInt(paramMap.get("fyear").toString())-1;
            paramMap.put("fyear",fyearInt);
            List<Map<String, Object>> lastYearList = czlargeScreenDisplayMapper.getMonthData(paramMap);
            Map<String, Object> lastYearMap =new HashMap<>();
            // 去列表第一条数据
            if (lastYearList.size()>0) {
                lastYearMap = lastYearList.get(0);
            }
            try {
                Integer fljje = (Integer) dataMap.get("fwcs");
                Integer lyFljje = (Integer) lastYearMap.get("fwcs");
                // 增减幅
                dataMap.put("fzjf", String.format("%.2f", (fljje-lyFljje) * 100.0 / lyFljje));
            }catch (Exception e){
                dataMap.put("fzjf", "0");
            }
            // 计算在 一般公共预算收入合计 中的占比
            if ("税收收入".equals(parameterMap.get("fkmmc"))||"非税收入".equals(parameterMap.get("fkmmc"))){
                paramMap = new HashMap<>(parameterMap);
                paramMap.remove("fkmbm");
                paramMap.put("fkmmc","一般公共预算收入合计");
                List<Map<String, Object>> totalList = czlargeScreenDisplayMapper.getMonthData(paramMap);
                Map<String, Object> totalMap =new HashMap<>();
                // 去列表第一条数据
                if (lastYearList.size()>0) {
                    totalMap = totalList.get(0);
                }

                try {
                    Integer fljje = (Integer) dataMap.get("fwcs");
                    Integer totalFljje = (Integer) totalMap.get("fwcs");
                    // 占比
                    dataMap.put("fzb", String.format("%.2f", fljje * 100.0 / totalFljje));
                }catch (Exception e){
                    dataMap.put("fzb", "0");
                }
            }

        }
        return monthData;
    }

    /**
     * 全省一般公共预算收支情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getProvinceData(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> provinceData = czlargeScreenDisplayMapper.getProvinceData(parameterMap);
        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(provinceData, extraParameter);
    }

    /**
     * 地方全省排名情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public List<Map<String, Object>> getProvinceRank(Map<String, Object> parameterMap) {
        return czlargeScreenDisplayMapper.getProvinceRank(parameterMap);
    }

    /**
     * 财政支出分科目排名情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public List<Map<String, Object>> getCategorizeSpendRank(Map<String, Object> parameterMap) {
        List<Map<String, Object>> categorizeSpendRank = czlargeScreenDisplayMapper.getCategorizeSpendRank(parameterMap);
        // 总数
        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
        categorizeSpendRank.forEach(map -> total.updateAndGet(v -> v.add(new BigDecimal(map.get("fljje").toString()))));

        // 遍历数据 计算同比 占比
        categorizeSpendRank.stream().forEach(map -> {
            Object fljje = map.get("fljje");
            // 占比
            double fzb = new BigDecimal(fljje.toString()).doubleValue() / total.get().doubleValue();
            map.put("fzb", Math.round(fzb*10000.0)/100.0);
        });
        return categorizeSpendRank;
    }

    /**
     * 地区支出排名情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public List<Map<String, Object>> getRegionSpendRank(Map<String, Object> parameterMap) {
        List<Map<String, Object>> regionSpendRankList = czlargeScreenDisplayMapper.getRegionSpendRank(parameterMap);
        // 全州 查询map
        HashMap<String, Object> regionMap = new HashMap<>();
        regionMap.putAll(parameterMap);
        regionMap.put("fssdq", "全州");
        List<Map<String, Object>> qzSpendRankList = czlargeScreenDisplayMapper.getRegionSpendRank(regionMap);
        Map<String, Object> qzSpendRank= new HashMap<>();
        if (qzSpendRankList.size()>0) {
            qzSpendRank = qzSpendRankList.get(0);
        }
        // 遍历区域结果 计算区域支出与全州的占比
        for (Map<String, Object> map : regionSpendRankList) {
            Map<String, Object> paramMap = new HashMap<>(parameterMap);
            paramMap.put("fssdq",map.get("fssdq"));
            // 取年份 转为int 减1
            String[] fmonthArr = (String[])paramMap.get("fyear");
            int fmonthInt = Integer.parseInt(fmonthArr[0])-1;
            paramMap.put("fyear",fmonthInt);
            List<Map<String, Object>> lastMonthList = czlargeScreenDisplayMapper.getRegionSpendRank(paramMap);
            Map<String, Object> lastMonthMap =new HashMap<>();
            // 去列表第一条数据
            if (lastMonthList.size()>0) {
                lastMonthMap = lastMonthList.get(0);
            }
            Integer fljje = (Integer) map.get("fljje");
            Integer qzFljje = (Integer) qzSpendRank.get("fljje");
            Integer lmFljje = (Integer) lastMonthMap.get("fljje");
            if (fljje!=null && qzFljje!=null && lmFljje!=null) {
                // 区域支出占比 保留两位小数
                map.put("fzb", String.format("%.2f", fljje * 100.0 / qzFljje));
                // fljje 同比 lmFljje
                map.put("ftb", String.format("%.2f", (fljje-lmFljje) * 100.0 / lmFljje));
            }
        }

        return regionSpendRankList;
    }

    /**
     * 年度税收情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public PieChart getAnnualTaxesData(Map<String, Object> parameterMap) {
        List<PieChartsSeriesData> annualTaxesData = czlargeScreenDisplayMapper.getAnnualTaxesData(parameterMap);
        String titleTest="分区域";
        String[] groupArr = (String[])parameterMap.get("group");
        if (parameterMap.get("group")!=null && "type".equals(groupArr[0]))
            titleTest = "分科目";
        PieChart pieChart = new PieChart();
        pieChart.setTitle_text(titleTest);
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(annualTaxesData));
        pieChart.setSeries_data(annualTaxesData);
        return pieChart;
    }

    //-----------------------------调整为四个类别后接口-----------------------------------------

    /**
     * 趋势数据 按年度 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getTrendDataByYear(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> yearsData = czlargeScreenDisplayMapper.getTrendDataByYear(parameterMap);
        List<BarSeriesInitData> wcsList = yearsData.stream().filter(bar -> "完成数".equals(bar.getLegend())).collect(Collectors.toList());
        List<BarSeriesInitData> mbzList = yearsData.stream().filter(bar -> "目标值".equals(bar.getLegend())).collect(Collectors.toList());

        // 补全年份
        supplementYear(wcsList, parameterMap,"完成数");
        // wcsList.addAll(mbzList);

        // 遍历list算出年份增减幅比
        // 上一年完成数
        Double lastYearNum = 0.0;
        List<BarSeriesInitData> ratioData = new ArrayList<>();
        for (BarSeriesInitData bar : yearsData) {
            if ("完成数".equals(bar.getLegend())) {
                Double ratio = 0.0;
                if (lastYearNum != 0.0) ratio = (bar.getyAxis() - lastYearNum) * 100.0 / lastYearNum;
                lastYearNum = bar.getyAxis();
                BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
                barSeriesInitData.setxAxis(bar.getxAxis());
                barSeriesInitData.setyAxis(Math.round(ratio * 100.0) / 100.0);
                barSeriesInitData.setLegend("增减幅");
                barSeriesInitData.setType("bar");
                ratioData.add(barSeriesInitData);
            }
        }
        wcsList.addAll(ratioData);

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(wcsList, extraParameter);
    }
    /**
     * 趋势数据 按月份 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getTrendDataByMonth(Map<String, Object> parameterMap) {
        String legendStr = parameterMap.get("fyear").toString();
        List<BarSeriesInitData> monthData = czlargeScreenDisplayMapper.getTrendDataByMonth(parameterMap);
        // 补全月份
        supplementMonth(monthData, parameterMap,legendStr);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);

        // 遍历list算出年份增减幅比
        // 上一年完成数
        // Double lastMonthNum = 0.0;
        // List<BarSeriesInitData> ratioData = new ArrayList<>();
        // for (BarSeriesInitData bar : monthData) {
        //     if (legendStr.equals(bar.getLegend())) {
        //         Double ratio = 0.0;
        //         if (lastMonthNum != 0.0) ratio = (bar.getyAxis() - lastMonthNum) * 100.0 / lastMonthNum;
        //         lastMonthNum = bar.getyAxis();
        //         BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
        //         barSeriesInitData.setxAxis(bar.getxAxis());
        //         barSeriesInitData.setyAxis(Math.round(ratio * 100.0) / 100.0);
        //         barSeriesInitData.setLegend("增减幅");
        //         barSeriesInitData.setType("bar");
        //         ratioData.add(barSeriesInitData);
        //     }
        // }
        // 上一年数据
        paramMap.put("fyear", parameterMap.get("lastYear"));
        List<BarSeriesInitData> lastMonthData = czlargeScreenDisplayMapper.getTrendDataByMonth(paramMap);
        List<BarSeriesInitData> ratioData = getIncreasePercentBarSeriesData(monthData, lastMonthData);
        // 与上一年同比
        monthData.addAll(ratioData);
        // 上一年数据
        monthData.addAll(lastMonthData);
        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(monthData, extraParameter);
    }

    /**
     * 获取科目的 各种(占比,增幅,等..)比例
     *
     * @param parameterMap
     */
    @Override
    public Map<String, Object> getRatioOfEachSubjectData(Map<String, Object> parameterMap) {
        Map<String, Object> dataMap = czlargeScreenDisplayMapper.getRatioOfEachSubjectData(parameterMap);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        // 上一年数据
        paramMap.put("fyear", parameterMap.get("lastYear"));
        Map<String, Object> lastYearData = czlargeScreenDisplayMapper.getRatioOfEachSubjectData(paramMap);

        if (parameterMap.get("ftotalKmmc") != null) {
            // 占比总数
            paramMap = new HashMap<>(parameterMap);
            paramMap.put("fkmmc", parameterMap.get("ftotalKmmc"));
            paramMap.put("fkmmcList", parameterMap.get("ftotalKmmcList"));
            Map<String, Object> totalData = czlargeScreenDisplayMapper.getRatioOfEachSubjectData(paramMap);

            // 占比
            if (totalData != null){
                dataMap.put("fzb", getProportion(totalData.get("fwcs"),dataMap.get("fwcs")));
            }
        }
        // 增减幅度
        dataMap.put("fzjf", getIncreasePercent(lastYearData.get("fwcs"),dataMap.get("fwcs")));

        // 进度值
        dataMap.put("fjdz", getProportion(dataMap.get("fmbz"),dataMap.get("fwcs")));

        return dataMap;
    }

    /**
     * 财政月报 地区合计 数据
     *
     * @param parameterMap
     */
    @Override
    public List<Map<String, Object>> getCzybRegionData(Map<String, Object> parameterMap) {
        List<Map<String, Object>> dataList = czlargeScreenDisplayMapper.getCzybRegionData(parameterMap);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        List<Map<String, Object>> lastYearList = czlargeScreenDisplayMapper.getCzybRegionData(paramMap);
        // 总数
//        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
//        dataList.forEach(map -> total.updateAndGet(v -> v.add(new BigDecimal(map.get("fljje").toString()))));
        BigDecimal total = dataList.stream()
                .filter(Objects::nonNull).filter(item -> item.get("fssdq") != "全市").map(item -> item.get("fljje"))
                .filter(Objects::nonNull)
                .map(Object::toString).map(BigDecimal::new).reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);

        // 创建一个HashMap用于存储去年的数据，键为"fssdq"的值，值为"fljje"的值
        Map<String, Object> lastYearMap = new HashMap<>();
        // 对去年的数据列表进行流式处理，将每个map中的"fssdq"对应的值作为键，"fljje"对应的值作为值，添加到lastYearMap中
        lastYearList.stream().forEach(map -> lastYearMap.put(map.get("fssdq").toString(),map.get("fljje")));
        dataList.stream().forEach(map -> {
            Object thisYear =  map.get("fljje");
            Object lastYear =  lastYearMap.get(map.get("fssdq").toString());
            // 增减额
            map.put("fzje", getIncreaseAmount(lastYear, thisYear));
            // 合计增幅
            map.put("fzfb", getIncreasePercent(lastYear, thisYear));
            // 占比
            map.put("fzb", getProportion(total, thisYear));
        });
        return dataList;
    }

    /**
     * 财政月报 分科目 数据
     *
     * @param parameterMap
     */
    @Override
    public List<Map<String, Object>> getCzybSubjectData(Map<String, Object> parameterMap) {
        List<Map<String, Object>> dataList = czlargeScreenDisplayMapper.getCzybSubjectData(parameterMap);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        List<Map<String, Object>> lastYearList = czlargeScreenDisplayMapper.getCzybSubjectData(paramMap);
        // 总数
        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
        dataList.forEach(map -> total.updateAndGet(v -> v.add(new BigDecimal(map.get("fljje").toString()))));

        // 创建一个HashMap用于存储去年的数据，键为"fkmmc"的值，值为"fljje"的值
        Map<String, Object> lastYearMap = new HashMap<>();
        // 对去年的数据列表进行流式处理，将每个map中的"fkmmc"对应的值作为键，"fljje"对应的值作为值，添加到lastYearMap中
        lastYearList.stream().forEach(map -> lastYearMap.put(map.get("fkmmc").toString(),map.get("fljje")));
        dataList.stream().forEach(map -> {
            Object thisYear =  map.get("fljje");
            Object lastYear =  lastYearMap.get(map.get("fkmmc").toString());
            // 合计增幅
            map.put("fzfb", getIncreasePercent(lastYear, thisYear));

            // 占比
            map.put("fzb", getProportion(total.get(), thisYear));
        });
        return dataList;
    }

    /**
     * 趋势数据 全省预算 按年度 数据情况
     *
     * @param parameterMap
     */
    @Override
    public BarChart getQsysTrendDataByYear(Map<String, Object> parameterMap) {
        String LegendStr = "全省一般公共预算"+parameterMap.get("fxmlx");
        List<BarSeriesInitData> yearsData = czlargeScreenDisplayMapper.getQsysTrendDataByYear(parameterMap);
        List<BarSeriesInitData> wcsList = yearsData.stream().filter(bar -> LegendStr.equals(bar.getLegend())).collect(Collectors.toList());

        // 补全年份
        supplementYear(wcsList, parameterMap,LegendStr);

        // 遍历list算出年份增减幅比
        // 上一年完成数
        Double lastYearNum = 0.0;
        List<BarSeriesInitData> ratioData = new ArrayList<>();
        for (BarSeriesInitData bar : yearsData) {
            if ("完成数".equals(bar.getLegend())) {
                Double ratio = 0.0;
                if (lastYearNum != 0.0) ratio = (bar.getyAxis() - lastYearNum) * 100.0 / lastYearNum;
                lastYearNum = bar.getyAxis();
                BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
                barSeriesInitData.setxAxis(bar.getxAxis());
                barSeriesInitData.setyAxis(Math.round(ratio * 100.0) / 100.0);
                barSeriesInitData.setLegend("增减幅");
                barSeriesInitData.setType("bar");
                ratioData.add(barSeriesInitData);
            }
        }
        wcsList.addAll(ratioData);

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(wcsList, extraParameter);
    }

    /**
     * 趋势数据 全省预算 按月份 数据情况
     *
     * @param parameterMap
     */
    @Override
    public BarChart getQsysTrendDataByMonth(Map<String, Object> parameterMap) {
        String legendStr = parameterMap.get("fyear").toString();
        List<BarSeriesInitData> monthData = czlargeScreenDisplayMapper.getQsysTrendDataByMonth(parameterMap);
        // 补全月份
        supplementMonth(monthData, parameterMap,legendStr);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);

        // 上一年数据
        paramMap.put("fyear", parameterMap.get("lastYear"));
        List<BarSeriesInitData> lastMonthData = czlargeScreenDisplayMapper.getQsysTrendDataByMonth(paramMap);
        // 与上一年同比
        List<BarSeriesInitData> ratioData = getIncreasePercentBarSeriesData(monthData, lastMonthData);
        monthData.addAll(ratioData);
        // 上一年数据
        monthData.addAll(lastMonthData);
        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(monthData, extraParameter);
    }


    /**
     * 趋势年份不足时补充年份
     */
    public List<BarSeriesInitData> supplementYear(List<BarSeriesInitData> dataList, Map<String, Object> parameterMap,String Legend) {
        Integer limit = Integer.valueOf(parameterMap.get("limit").toString());
        if (dataList.size()<limit){
            // x轴 存在的年份
            List<Integer> xAxis = dataList.stream().map(bar ->Integer.valueOf(bar.getxAxis()))
                    .collect(Collectors.toList());
            Integer year = Integer.valueOf(parameterMap.get("fyear").toString());
            for (int i = 0; i < limit; i++){
                if (!xAxis.contains(year-i)){
                    BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
                    barSeriesInitData.setxAxis(String.valueOf((year-i)));
                    barSeriesInitData.setyAxis(0.0);
                    barSeriesInitData.setLegend(Legend);
                    barSeriesInitData.setType("bar");

                    dataList.add(barSeriesInitData);
                }
            }
        }
        dataList.sort(Comparator.comparing(
                bar -> Integer.valueOf(bar.getxAxis())
        ));
        return dataList;
    }

    /**
     * 趋势月份不足时补充月份
     */
    public List<BarSeriesInitData> supplementMonth(List<BarSeriesInitData> dataList, Map<String, Object> parameterMap,String Legend) {
        Integer limit = 12;
        if (parameterMap.get("fmonth")!=null){
            limit = Integer.valueOf(parameterMap.get("fmonth").toString());
        }
        if (dataList.size()<limit){
            // x轴 存在的月份
            List<Integer> xAxis = dataList.stream().map(bar ->Integer.valueOf(bar.getxAxis()))
                    .collect(Collectors.toList());
            for (int i = 0; i < limit; i++){
                if (!xAxis.contains(i+1)){
                    BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
                    barSeriesInitData.setxAxis(String.valueOf((i+1)));
                    barSeriesInitData.setyAxis(0.0);
                    barSeriesInitData.setLegend(Legend);
                    barSeriesInitData.setType("bar");

                    dataList.add(barSeriesInitData);
                }
            }
        }
        // 按月排序
        dataList.sort(Comparator.comparing(
                bar -> Integer.valueOf(bar.getxAxis())
        ));
        return dataList;
    }


    /** 柱状图 同比 */
    public List<BarSeriesInitData> getIncreasePercentBarSeriesData(List<BarSeriesInitData> dataList,List<BarSeriesInitData> lastYearList){
        List<BarSeriesInitData> ratioData = new ArrayList<>();
        // 创建一个HashMap用于存储去年的数据，键为"fkmmc"的值，值为"fljje"的值
        Map<String, Object> lastYearMap = new HashMap<>();
        // 对去年的数据列表进行流式处理，将每个map中的"fkmmc"对应的值作为键，"fljje"对应的值作为值，添加到lastYearMap中
        lastYearList.stream().forEach(bar -> lastYearMap.put(bar.getxAxis(),bar.getyAxis()));
        dataList.stream().forEach(bar -> {
            Object thisYear =  bar.getyAxis();
            Object lastYear =  lastYearMap.get(bar.getxAxis());
            // 合计增幅
            double increasePercent = getIncreasePercent(lastYear, thisYear);
            BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
            barSeriesInitData.setxAxis(bar.getxAxis());
            barSeriesInitData.setyAxis(increasePercent);
            barSeriesInitData.setLegend("增减幅");
            barSeriesInitData.setType("bar");
            ratioData.add(barSeriesInitData);
        });
        return ratioData;
    }



    /** 增减额 */
    public double getIncreaseAmount(BigDecimal lastYear, BigDecimal thisYear) {
        double amount = 0;
        if (lastYear!=null && thisYear!=null) {
            amount = thisYear.doubleValue() - lastYear.doubleValue();
        }
        return amount;
    }
    public double getIncreaseAmount(Object lastYear, Object thisYear) {
        if (lastYear == null || thisYear == null) return 0;
        return this.getIncreaseAmount(new BigDecimal(lastYear.toString()), new BigDecimal(thisYear.toString()));
    }
    /** 增减比例 */
    public double getIncreasePercent(BigDecimal lastYear, BigDecimal thisYear) {
        double percent = 0;
        if (lastYear!=null && thisYear!=null && lastYear.doubleValue() != 0) {
            percent = (thisYear.doubleValue() - lastYear.doubleValue()) / lastYear.doubleValue();
        }
        return Math.round(percent * 10000.0) / 100.0;
    }
    public double getIncreasePercent(Object lastYear, Object thisYear) {
        if (lastYear == null || thisYear == null) return 0;
        return this.getIncreasePercent(new BigDecimal(lastYear.toString()), new BigDecimal(thisYear.toString()));
    }
    /** 占比 */
    public double getProportion(BigDecimal total, BigDecimal num) {
        double percent = 0;
        if (total !=null && num!=null && total.doubleValue() != 0) {
            percent = num.doubleValue() / total.doubleValue();
        }
        return Math.round(percent * 10000.0) / 100.0;
    }
    public double getProportion(Object total, Object num) {
        if (total == null || num == null) return 0;
        return this.getProportion(new BigDecimal(total.toString()), new BigDecimal(num.toString()));
    }
}
