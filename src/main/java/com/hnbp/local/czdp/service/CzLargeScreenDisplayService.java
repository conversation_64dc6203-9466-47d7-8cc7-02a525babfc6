package com.hnbp.local.czdp.service;



import com.hnbp.local.util.echarts.Bar.BarChart;
import com.hnbp.local.util.echarts.pie.PieChart;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0.00
 * @Annotation:
 * @Author: sheng_huang
 * @Data: 2024/5/14 15:02
 */

public interface CzLargeScreenDisplayService {


    /**
     * 西双版纳近十年财政情况
     * @param parameterMap
     * @return
     */
    BarChart getNearlyADecadeData(Map<String, Object> parameterMap);

    /**
     * 西双版纳当年月份财政情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getMonthData(Map<String, Object> parameterMap);


    /**
     * 全省一般公共预算收支情况
     * @param parameterMap
     * @return
     */
    BarChart getProvinceData(Map<String, Object> parameterMap);


    /**
     * 地方全省排名情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getProvinceRank(Map<String, Object> parameterMap);



    /**
     * 财政支出分科目排名情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getCategorizeSpendRank(Map<String, Object> parameterMap);

    /**
     * 地区支出排名情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getRegionSpendRank(Map<String, Object> parameterMap);


    /**
     * 年度税收情况
     * @param parameterMap
     * @return
     */
    PieChart getAnnualTaxesData(Map<String, Object> parameterMap);


    //-----------------------------调整为四个类别后接口-----------------------------------------

    /**
     * 趋势数据 按年度 数据情况
     */
    BarChart getTrendDataByYear(Map<String, Object> parameterMap);

    /**
     * 趋势数据 按月份 数据情况
     */
    BarChart getTrendDataByMonth(Map<String, Object> parameterMap);

    /** 获取科目的 各种(占比,增幅,等..)比例 */
    Map<String, Object> getRatioOfEachSubjectData(Map<String, Object> parameterMap);

    /** 财政月报 分地区合计 数据 */
    List<Map<String, Object>> getCzybRegionData(Map<String, Object> parameterMap);

    /** 财政月报 分科目 数据 */
    List<Map<String, Object>> getCzybSubjectData(Map<String, Object> parameterMap);


    /**
     * 趋势数据 全省预算 按年度 数据情况
     */
    BarChart getQsysTrendDataByYear(Map<String, Object> parameterMap);

    /**
     * 趋势数据 全省预算 按月份 数据情况
     */
    BarChart getQsysTrendDataByMonth(Map<String, Object> parameterMap);
}
