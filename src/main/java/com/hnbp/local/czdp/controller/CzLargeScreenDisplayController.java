package com.hnbp.local.czdp.controller;

import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.czdp.service.CzLargeScreenDisplayService;
import com.hnbp.local.util.echarts.Bar.BarChart;
import com.hnbp.local.util.echarts.pie.PieChart;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0.00
 * @Annotation: 大屏治税
 * @Author: sheng_huang
 * @Data: 2024/5/14 14:26
 */

@Controller
@RequestMapping("czdpzs")
@ResponseBody
public class CzLargeScreenDisplayController {

    @Autowired
    private CzLargeScreenDisplayService czlargeScreenDisplayService;

    /** 西双版纳近十年财政情况 */
    @RequestMapping("getNearlyADecadeData")
    public ResultMsg getNearlyADecadeData(@RequestParam Map<String, Object> parameterMap) throws IOException {
        Map<String, Object> responseMap = new HashMap<>();

        // 根据参数映射表获取近十年的数据。
        BarChart responseBarChart = czlargeScreenDisplayService.getNearlyADecadeData(buildParam(parameterMap));
        responseMap.put("barChart", responseBarChart);

        // 获取月份数据，并将其放入响应映射中。
        List<Map<String, Object>> monthData = czlargeScreenDisplayService.getMonthData(buildParam(parameterMap));
        responseMap.put("otherData", monthData);

        return ResultMsg.success(responseMap);
    }

    /** 全省一般公共预算财政情况 */
    @RequestMapping("getProvinceData")
    public ResultMsg getProvinceData(@RequestParam Map<String, Object> parameterMap) throws IOException {
        Map<String, Object> responseMap = new HashMap<>();

        BarChart responseBarChart = czlargeScreenDisplayService.getProvinceData(parameterMap);
        responseMap.put("barChart", responseBarChart);

        List<Map<String, Object>> rankData = czlargeScreenDisplayService.getProvinceRank(parameterMap);
        responseMap.put("otherData", rankData);

        return ResultMsg.success(responseMap);
    }

    /** 财政支出分科目排名情况 */
    @RequestMapping("getCategorizeSpendRank")
    public ResultMsg getCategorizeSpendRank(@RequestParam Map<String, Object> parameterMap) throws IOException {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.putAll(parameterMap);
        // 默认值为西双版纳(全州)
        if (!paramMap.containsKey("fssdq") && paramMap.get("fssdq") == null) {
            paramMap.put("fssdq", "全州");
        }
        List<Map<String, Object>> categorizeSpendRank = czlargeScreenDisplayService.getCategorizeSpendRank(paramMap);

        return ResultMsg.success(categorizeSpendRank);
    }

    /** 地区支出排名情况 */
    @RequestMapping("getRegionSpendRank")
    public ResultMsg getRegionSpendRank(@RequestParam Map<String, Object> parameterMap) throws IOException {
        List<Map<String, Object>> regionSpendRank = czlargeScreenDisplayService.getRegionSpendRank(parameterMap);

        return ResultMsg.success(regionSpendRank);
    }

    /** 年度税收情况 */
    @RequestMapping("getAnnualTaxesData")
    public ResultMsg getAnnualTaxesData(@RequestParam Map<String, Object> parameterMap) throws IOException {
        PieChart pieChart = czlargeScreenDisplayService.getAnnualTaxesData(parameterMap);

        return ResultMsg.success(pieChart);
    }

    //-----------------------------调整为四个类别后接口-----------------------------------------

    /** 趋势数据 按年度 数据情况 */
    @RequestMapping("getTrendDataByYear")
    public ResultMsg getTrendDataByYear(@RequestParam Map<String, Object> parameterMap) throws IOException {
        BarChart barChart = czlargeScreenDisplayService.getTrendDataByYear(buildParam(parameterMap));

        return ResultMsg.success(barChart);
    }

    /** 趋势数据 按月份 数据情况 */
    @RequestMapping("getTrendDataByMonth")
    public ResultMsg getTrendDataByMonth(@RequestParam Map<String, Object> parameterMap) throws IOException {
        BarChart barChart = czlargeScreenDisplayService.getTrendDataByMonth(buildParam(parameterMap));

        return ResultMsg.success(barChart);
    }

    /** 获取科目的 各种(占比,增幅,等..)比例 */
    @RequestMapping("getRatioOfEachSubjectData")
    public ResultMsg getRatioOfEachSubjectData(@RequestParam Map<String, Object> parameterMap) throws IOException {
        Map<String, Object> ratioOfEachSubjectData = czlargeScreenDisplayService.getRatioOfEachSubjectData(buildParam(parameterMap));

        return ResultMsg.success(ratioOfEachSubjectData);
    }

    /** 财政月报 分地区合计 数据 */
    @RequestMapping("getCzybRegionData")
    public ResultMsg getCzybRegionData(@RequestParam Map<String, Object> parameterMap) throws IOException {
        List<Map<String, Object>> dataList = czlargeScreenDisplayService.getCzybRegionData(buildParam(parameterMap));

        return ResultMsg.success(dataList);
    }

    /** 财政月报 分科目 数据 */
    @RequestMapping("getCzybSubjectData")
    public ResultMsg getCzybSubjectData(@RequestParam Map<String, Object> parameterMap) throws IOException {
        Map<String, Object> paramMap = buildParam(parameterMap);
        Object fssdq = paramMap.get("fssdq");
        if (fssdq == null) {
            paramMap.put("fssdq", "全市");
        }
        List<Map<String, Object>> dataList = czlargeScreenDisplayService.getCzybSubjectData(paramMap);

        return ResultMsg.success(dataList);
    }

    /** 趋势数据 全省预算 按年度 数据情况 */
    @RequestMapping("getQsysTrendDataByYear")
    public ResultMsg getQsysTrendDataByYear(@RequestParam Map<String, Object> parameterMap) throws IOException {
        BarChart barChart = czlargeScreenDisplayService.getQsysTrendDataByYear(buildParam(parameterMap));

        return ResultMsg.success(barChart);
    }

    /** 趋势数据 全省预算 按月度 数据情况 */
    @RequestMapping("getQsysTrendDataByMonth")
    public ResultMsg getQsysTrendDataByMonth(@RequestParam Map<String, Object> parameterMap) throws IOException {
        BarChart barChart = czlargeScreenDisplayService.getQsysTrendDataByMonth(buildParam(parameterMap));

        return ResultMsg.success(barChart);
    }

    /**
     * 处理参数
     * @param parameterMap
     * @return
     */
    private Map<String, Object> buildParam(Map<String, Object> parameterMap) {
        Map<String, Object> param = new HashMap<>();
        param.putAll(parameterMap);
        Object fyear = param.get("fyear");
        if (fyear != null) {
            String fyearArr = (String) fyear;
            // 转成数字并减5
            param.put("yearStart", Integer.parseInt(fyearArr) - 5);
            param.put("yearEnd", Integer.parseInt(fyearArr)); // 前一年
            // 去年
            param.put("lastYear", Integer.parseInt(fyearArr) - 1);
            param.put("fyear", Integer.parseInt(fyearArr));
        }
        Object fmonth = param.get("fmonth");
        if (fmonth != null) {
            String fmonthArr = (String) fmonth;
            param.put("fmonth", Integer.parseInt(fmonthArr));
        }
        Object fkmmc = param.get("fkmmc");
        if (fkmmc != null) {
            String fkmmcArr = (String) fkmmc;
            param.put("fkmmc", fkmmcArr);
            if ("财政总收入".equals(fkmmcArr)) {
                param.remove("fkmmc");
                param.put("fkmmc_in", true);
            }
            param.put("fkmmcList", Arrays.asList(fkmmcArr.split(",")));
        }
        // 占比总数的科目名称 (在哪个科目占比多少)
        Object ftotalKmmc = param.get("ftotalKmmc");
        if (ftotalKmmc != null) {
            String ftotalKmmcArr = (String) ftotalKmmc;
            param.put("ftotalKmmc", ftotalKmmcArr);
            param.put("ftotalKmmcList", Arrays.asList(ftotalKmmcArr.split(",")));
        }
        Object fxmlx = param.get("fxmlx");
        if (fxmlx == null) {
            param.put("fxmlx", "支出");
        } else {
            String fxmlxArr = (String) fxmlx;
            param.put("fxmlx", fxmlxArr);
        }
        Object fkmbm = param.get("fkmbm");
        if (fkmbm != null) {
            String strArr = (String) fkmbm;
            param.put("fkmbm", strArr);
        }
        Object fssdq = param.get("fssdq");
        if (fssdq == null) {
            param.put("fssdq", "全市");
        }
        Object limit = param.get("limit");
        if (limit != null) {
            String limitArr = (String) limit;
            param.put("limit", Integer.parseInt(limitArr));
        } else {
            param.put("limit", 5);
        }
        return param;
    }
}