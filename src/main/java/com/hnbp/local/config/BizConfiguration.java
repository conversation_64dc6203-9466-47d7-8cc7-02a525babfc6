package com.hnbp.local.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties("biz-config")
public class BizConfiguration {
    private String taxDataType;

    public String getTaxDataType() {
        return taxDataType;
    }

    public void setTaxDataType(String taxDataType) {
        this.taxDataType = taxDataType;
    }
}