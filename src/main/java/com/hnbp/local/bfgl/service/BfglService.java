package com.hnbp.local.bfgl.service;

import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.utils.zhzs.MapUtils;
import com.hnbp.local.bfgl.mapper.BfglMapper;
import com.hnbp.local.bfgl.model.BFglDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: 你的名字
 * @Date: 2023/05/24/13:20
 * @Description:
 */
@Service
public class BfglService {
    @Autowired
    BfglMapper bfglMapper;


    public List<Map<String, Object>> getyqkh(BFglDTO param) {
        return bfglMapper.getyqkh(param);
    }

    public List<Map<String, String>> getAllYq() {
        return bfglMapper.getAllYq();
    }

    public List<Map<String,Object>> getyqkh_mx(BFglDTO dto){
        List<Map<String,Object>> map = bfglMapper.getyqkh_mx(dto);
        return map;
    }
}
