package com.hnbp.local.jjzbfx.mapper;

import com.hnbp.local.jjzbfx.model.GdpParameterQuery;
import com.hnbp.local.util.echarts.Bar.BarSeriesInitData;
import org.apache.ibatis.annotations.Mapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description:
 * @date 2024-09-12
 */
@Mapper
public interface GdpTrendsMapper {

    List<BarSeriesInitData> getGdplnjjzbdbtlx(GdpParameterQuery gdpParameterQuery);

    List<HashMap<String, Object>> getGdpcyjgtlx(GdpParameterQuery gdpParameterQuery);

    List<BarSeriesInitData> getGdpdqjjzbdbtlx(GdpParameterQuery gdpParameterQuery);

    List<HashMap<String, Object>> getGdphyjjsjtlx(GdpParameterQuery gdpParameterQuery);

    List<BarSeriesInitData> getGdpdqhyjjzbdbtlx(GdpParameterQuery gdpParameterQuery);

    List<BarSeriesInitData> getGdplnhyjjzbdbtlx(GdpParameterQuery gdpParameterQuery);


}
