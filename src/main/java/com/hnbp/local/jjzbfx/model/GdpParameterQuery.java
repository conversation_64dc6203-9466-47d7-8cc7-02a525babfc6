package com.hnbp.local.jjzbfx.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @version 1.0.00
 * @Annotation: 同类型经济指标分析-查询参数
 * @Author: sheng_huang
 * @Data: 2025/3/24 10:18
 */

@Data
public class GdpParameterQuery {

    @Schema(description = "时间")
    private String ftime;

    @Schema(description = "起始年份")
    private String start_year;

    @Schema(description = "终止年份")
    private String fyear;

    @Schema(description = "地区")
    private String fregion;

    @Schema(description = "地区集合")
    private List<String> fregionList;

    @Schema(description = "区县")
    private String fqx;

    @Schema(description = "经济指标")
    private List<String> fjjzb;
}
