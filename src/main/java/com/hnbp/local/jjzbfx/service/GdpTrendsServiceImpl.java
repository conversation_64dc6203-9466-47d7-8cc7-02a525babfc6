package com.hnbp.local.jjzbfx.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.local.jjzbfx.mapper.GdpTrendsMapper;
import com.hnbp.local.jjzbfx.model.GdpParameterQuery;
import com.hnbp.local.util.echarts.Bar.BarChart;
import com.hnbp.local.util.echarts.Bar.BarSeriesData;
import com.hnbp.local.util.echarts.Bar.BarSeriesInitData;
import com.hnbp.local.util.util.ChartUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @DATE 2024/1/25
 */
@Service
public class GdpTrendsServiceImpl {
    @Autowired
    private GdpTrendsMapper gdpTrendsMapper;

    public BarChart getGdplnjjzbdbtlx(GdpParameterQuery gdpParameterQuery) {
        this.buildGdpParameterQuery(gdpParameterQuery);

        String ftime = gdpParameterQuery.getFtime();
        if (!StringUtils.isEmpty(ftime)) {
            String[] split = ftime.split(" ");
            if (split.length == 3) {
                gdpParameterQuery.setStart_year(split[0]);
                gdpParameterQuery.setFyear(split[2]);
            }
        }
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = gdpTrendsMapper.getGdplnjjzbdbtlx(gdpParameterQuery);
        //柱状图数据封装
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        extraParameter.put("markPoint", "max");
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text("GDP产业趋势");
        List series = barChart.getSeries();
        List series_new = new ArrayList();
        if (series == null) {
            return null;
        }
        for (Object o : series) {
            BarSeriesData barSeriesData = (BarSeriesData) o;
            String fxmmc = barSeriesData.getName();
            if (fxmmc.contains("占比")) {
                barSeriesData.setType("line");
            }

            checkYear(Integer.parseInt(gdpParameterQuery.getStart_year()), Integer.parseInt(gdpParameterQuery.getFyear()), barSeriesData, barChart.getxAxis_data());

            series_new.add(barSeriesData);
        }


        barChart.setxAxis_data(getYears(Integer.parseInt(gdpParameterQuery.getStart_year()), Integer.parseInt(gdpParameterQuery.getFyear())));

        barChart.setSeries(series_new);
        return barChart;
    }


    public List<HashMap<String, Object>> getGdpcyjgtlx(GdpParameterQuery gdpParameterQuery) {
        this.buildGdpParameterQuery(gdpParameterQuery);
        return gdpTrendsMapper.getGdpcyjgtlx(gdpParameterQuery);
    }

    public BarChart getGdpdqjjzbdbtlx(GdpParameterQuery gdpParameterQuery) {
        this.buildGdpParameterQuery(gdpParameterQuery);

        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = gdpTrendsMapper.getGdpdqjjzbdbtlx(gdpParameterQuery);
        //柱状图数据封装
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        extraParameter.put("markPoint", "max");
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text("GDP产业趋势");
        List series = barChart.getSeries();
        List series_new = new ArrayList();
        if (series == null) {
            return null;
        }
        for (Object o : series) {
            BarSeriesData barSeriesData = (BarSeriesData) o;
            String fxmmc = barSeriesData.getName();
            if (fxmmc.contains("占比")) {
                barSeriesData.setType("line");
            }
            series_new.add(barSeriesData);
        }
        barChart.setSeries(series_new);
        return barChart;
    }

    public List<HashMap<String, Object>> getGdphyjjsjtlx(GdpParameterQuery gdpParameterQuery) {
        this.buildGdpParameterQuery(gdpParameterQuery);
        return gdpTrendsMapper.getGdphyjjsjtlx(gdpParameterQuery);
    }

    public BarChart getGdpdqhyjjzbdbtlx(GdpParameterQuery gdpParameterQuery) {
        this.buildGdpParameterQuery(gdpParameterQuery);

        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = gdpTrendsMapper.getGdpdqhyjjzbdbtlx(gdpParameterQuery);
        //柱状图数据封装
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        extraParameter.put("markPoint", "max");
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text("GDP产业趋势");
        List series = barChart.getSeries();
        List series_new = new ArrayList();
        if (series == null) {
            return null;
        }
        for (Object o : series) {
            BarSeriesData barSeriesData = (BarSeriesData) o;
            String fxmmc = barSeriesData.getName();
            if (fxmmc.contains("占比")) {
                barSeriesData.setType("line");
            }
            series_new.add(barSeriesData);
        }
        barChart.setSeries(series_new);
        return barChart;
    }

    public BarChart getGdplnhyjjzbdbtlx(GdpParameterQuery gdpParameterQuery) {
        this.buildGdpParameterQuery(gdpParameterQuery);
        String ftime = gdpParameterQuery.getFtime();
        if (!StringUtils.isEmpty(ftime)) {
            String[] split = ftime.split(" ");
            if (split.length == 3) {
                gdpParameterQuery.setStart_year(split[0]);
                gdpParameterQuery.setFyear(split[2]);
            }
        }
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = gdpTrendsMapper.getGdplnhyjjzbdbtlx(gdpParameterQuery);
        //柱状图数据封装
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        extraParameter.put("markPoint", "max");
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text("GDP产业趋势");
        List series = barChart.getSeries();
        List series_new = new ArrayList();
        if (series == null) {
            return null;
        }
        for (Object o : series) {
            BarSeriesData barSeriesData = (BarSeriesData) o;
            String fxmmc = barSeriesData.getName();
            if (fxmmc.contains("占比")) {
                barSeriesData.setType("line");
            }

            checkYear(Integer.parseInt(gdpParameterQuery.getStart_year()), Integer.parseInt(gdpParameterQuery.getFyear()), barSeriesData, barChart.getxAxis_data());

            series_new.add(barSeriesData);
        }

        barChart.setxAxis_data(getYears(Integer.parseInt(gdpParameterQuery.getStart_year()), Integer.parseInt(gdpParameterQuery.getFyear())));

        barChart.setSeries(series_new);
        return barChart;
    }

    private List<String> getYears(int start, int end) {
        List<String> years = new ArrayList<>();
        for (int i = end; i >= start; i--) {
            years.add(String.valueOf(i));
        }
        return years;
    }

    private void checkYear(int start, int end, BarSeriesData bardata, List<String> xAxis) {
        List newData = new ArrayList();
        List data = bardata.getData();
        for (int i = end; i >= start; i--) {
            int i1 = xAxis.indexOf(String.valueOf(i));
            if (i1 == -1) {
                newData.add(0);
            } else {
                newData.add(data.get(i1));
            }
        }
        bardata.setData(newData);

    }

    /**
     * 构建参数
     * @param gdpParameterQuery
     * @return
     */
    private GdpParameterQuery buildGdpParameterQuery(GdpParameterQuery gdpParameterQuery){
        String fregion = gdpParameterQuery.getFregion();
        if (StringUtils.isNotEmpty(fregion)){
            List<String> cityList = Arrays.stream(fregion.split(","))
                .collect(Collectors.toList());
            gdpParameterQuery.setFregionList(cityList);
        }

        return gdpParameterQuery;
    }

}
