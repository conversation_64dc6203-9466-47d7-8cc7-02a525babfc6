package com.hnbp.local.jjzbfx.controller;

import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.jjzbfx.model.GdpParameterQuery;
import com.hnbp.local.jjzbfx.service.GdpTrendsServiceImpl;
import com.hnbp.local.util.echarts.Bar.BarChart;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @DATE 2024/1/25
 */
@Controller
//@RequestMapping("/page/gdp")
public class MacroAnalysisAction {
    @Autowired
    private GdpTrendsServiceImpl gdpTrendsService;

    /**
     * 同类型gdp分析：
     * 1.同类型gdp产业趋势
     *
     * @throws Exception
     */
    @RequestMapping("getGdplnjjzbdbtlx_bzb")
    @ResponseBody
    public ResultMsg getGdplnjjzbdbtlx_bzb(GdpParameterQuery parameterMap) throws Exception {
        BarChart barChart = gdpTrendsService.getGdplnjjzbdbtlx(parameterMap);
        return ResultMsg.success(barChart);

    }

    /**
     * 同类型gdp趋势分析：
     * 2.同类型经济指标分析表
     *
     * @throws Exception
     */
    @RequestMapping("getGdpcyjgtlx_bzb")
    @ResponseBody
    public ResultMsg getGdpcyjgtlx_bzb(GdpParameterQuery parameterMap) throws Exception {
        List<HashMap<String, Object>> result = gdpTrendsService.getGdpcyjgtlx(parameterMap);
        return ResultMsg.success(result);
    }

    /**
     * 同类型gdp分析：
     * 1.同类型gdp产业趋势
     *
     * @throws Exception
     */
    @RequestMapping("getGdpdqjjzbdbtlx_bzb")
    @ResponseBody
    public ResultMsg getGdpdqjjzbdbtlx_bzb(GdpParameterQuery parameterMap) throws Exception {
        BarChart barChart = gdpTrendsService.getGdpdqjjzbdbtlx(parameterMap);
        return ResultMsg.success(barChart);
    }

    /**
     * 同类型gdp趋势分析：
     * 2.同类型行业经济指标分析表
     *
     * @throws Exception
     */
    @RequestMapping("getGdphyjjsjtlx_bzb")
    @ResponseBody
    public ResultMsg getGdphyjjsjtlx_bzb(GdpParameterQuery parameterMap) throws Exception {
        List<HashMap<String, Object>> result = gdpTrendsService.getGdphyjjsjtlx(parameterMap);
        return ResultMsg.success(result);
    }

    /**
     * 同类型gdp分析：
     * 1.同类型gdp产业趋势
     *
     * @throws Exception
     */
    @RequestMapping("getGdpdqhyjjzbdbtlx_bzb")
    @ResponseBody
    public ResultMsg getGdpdqhyjjzbdbtlx_bzb(GdpParameterQuery parameterMap) throws Exception {
        BarChart barChart = gdpTrendsService.getGdpdqhyjjzbdbtlx(parameterMap);
        return ResultMsg.success(barChart);

    }

    /**
     * 同类型gdp分析：
     * 1.同类型gdp产业趋势
     *
     * @throws Exception
     */
    @RequestMapping("getGdplnhyjjzbdbtlx_bzb")
    @ResponseBody
    public ResultMsg getGdplnhyjjzbdbtlx_bzb(GdpParameterQuery parameterMap) throws Exception {
        BarChart barChart = gdpTrendsService.getGdplnhyjjzbdbtlx(parameterMap);
        return ResultMsg.success(barChart);
    }

    //根据时间范围获取时间集合
    public String getTimeList(String ftime) {
        StringBuffer stringBuffer = new StringBuffer("(");
        String[] strings = ftime.split(" ");
        String str1 = strings[0];
        String str2 = strings[2];
        for (int i = Integer.valueOf(str1); i <= Integer.valueOf(str2); i++) {
            if (i == Integer.valueOf(str2)) {
                stringBuffer.append(String.valueOf(i));
            } else {
                stringBuffer.append("" + String.valueOf(i) + ",");
            }
        }
        stringBuffer.append(")");
        return stringBuffer.toString();
    }

    //根据行业字符串获取行业查询条件
    public String getFhyList(String fhy) {
        if (fhy == "" || fhy.equals("")) {
            return "";
        }
        StringBuffer stringBuffer = new StringBuffer("(");
        String[] strings = fhy.split(",");
        int size = strings.length;
        for (int i = 0; i < size; i++) {
            if (i == size - 1) {
                stringBuffer.append("'" + strings[i] + "'");
            } else {
                stringBuffer.append("'" + strings[i] + "',");
            }
        }
        stringBuffer.append(")");
        return stringBuffer.toString();
    }

}
