package com.hnbp.local.sjfx.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.utils.zhzs.MapUtils;
import com.hnbp.common.core.utils.zhzs.echarts.ChartUtil;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineChart;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineSeriesInitData;
import com.hnbp.local.sjfx.mapper.SjfxAnalysisMapper;
import com.hnbp.local.sjfx.model.FTlsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: 你的名字
 * @Date: 2023/05/23/15:38
 * @Description:
 */
@Service
public class SjfxAnalysisService {

    @Autowired
    SjfxAnalysisMapper sjfxAnalysisMapper;

    /**
     * Date: 2022/11/1
     * Description: 房产税-表格
     *
     **/
    public List<Map> getFcssjfx(FTlsDTO dto){
        return sjfxAnalysisMapper.getFcssjfx(dto);
    }

    /**
     * Date: 2022/11/1
     * Description: 城镇土地使用税-表格
     *
     **/
    public List<Map> getCztdsyssjfx(FTlsDTO dto){
        return sjfxAnalysisMapper.getCztdsyssjfx(dto);
    }

    public List<Map> getCcssjfx(FTlsDTO dto) {
        return sjfxAnalysisMapper.getCcssjfx(dto);
    }
}
