package com.hnbp.local.sjfx.mapper;


import com.hnbp.common.core.utils.zhzs.echarts.line.LineSeriesInitData;
import com.hnbp.local.sjfx.model.FTlsDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: 你的名字
 * @Date: 2023/05/23/15:39
 * @Description:
 */
@Mapper
public interface SjfxAnalysisMapper {


    List<Map>getFcssjfx(FTlsDTO dto);
    List<Map>getCztdsyssjfx(FTlsDTO dto);
    List<Map> getCcssjfx(FTlsDTO dto);
}
