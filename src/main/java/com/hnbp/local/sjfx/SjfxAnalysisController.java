package com.hnbp.local.sjfx;


import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.sjfx.model.FTlsDTO;
import com.hnbp.local.sjfx.service.SjfxAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/sjfx")
public class SjfxAnalysisController {
    @Autowired
    SjfxAnalysisService sjfxAnalysisService;

    @RequestMapping("/getFcssjfx_bzb")
    @ResponseBody
    public ResultMsg getFcssjfx(FTlsDTO dto) throws Exception {
        List<Map> result = sjfxAnalysisService.getFcssjfx(dto);
        return ResultMsg.success(result);
    }

    @RequestMapping("/getCztdsyssjfx_bzb")
    @ResponseBody
    public ResultMsg getCztdsyssjfx(FTlsDTO dto) throws Exception {
        List<Map> result = sjfxAnalysisService.getCztdsyssjfx(dto);
        return ResultMsg.success(result);
    }

    @RequestMapping("/getCcssjfx_bzb")
    @ResponseBody
    public ResultMsg getCcssjfx(FTlsDTO dto) throws Exception {
        List<Map> result = sjfxAnalysisService.getCcssjfx(dto);
        return ResultMsg.success(result);
    }
}
