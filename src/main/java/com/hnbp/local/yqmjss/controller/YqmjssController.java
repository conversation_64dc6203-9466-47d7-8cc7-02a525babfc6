package com.hnbp.local.yqmjss.controller;

import com.github.pagehelper.Page;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.yqmjss.service.YqmjssService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 园区亩均税收
 * - 迁移自版纳
 */
@RestController
@RequestMapping("/yqmjss")
@Tag(name = "园区亩均税收")
public class YqmjssController {

    @Autowired
    private YqmjssService yqmjssService;

    /**
     * Date: 2022/11/7
     * Description: 园区开发面积信息(西双版纳)
     *
     **/
    @RequestMapping("/getYqmjss")
    @ResponseBody
    public ResultMsg getYqmjss(@RequestParam Map<String, String> params) throws Exception {
        Page<Map<String, Object>> page = yqmjssService.getYqmjss(params);
        return ResultMsg.success(page.getResult(), page.getTotal());
    }

    @RequestMapping("/getYqmjss_mx")
    @ResponseBody
    public ResultMsg getYqmjss_mx(@RequestParam Map<String, String> params) throws Exception {
        Page<Map<String, Object>> page = yqmjssService.getYqmjss_mx(params);
        return ResultMsg.success(page.getResult(), page.getTotal());
    }
}
