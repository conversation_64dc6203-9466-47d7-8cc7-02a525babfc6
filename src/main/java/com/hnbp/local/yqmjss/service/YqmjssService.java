package com.hnbp.local.yqmjss.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.hnbp.local.yqmjss.mapper.YqmjssMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class YqmjssService {
    @Autowired
    private YqmjssMapper yqmjssMapper;

    public Page<Map<String, Object>> getYqmjss(Map<String, String> params) {
//        int page = Integer.parseInt(params.get("page"));
//        int limit = Integer.parseInt(params.get("limit"));
//        PageHelper.startPage(page, limit);
        return yqmjssMapper.getYqmjss(params);
    }

    public Page<Map<String, Object>> getYqmjss_mx(Map<String, String> params) {
        int page = Integer.parseInt(params.get("page"));
        int limit = Integer.parseInt(params.get("limit"));
        PageHelper.startPage(page, limit);
        return yqmjssMapper.getYqmjss_mx(params);
    }
}
