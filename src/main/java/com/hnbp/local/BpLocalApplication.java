package com.hnbp.local;

import com.hnbp.common.security.annotation.EnableCustomConfig;
import com.hnbp.shiro.config.ShiroConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class}, scanBasePackages = {"com.hnbp"})
@EnableTransactionManagement// 用来支持@Transactional 但在TransactionManagerConfig 配置了aop方式的事务
@EnableScheduling//定时任务支持
@EnableCustomConfig
@EnableCaching
@Import(ShiroConfig.class) // 显式引入 ShiroConfig
public class BpLocalApplication {
    public static void main(String[] args) {
        SpringApplication.run(BpLocalApplication.class, args);
    }
}
