package com.hnbp.local.dpgl.service.impl;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.local.dpgl.mapper.SsLargeScreenDisplayMapper;
import com.hnbp.local.dpgl.service.SsLargeScreenDisplayService;
import com.hnbp.local.util.echarts.Bar.BarChart;
import com.hnbp.local.util.echarts.Bar.BarSeriesInitData;
import com.hnbp.local.util.util.ChartUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @version 1.0.00
 * @Annotation:
 * @Author: Lsy
 */

@Service
public class SsLargeScreenDisplayServiceImpl implements SsLargeScreenDisplayService {

    @Autowired
    private SsLargeScreenDisplayMapper sslargeScreenDisplayMapper;


    /**
     * 税收按省/地/区级 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public Map<String, Object> getTaxDataByLevel(Map<String, Object> parameterMap) {
        Map<String, Object> ssLevelData = sslargeScreenDisplayMapper.getTaxDataByLevel(parameterMap);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        Map<String, Object> lastYearData = sslargeScreenDisplayMapper.getTaxDataByLevel(paramMap);
        if (ssLevelData != null) {
            Object fhj = ssLevelData.get("fhj");

            Map<String, Object> resultMap = new HashMap<>();
            ssLevelData.keySet().forEach(key -> {
                // 增幅比例
                resultMap.put(key+"zfb", getIncreasePercent(lastYearData==null?0:lastYearData.get(key), ssLevelData.get(key)));
                // 占比
                resultMap.put(key+"zb", getProportion(fhj, ssLevelData.get(key)));
            });
            ssLevelData.putAll(resultMap);
        }
        return ssLevelData;
    }

    /**
     * 税收按 区域 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public List<Map<String, Object>> getTaxDataByRegion(Map<String, Object> parameterMap) {
        List<Map<String, Object>> taxDataByRegion = sslargeScreenDisplayMapper.getTaxDataByRegion(parameterMap);

        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        List<Map<String, Object>>  lastYearData = sslargeScreenDisplayMapper.getTaxDataByRegion(paramMap);
        Map<String, Object> lastYearMap = new HashMap<>();
        lastYearData.stream().forEach(map -> lastYearMap.put(map.get("fskgk").toString(),map.get("fhj")));
        // 总税收
        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
        taxDataByRegion.forEach(map -> total.updateAndGet(v -> v.add(new BigDecimal(map.get("fhj").toString()))));
        // 遍历数据 计算同比 占比
        taxDataByRegion.stream().forEach(map -> {
            Object thisYear =  map.get("fhj");
            Object lastYear =  lastYearMap.get(map.get("fskgk").toString());
            // 同比
            map.put("fhjtb", getIncreasePercent(lastYear, thisYear));
            // 占比
            double fhjzb = Math.round((new BigDecimal(thisYear.toString()).doubleValue() / total.get().doubleValue()) * 10000.0) / 100.0;
            map.put("fhjzb", fhjzb);
        });

        return taxDataByRegion;
    }

    /**
     * 税收按 区域 税种 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public List<Map<String, Object>> getTaxDataByRegionTax(Map<String, Object> parameterMap) {
        List<Map<String, Object>> taxData = sslargeScreenDisplayMapper.getTaxDataByRegionTax(parameterMap);

        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        List<Map<String, Object>>  lastYearData = sslargeScreenDisplayMapper.getTaxDataByRegionTax(paramMap);
        Map<String, Object> lastYearMap = new HashMap<>();
        lastYearData.stream().forEach(map -> lastYearMap.put(map.get("fzsxm").toString(),map.get("fhj")));

        // 税收合计
        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
        taxData.stream().forEach(map -> {
            if ("合计".equals(map.get("fzsxm"))){
                total.updateAndGet(v -> new BigDecimal(map.get("fhj").toString()));
            }else {
                map.remove("fhjtb");
            }
        });
        // 遍历数据 计算同比 占比
        taxData.stream().forEach(map -> {
            if (!"合计".equals(map.get("fzsxm"))){
                Object thisYear =  map.get("fhj");
                Object lastYear =  lastYearMap.get(map.get("fzsxm").toString());
                // 同比
                map.put("fhjtb", getIncreasePercent(lastYear, thisYear));
                // 占比
                double fhjzb = getProportion(total.get(), map.get("fhj"));
                map.put("fhjzb", fhjzb);
            }
        });

        return taxData;
    }

    /**
     * 税收按 区域 项目明细 数据情况
     *
     */
    @Override
    public List<Map<String, Object>> getTaxDataByRegionFxm(Map<String, Object> parameterMap) {
        List<Map<String, Object>> taxData = sslargeScreenDisplayMapper.getTaxDataByRegionFxm(parameterMap);

        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        List<Map<String, Object>>  lastYearData = sslargeScreenDisplayMapper.getTaxDataByRegionFxm(paramMap);
        Map<String, Object> lastYearMap = new HashMap<>();
        lastYearData.stream().forEach(map -> lastYearMap.put(map.get("fjhxmmc").toString(),map.get("fhj")));

        // 全州数据
        paramMap = new HashMap<>(parameterMap);
        paramMap.put("fskgk", "全州");
        List<Map<String, Object>>  qzData = sslargeScreenDisplayMapper.getTaxDataByRegionFxm(paramMap);
        Map<String, Object> qzFhjMap = new HashMap<>();
        qzData.stream().forEach(map -> qzFhjMap.put(map.get("fjhxmmc").toString(),map.get("fhj")));


        // 遍历数据 计算同比 占比
        taxData.stream().forEach(map -> {
            if (!"合计".equals(map.get("fjhxmmc"))){
                Object thisYear =  map.get("fhj");
                Object lastYear =  lastYearMap.get(map.get("fjhxmmc").toString());
                Object qzFhj =  qzFhjMap.get(map.get("fjhxmmc").toString());
                // 同比
                map.put("fhjtb", getIncreasePercent(lastYear, thisYear));
                // 占比
                double fhjzb = getProportion(qzFhj, map.get("fhj"));
                map.put("fhjzb", fhjzb);
            }
        });

        return taxData;
    }

    /**
     * 税收按 行业 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public List<Map<String, Object>> getTaxDataByIndustry(Map<String, Object> parameterMap) {
        List<Map<String, Object>> industryList = sslargeScreenDisplayMapper.getTaxDataByIndustry(parameterMap);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        List<Map<String, Object>> lastYearList = sslargeScreenDisplayMapper.getTaxDataByIndustry(paramMap);

        // 总数
        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
        industryList.forEach(map -> total.updateAndGet(v -> v.add(new BigDecimal(map.get("fsssrhj").toString()))));

        // 创建一个HashMap用于存储去年的数据，键为"fhyml"的值，值为"fsssrhj"的值
        Map<String, Object> lastYearMap = new HashMap<>();
        // 对去年的数据列表进行流式处理，将每个map中的"fhyml"对应的值作为键，"fsssrhj"对应的值作为值，添加到lastYearMap中
        lastYearList.stream().forEach(map -> lastYearMap.put(map.get("fhyml").toString(),map.get("fsssrhj")));

        industryList.stream().forEach(map -> {
            Object thisYear =  map.get("fsssrhj");
            Object lastYear =  lastYearMap.get(map.get("fhyml").toString());
            // 合计增幅
            map.put("fhjzfb", getIncreasePercent(lastYear, thisYear));
            // 占比
            map.put("fhjzb", getProportion(total.get(), thisYear));
        });
        return industryList;
    }

    /**
     * 税收按 特色行业 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public List<Map<String, Object>> getTaxDataBySpecialIndustry(Map<String, Object> parameterMap) {
        List<Map<String, Object>> industryList = sslargeScreenDisplayMapper.getTaxDataBySpecialIndustry(parameterMap);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        List<Map<String, Object>> lastYearList = sslargeScreenDisplayMapper.getTaxDataBySpecialIndustry(paramMap);

        // 总数
        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
        industryList.forEach(map -> total.updateAndGet(v -> v.add(new BigDecimal(map.get("fsssrhj").toString()))));

        // 创建一个HashMap用于存储去年的数据，键为"ftshymc"的值，值为"fsssrhj"的值
        Map<String, Object> lastYearMap = new HashMap<>();
        // 对去年的数据列表进行流式处理，将每个map中的"ftshymc"对应的值作为键，"fsssrhj"对应的值作为值，添加到lastYearMap中
        lastYearList.stream().forEach(map -> lastYearMap.put(map.get("ftshymc").toString(),map.get("fsssrhj")));

        industryList.stream().forEach(map -> {
            Object thisYear =  map.get("fsssrhj");
            Object lastYear =  lastYearMap.get(map.get("ftshymc").toString());
            // 合计增幅
            map.put("fhjzfb", getIncreasePercent(lastYear, thisYear));
            // 占比
            map.put("fhjzb", getProportion(total.get(), thisYear));
        });
        return industryList;
    }

    /**
     * 税收按 部门 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public List<Map<String, Object>> getTaxDataByDepartment(Map<String, Object> parameterMap) {
        List<Map<String, Object>> departmentList = sslargeScreenDisplayMapper.getTaxDataByDepartment(parameterMap);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        List<Map<String, Object>> lastYearList = sslargeScreenDisplayMapper.getTaxDataByDepartment(paramMap);

        // 总数
        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
        departmentList.forEach(map -> total.updateAndGet(v -> v.add(new BigDecimal(map.get("fsssrhj").toString()))));

        // 创建一个HashMap用于存储去年的数据，键为"fbmmc"的值，值为"fsssrhj"的值
        Map<String, Object> lastYearMap = new HashMap<>();

        String mapKey = paramMap.get("fbmmc")==null?"fbmmc":"fhyfl";
        // 对去年的数据列表进行流式处理，将每个map中的"fbmmc"对应的值作为键，"fsssrhj"对应的值作为值，添加到lastYearMap中
        lastYearList.stream().forEach(map -> lastYearMap.put(map.get(mapKey).toString(),map.get("fsssrhj")));

        departmentList.stream().forEach(map -> {
            Object thisYear =  map.get("fsssrhj");
            Object lastYear =  lastYearMap.get(map.get(mapKey).toString());
            // 合计增幅
            map.put("fhjzfb", getIncreasePercent(lastYear, thisYear));
            // 占比
            map.put("fhjzb", getProportion(total.get(), thisYear));
        });
        return departmentList;
    }

    /**
     * 税收按 税种 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public List<Map<String, Object>> getTaxDataByTaxType(Map<String, Object> parameterMap) {
        List<Map<String, Object>> taxTypeList = sslargeScreenDisplayMapper.getTaxDataByTaxType(parameterMap);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        List<Map<String, Object>> lastYearList = sslargeScreenDisplayMapper.getTaxDataByTaxType(paramMap);

        // 创建一个HashMap用于存储去年的数据，键为"fzsxm"的值，值为"fhj"的值
        Map<String, Object> lastYearMap = new HashMap<>();
        // 对去年的数据列表进行流式处理，将每个map中的"fzsxm"对应的值作为键，"fhj"对应的值作为值，添加到lastYearMap中
        lastYearList.stream().forEach(map -> lastYearMap.put(map.get("fzsxm").toString(),map.get("fhj")));

        taxTypeList.stream().forEach(map -> {
            Object thisYear =  map.get("fhj");
            Object lastYear =  lastYearMap.get(map.get("fzsxm").toString());
            // 合计增幅
            map.put("fhjzfb", getIncreasePercent(lastYear, thisYear));
        });
        return taxTypeList;
    }

    /**
     * 税收按 税种分类 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public List<Map<String, Object>> getTaxDataByTaxCategory(Map<String, Object> parameterMap) {
        List<Map<String, Object>> taxCategoryList = sslargeScreenDisplayMapper.getTaxDataByTaxCategory(parameterMap);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        List<Map<String, Object>> lastYearList = sslargeScreenDisplayMapper.getTaxDataByTaxCategory(paramMap);

        // 总数
        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
        taxCategoryList.forEach(map -> total.updateAndGet(v -> v.add(new BigDecimal(map.get("fhj").toString()))));

        // 创建一个HashMap用于存储去年的数据，键为"flxmc"的值，值为"fhj"的值
        Map<String, Object> lastYearMap = new HashMap<>();
        // 对去年的数据列表进行流式处理，将每个map中的"flxmc"对应的值作为键，"fhj"对应的值作为值，添加到lastYearMap中
        lastYearList.stream().forEach(map ->
            lastYearMap.put(map.get("fzsxm")==null?map.get("flxmc").toString():map.get("fzsxm").toString(),map.get("fhj"))
        );

        taxCategoryList.stream().forEach(map -> {
            Object thisYear =  map.get("fhj");
            Object lastYear =  lastYearMap.get(map.get("fzsxm")==null?map.get("flxmc").toString():map.get("fzsxm").toString());
            // 合计增幅
            map.put("fhjzfb", getIncreasePercent(lastYear, thisYear));
            // 占比
            map.put("fhjzb", getProportion(total.get(), thisYear));
        });
        return taxCategoryList;
    }

    /**
     * 年度数据 按税种 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getYearDataByTaxType(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> yearsData = sslargeScreenDisplayMapper.getYearDataByTaxType(parameterMap);
        List<BarSeriesInitData> ssList = yearsData.stream().filter(bar -> "税收".equals(bar.getLegend())).collect(Collectors.toList());
        List<BarSeriesInitData> hsList = yearsData.stream().filter(bar -> "户数".equals(bar.getLegend())).collect(Collectors.toList());
        // 补全年份
        supplementYear(ssList, parameterMap,"税收");
        supplementYear(hsList, parameterMap,"户数");
        ssList.addAll(hsList);

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(ssList, extraParameter);
    }

    /**
     * 税收按 市场主体 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public List<Map<String, Object>> getTaxDataByMainstay(Map<String, Object> parameterMap) {
        List<Map<String, Object>> taxTypeList = sslargeScreenDisplayMapper.getTaxDataByMainstay(parameterMap);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        List<Map<String, Object>> lastYearList = sslargeScreenDisplayMapper.getTaxDataByMainstay(paramMap);

        // 总数
        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
        taxTypeList.forEach(map -> total.updateAndGet(v -> v.add(new BigDecimal(map.get("fsssrhj").toString()))));

        // 创建一个HashMap用于存储去年的数据，键为"fjhxmmc"的值，值为"fsssrhj"的值
        Map<String, Object> lastYearMap = new HashMap<>();
        // 对去年的数据列表进行流式处理，将每个map中的"fjhxmmc"对应的值作为键，"fsssrhj"对应的值作为值，添加到lastYearMap中
        lastYearList.stream().forEach(map -> lastYearMap.put(map.get("fjhxmmc").toString(),map.get("fsssrhj")));

        taxTypeList.stream().forEach(map -> {
            Object thisYear =  map.get("fsssrhj");
            Object lastYear =  lastYearMap.get(map.get("fjhxmmc").toString());
            // 合计增幅
            map.put("fhjzfb", getIncreasePercent(lastYear, thisYear));

            // 占比
            map.put("fhjzb", getProportion(total.get(), thisYear));
        });
        return taxTypeList;
    }

    /**
     * 年度数据 按行业 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getYearDataByIndustry(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> yearsData = sslargeScreenDisplayMapper.getYearDataByIndustry(parameterMap);
        // 补全年份
        supplementYear(yearsData, parameterMap,parameterMap.get("fhy").toString());

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(yearsData, extraParameter);
    }

    /**
     * 年度数据 按省/地/区级 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getYearDataByLevel(Map<String, Object> parameterMap) {
        List<Map<String, Object>> yearDataByLevel = sslargeScreenDisplayMapper.getYearDataByLevel(parameterMap);
        // 需要取的列
        String levelKey = parameterMap.get("fxm").toString();
        Object fjbxmmc = parameterMap.getOrDefault("fjbxmmc",levelKey);
        // 组装 柱状图数据
        List<BarSeriesInitData> dataList = yearDataByLevel.stream().map(map -> {
            BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
            barSeriesInitData.setyAxis(Double.valueOf(map.get(levelKey).toString()));
            barSeriesInitData.setxAxis(map.get("fnf").toString());
            barSeriesInitData.setLegend(fjbxmmc.toString());
            barSeriesInitData.setType("bar");
            return barSeriesInitData;
        }).collect(Collectors.toList());

        // 补全年份
        supplementYear(dataList, parameterMap,fjbxmmc.toString());
        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(dataList, extraParameter);
    }

    /**
     * 年度数据 按区域 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getYearDataByRegion(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> yearsData = sslargeScreenDisplayMapper.getYearDataByRegion(parameterMap);
        // 补全年份
        supplementYear(yearsData, parameterMap,parameterMap.get("fxm").toString());

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(yearsData, extraParameter);
    }

    /**
     * 年度数据 按主体 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getYearDataByMainstay(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> yearsData = sslargeScreenDisplayMapper.getYearDataByMainstay(parameterMap);
        // 补全年份
        supplementYear(yearsData, parameterMap,parameterMap.get("fxm").toString());

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(yearsData, extraParameter);
    }

    /**
     * 年度数据 按部门 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getYearDataByDepartment(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> yearsData = sslargeScreenDisplayMapper.getYearDataByDepartment(parameterMap);
        List<BarSeriesInitData> ssList = yearsData.stream().filter(bar -> "税收".equals(bar.getLegend())).collect(Collectors.toList());
        List<BarSeriesInitData> czList = yearsData.stream().filter(bar -> "产值".equals(bar.getLegend())).collect(Collectors.toList());
        // 补全年份
        supplementYear(ssList, parameterMap,"税收");
        supplementYear(czList, parameterMap,"产值");
        ssList.addAll(czList);

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(ssList, extraParameter);

    }

    /**
     * 月度数据 按行业 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getMonthDataByIndustry(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> data = sslargeScreenDisplayMapper.getMonthDataByIndustry(parameterMap);

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(data, extraParameter);
    }

    /**
     * 月度数据 按省/地/区级 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getMonthDataByLevel(Map<String, Object> parameterMap) {
        List<Map<String, Object>> data = sslargeScreenDisplayMapper.getMonthDataByLevel(parameterMap);
        // 需要取的列
        String levelKey = parameterMap.get("fxm").toString();
        Object fjbxmmc = parameterMap.getOrDefault("fjbxmmc",levelKey);
        // 组装 柱状图数据
        List<BarSeriesInitData> dataList = data.stream().map(map -> {
            BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
            barSeriesInitData.setyAxis(Double.valueOf(map.get(levelKey).toString()));
            barSeriesInitData.setxAxis(map.get("xaxis").toString());
            barSeriesInitData.setLegend(fjbxmmc.toString());
            barSeriesInitData.setType("bar");
            return barSeriesInitData;
        }).collect(Collectors.toList());

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(dataList, extraParameter);
    }

    /**
     * 月度数据 按区域 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getMonthDataByRegion(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> data = sslargeScreenDisplayMapper.getMonthDataByRegion(parameterMap);

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(data, extraParameter);
    }

    /**
     * 月度数据 按主体 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getMonthDataByMainstay(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> data = sslargeScreenDisplayMapper.getMonthDataByMainstay(parameterMap);
        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(data, extraParameter);
    }

    /**
     * 月度数据 按部门 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getMonthDataByDepartment(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> data = sslargeScreenDisplayMapper.getMonthDataByDepartment(parameterMap);

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(data, extraParameter);

    }

    /**
     * 月度数据 按税种 数据情况
     *
     * @param parameterMap
     * @return
     */
    @Override
    public BarChart getMonthDataByTaxType(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> data = sslargeScreenDisplayMapper.getMonthDataByTaxType(parameterMap);

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(data, extraParameter);
    }

    @Override
    public PageInfo rankingByTaxpayer(Map<String, Object> parameterMap) {
        return new PageInfo(sslargeScreenDisplayMapper.rankingByTaxpayer(parameterMap));
    }


    /** 获取有数据的最大日期 */
    @Override
    public String getSsdpMaxDateByData() {
        return sslargeScreenDisplayMapper.getSsdpMaxDateByData();
    }


    /**
     * 趋势年份不足时补充年份
     */
    public List<BarSeriesInitData> supplementYear(List<BarSeriesInitData> dataList, Map<String, Object> parameterMap,String Legend) {
        Integer limit = Integer.valueOf(parameterMap.get("limit").toString());
        if (dataList.size()<limit){
            // x轴 存在的年份
            List<Integer> xAxis = dataList.stream().map(bar ->Integer.valueOf(bar.getxAxis()))
                    .collect(Collectors.toList());
            Integer year = Integer.valueOf(parameterMap.get("fyear").toString());
            for (int i = 0; i < limit; i++){
                if (!xAxis.contains(year-i)){
                    BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
                    barSeriesInitData.setxAxis(String.valueOf((year-i)));
                    barSeriesInitData.setyAxis(0.0);
                    barSeriesInitData.setLegend(Legend);
                    barSeriesInitData.setType("bar");

                    dataList.add(barSeriesInitData);
                }
            }
        }

        dataList.sort(Comparator.comparing(
                bar -> Integer.valueOf(bar.getxAxis())
        ));
        return dataList;
    }

    /** 增减比例 */
    public double getIncreasePercent(BigDecimal lastYear, BigDecimal thisYear) {
        double percent = 0;
        if (lastYear!=null && thisYear!=null && lastYear.doubleValue() != 0) {
            percent = (thisYear.doubleValue() - lastYear.doubleValue()) / lastYear.doubleValue();
        }
        return Math.round(percent * 10000.0) / 100.0;
    }
    public double getIncreasePercent(Object lastYear, Object thisYear) {
        if (lastYear == null || thisYear == null) return 0;
        return this.getIncreasePercent(new BigDecimal(lastYear.toString()), new BigDecimal(thisYear.toString()));
    }
    /** 占比 */
    public double getProportion(BigDecimal total, BigDecimal num) {
        double percent = 0;
        if (total !=null && num!=null && total.doubleValue() != 0) {
            percent = num.doubleValue() / total.doubleValue();
        }
        return Math.round(percent * 10000.0) / 100.0;
    }
    public double getProportion(Object total, Object num) {
        if (total == null || num == null) return 0;
        return this.getProportion(new BigDecimal(total.toString()), new BigDecimal(num.toString()));
    }
}
