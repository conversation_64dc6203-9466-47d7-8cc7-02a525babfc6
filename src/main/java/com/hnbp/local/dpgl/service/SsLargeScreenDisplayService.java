package com.hnbp.local.dpgl.service;

import com.github.pagehelper.PageInfo;
import com.hnbp.local.util.echarts.Bar.BarChart;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0.00
 * @Annotation:
 * @Author: Lsy
 */

public interface SsLargeScreenDisplayService {



    /**
     * 税收按省/地/区级 数据情况
     * @param parameterMap
     * @return
     */
    Map<String, Object> getTaxDataByLevel(Map<String, Object> parameterMap);

    /**
     * 税收按 区域 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByRegion(Map<String, Object> parameterMap);

    /**
     * 税收按 区域 税种 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByRegionTax(Map<String, Object> parameterMap);

    /**
     * 税收按 区域 项目明细 数据情况
     *
     */
    List<Map<String, Object>> getTaxDataByRegionFxm(Map<String, Object> parameterMap);

    /**
     * 税收按 行业 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByIndustry(Map<String, Object> parameterMap);

    /**
     * 税收按 特色行业 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataBySpecialIndustry(Map<String, Object> parameterMap);



    /**
     * 税收按 部门 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByDepartment(Map<String, Object> parameterMap);



    /**
     * 税收按 税种 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByTaxType(Map<String, Object> parameterMap);


    /**
     * 税收按 税种分类 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByTaxCategory(Map<String, Object> parameterMap);


    /**
     * 年度数据 按税种 数据情况
     * @param parameterMap
     * @return
     */
    BarChart getYearDataByTaxType(Map<String, Object> parameterMap);


    /**
     * 税收按 市场主体 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByMainstay(Map<String, Object> parameterMap);



    /**
     * 年度数据 按行业 数据情况
     * @param parameterMap
     * @return
     */
    BarChart getYearDataByIndustry(Map<String, Object> parameterMap);

    /**
     * 年度数据 按省/地/区级 数据情况
     * @param parameterMap
     * @return
     */
    BarChart getYearDataByLevel(Map<String, Object> parameterMap);

    /**
     * 年度数据 按区域 数据情况
     * @param parameterMap
     * @return
     */
    BarChart getYearDataByRegion(Map<String, Object> parameterMap);

    /**
     * 年度数据 按主体 数据情况
     * @param parameterMap
     * @return
     */
    BarChart getYearDataByMainstay(Map<String, Object> parameterMap);

    /**
     * 年度数据 按部门 数据情况
     * @param parameterMap
     * @return
     */
    BarChart getYearDataByDepartment(Map<String, Object> parameterMap);


    /**
     * 月度数据 按行业 数据情况
     * @param parameterMap
     * @return
     */
    BarChart getMonthDataByIndustry(Map<String, Object> parameterMap);

    /**
     * 月度数据 按省/地/区级 数据情况
     * @param parameterMap
     * @return
     */
    BarChart getMonthDataByLevel(Map<String, Object> parameterMap);

    /**
     * 月度数据 按区域 数据情况
     * @param parameterMap
     * @return
     */
    BarChart getMonthDataByRegion(Map<String, Object> parameterMap);

    /**
     * 月度数据 按主体 数据情况
     * @param parameterMap
     * @return
     */
    BarChart getMonthDataByMainstay(Map<String, Object> parameterMap);

    /**
     * 月度数据 按部门 数据情况
     * @param parameterMap
     * @return
     */
    BarChart getMonthDataByDepartment(Map<String, Object> parameterMap);

    /**
     * 月度数据 按税种 数据情况
     * @param parameterMap
     * @return
     */
    BarChart getMonthDataByTaxType(Map<String, Object> parameterMap);

    /** 获取有数据的最大日期 */
    String getSsdpMaxDateByData();

    PageInfo rankingByTaxpayer(Map<String, Object> parameterMap);
}
