package com.hnbp.local.dpgl.controller;

import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.dpgl.service.SsLargeScreenDisplayService;
import com.hnbp.local.util.echarts.Bar.BarChart;
import com.hnbp.local.util.echarts.pie.PieChartsSeriesData;
import com.hnbp.local.util.echarts.pie.PieChart;
import com.hnbp.local.util.util.ChartUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0.00
 * @Annotation: 大屏治税
 * @Author: sheng_huang
 * @Data: 2024/5/14 14:26
 */

@Controller
@RequestMapping("ssdpzs")
@ResponseBody
public class SsLargeScreenDisplayController {


    @Autowired
    private SsLargeScreenDisplayService sslargeScreenDisplayService;


    /** 西双版纳税收按 省/地/区级 数据情况 */
    @RequestMapping("getTaxDataByLevel")
    public ResultMsg getTaxDataByLevel(@RequestParam Map<String, Object> parameterMap) throws IOException {
        Map<String, Object> ssLevelData = sslargeScreenDisplayService.getTaxDataByLevel(buildParam(parameterMap));
        return ResultMsg.success(ssLevelData);
    }

    /** 西双版纳税收按 区域 数据情况 */
    @RequestMapping("getTaxDataByRegion")
    public ResultMsg getTaxDataByRegion(@RequestParam Map<String, Object> parameterMap) throws IOException {

        List<Map<String, Object>> listData = sslargeScreenDisplayService.getTaxDataByRegion(buildParam(parameterMap));

        // 饼图结构数据
        List<PieChartsSeriesData> pieDataList = new ArrayList<>();
        for (Map<String, Object> map : listData) {
            Object fhj = map.get("fhj");
            // 格式化BigDecimal值
            map.put("fhj",fhj);

            PieChartsSeriesData pieData = new PieChartsSeriesData();
            pieData.setName(map.get("fskgk").toString());
            pieData.setValue(Double.parseDouble(fhj.toString()));
            pieDataList.add(pieData);
        }

        PieChart pieChart = new PieChart();
        pieChart.setTitle_text("区域税收结构");
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieDataList));
        pieChart.setSeries_data(pieDataList);

        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put("pieChart", pieChart);
        responseMap.put("listData", listData);


        return ResultMsg.success(responseMap);

    }

    /** 税收按 区域 税种数据情况 */
    @RequestMapping("getTaxDataByRegionTax")
    public ResultMsg getTaxDataByRegionTax(@RequestParam Map<String, Object> parameterMap) throws IOException {
        List<Map<String, Object>> listData = sslargeScreenDisplayService.getTaxDataByRegionTax(buildParam(parameterMap));

        return ResultMsg.success(listData);
    }

    /** 税收按 区域 项目明细 数据情况 */
    @RequestMapping("getTaxDataByRegionFxm")
    public ResultMsg getTaxDataByRegionFxm(@RequestParam Map<String, Object> parameterMap) throws IOException {
        List<Map<String, Object>> listData = sslargeScreenDisplayService.getTaxDataByRegionFxm(buildParam(parameterMap));

        return ResultMsg.success(listData);

    }

    /** 西双版纳税收按 行业 数据情况 */
    @RequestMapping("getTaxDataByIndustry")
    public ResultMsg getTaxDataByIndustry(@RequestParam Map<String, Object> parameterMap) throws IOException {
        List<Map<String, Object>> listData = sslargeScreenDisplayService.getTaxDataByIndustry(buildParam(parameterMap));
        return ResultMsg.success(listData);
    }

    /** 西双版纳税收按 行业 数据情况 */
    @RequestMapping("getTaxDataBySpecialIndustry")
    public ResultMsg getTaxDataBySpecialIndustry(@RequestParam Map<String, Object> parameterMap) throws IOException {
        List<Map<String, Object>> listData = sslargeScreenDisplayService.getTaxDataBySpecialIndustry(buildParam(parameterMap));
        return ResultMsg.success(listData);

    }

    /** 西双版纳税收按 部门 数据情况 */
    @RequestMapping("getTaxDataByDepartment")
    public ResultMsg getTaxDataByDepartment(@RequestParam Map<String, Object> parameterMap) throws IOException {
        List<Map<String, Object>> listData = sslargeScreenDisplayService.getTaxDataByDepartment(buildParam(parameterMap));

        return ResultMsg.success(listData);
    }

    /** 西双版纳税收按 税种 数据情况 */
    @RequestMapping("getTaxDataByTaxType")
    public ResultMsg getTaxDataByTaxType(@RequestParam Map<String, Object> parameterMap) throws IOException {
        List<Map<String, Object>> listData = sslargeScreenDisplayService.getTaxDataByTaxType(buildParam(parameterMap));

        return ResultMsg.success(listData);
    }

    /** 西双版纳税收按 税种分类 数据情况 */
    @RequestMapping("getTaxDataByTaxCategory")
    public ResultMsg getTaxDataByTaxCategory(@RequestParam Map<String, Object> parameterMap) throws IOException {
        Map<String, Object> responseMap = new HashMap<>();
        Map<String, Object> param = buildParam(parameterMap);
        Object flxmc = param.get("flxmc");
        if(flxmc!=null){
            param.put("flxmc",flxmc);
            if ("all".equals(flxmc)){
                param.put("flxmcAll","流转税','所得税','财产行为资源税");
            }
        }
        List<Map<String, Object>> listData = sslargeScreenDisplayService.getTaxDataByTaxCategory(param);

        // 饼图结构数据
        List<PieChartsSeriesData> pieDataList = new ArrayList<>();
        for (Map<String, Object> map : listData) {
            Object fhj = map.get("fhj");
            // 格式化BigDecimal值
            map.put("fhj",fhj);
            String name = map.get("fzsxm")!=null?map.get("fzsxm").toString():map.get("flxmc").toString();
            PieChartsSeriesData pieData = new PieChartsSeriesData();
            pieData.setName(name);
            pieData.setValue(Double.parseDouble(fhj.toString()));
            pieDataList.add(pieData);
        }
        PieChart pieChart = new PieChart();
        pieChart.setTitle_text("税种");
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieDataList));
        pieChart.setSeries_data(pieDataList);

        responseMap.put("listData", listData);
        responseMap.put("pieChart", pieChart);

        return ResultMsg.success(responseMap);

    }

    /** 西双版纳税收按 市场主体 数据情况 */
    @RequestMapping("getTaxDataByMainstay")
    public ResultMsg getTaxDataByMainstay(@RequestParam Map<String, Object> parameterMap) throws IOException {
        List<Map<String, Object>> listData = sslargeScreenDisplayService.getTaxDataByMainstay(buildParam(parameterMap));

        return ResultMsg.success(listData);
    }

    /** 年度数据 按行业 数据情况 */
    @RequestMapping("getYearDataByIndustry")
    public ResultMsg getYearDataByIndustry(@RequestParam Map<String, Object> parameterMap) throws IOException {
        BarChart barChart = sslargeScreenDisplayService.getYearDataByIndustry(buildParam(parameterMap));
        return ResultMsg.success(barChart);
    }


    /** 趋势 年度数据 数据情况 */
    @RequestMapping("getYearTrendData")
    public ResultMsg getYearTrendData(@RequestParam Map<String, Object> parameterMap) throws IOException {
        Object typeObj = parameterMap.get("type");
        String type = null;
        if(typeObj!=null){
            type = typeObj.toString();
        }
        BarChart barChart = null;
        switch(type) {
            case "特色行业":
            case "行业":
                Map<String, Object> paramMap = new HashMap<>(parameterMap);
                paramMap.put("fhy", paramMap.get("fxm"));
                barChart = sslargeScreenDisplayService.getYearDataByIndustry(buildParam(paramMap));
                break;
            case "级别":
                barChart = sslargeScreenDisplayService.getYearDataByLevel(buildParam(parameterMap));
                break;
            case "区域":
                barChart = sslargeScreenDisplayService.getYearDataByRegion(buildParam(parameterMap));
                break;
            case "主体":
                barChart = sslargeScreenDisplayService.getYearDataByMainstay(buildParam(parameterMap));
                break;
            case "部门":
                barChart = sslargeScreenDisplayService.getYearDataByDepartment(buildParam(parameterMap));
                break;
            case "税种":
                barChart = sslargeScreenDisplayService.getYearDataByTaxType(buildParam(parameterMap));
                break;
        }

        return ResultMsg.success(barChart);
    }


    /** 趋势 月度数据 数据情况 */
    @RequestMapping("getMonthTrendData")
    public ResultMsg getMonthTrendData(@RequestParam Map<String, Object> parameterMap) throws IOException {
        Object typeObj = parameterMap.get("type");
        String type = null;
        if(typeObj!=null){
            type = typeObj.toString();
        }
        BarChart barChart = null;
        switch(Objects.requireNonNull(type)) {
            case "特色行业":
            case "行业":
                Map<String, Object> paramMap = new HashMap<>(parameterMap);
                paramMap.put("fhy", paramMap.get("fxm"));
                barChart = sslargeScreenDisplayService.getMonthDataByIndustry(buildParam(paramMap));
                break;
            case "级别":
                barChart = sslargeScreenDisplayService.getMonthDataByLevel(buildParam(parameterMap));
                break;
            case "区域":
                barChart = sslargeScreenDisplayService.getMonthDataByRegion(buildParam(parameterMap));
                break;
            case "主体":
                barChart = sslargeScreenDisplayService.getMonthDataByMainstay(buildParam(parameterMap));
                break;
            case "部门":
                barChart = sslargeScreenDisplayService.getMonthDataByDepartment(buildParam(parameterMap));
                break;
            case "税种":
                barChart = sslargeScreenDisplayService.getMonthDataByTaxType(buildParam(parameterMap));
                break;
        }

        return ResultMsg.success(barChart);
    }

    /**
     * 按纳税人排名统计(表)
     *
     * @return PageInfo
     * @Title rankingByTaxpayer
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
    @RequestMapping("rankingByTaxpayer")
    @ResponseBody
    public ResultMsg rankingByTaxpayer(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        //参数处理
        processParam(parameterMap);
        PageInfo pageInfo = sslargeScreenDisplayService.rankingByTaxpayer(parameterMap);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    /** 获取有数据的最大日期 */
    @RequestMapping("getSsdpMaxDateByData")
    public ResultMsg getSsdpMaxDateByData() {
        String maxDate = sslargeScreenDisplayService.getSsdpMaxDateByData();
        return ResultMsg.success("成功",maxDate);
    }




    /**
     * 处理参数
     * @param parameterMap
     * @return
     */
    private Map<String,Object> buildParam(Map<String,Object> parameterMap){
        Map<String, Object> param = new HashMap<>();
        param.putAll(parameterMap);
        Object fyear = param.get("fyear");
        if(fyear!=null){
            String fyearStr = fyear.toString();
            // 前一年
            param.put("lastYear",Integer.parseInt(fyearStr)-1);
            param.put("fyear",Integer.parseInt(fyearStr));
        }
        Object fhy = param.get("fhy");
        if(fhy!=null){
            String fhyStr = fhy.toString();
            param.put("fhy",fhyStr);
            param.put("fhyList",fhyStr.split(","));
        }
        Object level = param.get("fxm");
        if(level!=null){
            String levelStr = level.toString();
            param.put("fxm",levelStr);
        }
        Object limit = param.get("limit");
        if(limit != null){
            String limitStr = limit.toString();
            param.put("limit",Integer.parseInt(limitStr));
        }else {
            param.put("limit",5);
        }

        return param;
    }

    private void processParam(Map<String, Object> parameterMap) {
        String fyear  = parameterMap.get("fyear").toString();
        String fmonth = parameterMap.get("fmonth").toString();
        parameterMap.put("start_year", fyear);
        parameterMap.put("fstartmonth", "01");
        parameterMap.put("fendmonth", fmonth);
        parameterMap.put("frkrq", String.format("%s-%s ~ %s-%s", fyear, "01", fyear, fmonth));

        parameterMap.put("fzsxmList", Arrays.stream("增值税,企业所得税,契税,土地增值税,耕地占用税,资源税,房产税,城市维护建设税,车辆购置税,城镇土地使用税,车船税,个人所得税,烟叶税,印花税,消费税,环境保护税,营业税".split(",")).collect(Collectors.toList()));

        parameterMap.put("frkkj", "全口径税收");
        parameterMap.put("fqsje", 100);
        parameterMap.put("fnsrpm", 1000);

        Object fssqy = parameterMap.get("fssqy");
        if (null != fssqy) {
            parameterMap.put("fssqyList", Arrays.stream(fssqy.toString().split(","))
                    .map(String::trim) // 去除空格
                    .map(String::valueOf) // 转换为目标类型
                    .collect(Collectors.toList()));
        }
    }
}
