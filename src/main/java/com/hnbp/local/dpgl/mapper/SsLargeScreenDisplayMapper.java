package com.hnbp.local.dpgl.mapper;

import com.hnbp.local.util.echarts.Bar.BarSeriesInitData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0.00
 * @Annotation:
 * @Author: Lsy
 */
@Mapper
public interface SsLargeScreenDisplayMapper {


    /**
     * 税收按省/地/区级 数据情况
     * @param parameterMap
     * @return
     */
    Map<String, Object> getTaxDataByLevel(Map<String, Object> parameterMap);

    /**
     * 税收按 区域 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByRegion(Map<String, Object> parameterMap);

    /**
     * 税收按 区域 税种 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByRegionTax(Map<String, Object> parameterMap);

    /**
     * 税收按 区域 项目明细 数据情况
     *
     */
    List<Map<String, Object>> getTaxDataByRegionFxm(Map<String, Object> parameterMap);


    /**
     * 税收按 行业 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByIndustry(Map<String, Object> parameterMap);

    /**
     * 税收按 特色行业 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataBySpecialIndustry(Map<String, Object> parameterMap);


    /**
     * 税收按 部门 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByDepartment(Map<String, Object> parameterMap);



    /**
     * 税收按 税种 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByTaxType(Map<String, Object> parameterMap);


    /**
     * 税收按 税种 分类 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByTaxCategory(Map<String, Object> parameterMap);



    /**
     * 年度数据 税种 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getYearDataByTaxType(Map<String, Object> parameterMap);


    /**
     * 税收按 市场主体 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByMainstay(Map<String, Object> parameterMap);


    /**
     * 年度数据 按行业 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getYearDataByIndustry(Map<String, Object> parameterMap);

    /**
     * 年度数据 按省/地/区级 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getYearDataByLevel(Map<String, Object> parameterMap);



    /**
     * 年度数据 按区域 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getYearDataByRegion(Map<String, Object> parameterMap);


    /**
     * 年度数据 按主体 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getYearDataByMainstay(Map<String, Object> parameterMap);


    /**
     * 年度数据 按部门 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getYearDataByDepartment(Map<String, Object> parameterMap);



    /**
     * 月度数据 按行业 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getMonthDataByIndustry(Map<String, Object> parameterMap);

    /**
     * 月度数据 按省/地/区级 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getMonthDataByLevel(Map<String, Object> parameterMap);



    /**
     * 月度数据 按区域 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getMonthDataByRegion(Map<String, Object> parameterMap);


    /**
     * 月度数据 按主体 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getMonthDataByMainstay(Map<String, Object> parameterMap);


    /**
     * 月度数据 按部门 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getMonthDataByDepartment(Map<String, Object> parameterMap);


    /**
     * 月度数据 税种 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getMonthDataByTaxType(Map<String, Object> parameterMap);

    List<Map<String, Object>> rankingByTaxpayer(Map<String, Object> parameterMap);

    /** 获取有数据的最大日期 */
    String getSsdpMaxDateByData();
}
