package com.hnbp.local.fxkgl.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 *  风险库 实体类
 *  TB_DW_ZHZS_FXKGL_GEN
 */
@Data
public class RiskLibrary {
    private String sourceId;
    private String createTime;
    private String updateTime;
    private Integer status;
    private String batchNo;
    private String fid;
    /** 风险预警模型 */
    private String ffxyjmx;
    /** 分析预警日期起 */
    private String ffxyjrqq;
    /** 分析预警日期至 */
    private String ffxyjrqz;
    /** 预警户数 */
    private Long fyjhs;
    /** 预计可征收空间 */
    private String fyjkzskj;
    /** 报告生成日期 */
    private String fbgscrq;
    /** 创建人 */
    private String fcjr;

    /** 附件 */
    private List<RiskLibraryFile> fileList;
}