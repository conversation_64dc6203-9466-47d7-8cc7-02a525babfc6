package com.hnbp.local.fxkgl.model;

import lombok.Data;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;

/**
 *  风险库附件 实体类
 *  TB_DW_ZHZS_FXKGL_FILE_GEN
 *
 */
@Data
public class RiskLibraryFile {
    private String sourceId;
    private String createTime;
    private String updateTime;
    private Integer status;
    private String batchNo;
    private String fid;
    /** 类别 */
    private String flb;
    /** 风险预警模型ID */
    private String ffxyjmxid;
    /** 文件名称 */
    private String fwjmc;
    /** 文件类型 */
    private String fwjlx;
    /** 文件路径 */
    private String fwjlj;

    private Path path;

}