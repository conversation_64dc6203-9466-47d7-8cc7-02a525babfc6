package com.hnbp.local.fxkgl.service;

import cn.hutool.core.util.IdUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.local.fxkgl.mapper.RiskLibraryFileMapper;
import com.hnbp.local.fxkgl.mapper.RiskLibraryMapper;
import com.hnbp.local.fxkgl.model.RiskLibraryFile;
import com.hnbp.local.fxkgl.model.RiskLibrary;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 风险库管理Impl
 * @date 2025-05-13
 */
@Service
public class RiskLibraryService {

    @Autowired
    private RiskLibraryMapper riskLibraryMapper;
    @Autowired
    private RiskLibraryFileService riskLibraryFileService;

    public PageInfo getRiskLibraryData(Map<String, Object> parameterMap) {
        String page = parameterMap.getOrDefault("page", "1").toString();
        String limit = parameterMap.getOrDefault("limit", "100").toString();
        PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(limit));
        return new PageInfo(riskLibraryMapper.queryRiskLibraryList(parameterMap));
    }

    /** 保存 */
    public RiskLibrary save(RiskLibrary riskLibrary) {
        if (riskLibrary.getFid() == null) {
            riskLibrary.setFid(IdUtil.fastSimpleUUID());
            riskLibraryMapper.insert(riskLibrary);
        } else {
            riskLibraryMapper.update(riskLibrary);
        }
        // 删除现有的附件
        riskLibraryFileService.deleteByFxyjmxId(riskLibrary.getFid());
        // 新附件设置父ID
        riskLibrary.getFileList().forEach(file -> {
            file.setFfxyjmxid(riskLibrary.getFid());
        });
        // 批量保存附件
        riskLibraryFileService.insert(riskLibrary.getFileList());

        return riskLibrary;
    }

    /** 删除 */
    public void delete(String id) {
        riskLibraryMapper.deleteById(id);
        riskLibraryFileService.deleteByFxyjmxId(id);
    }
    public List<RiskLibraryFile> getRiskLibraryFiles(List<String> riskIds) {
        List<RiskLibraryFile> result = riskLibraryMapper.getRiskLibraryFiles(riskIds);
        return result;
    }
}
