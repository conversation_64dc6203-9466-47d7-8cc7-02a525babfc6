package com.hnbp.local.fxkgl.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.hnbp.local.fxkgl.mapper.RiskLibraryFileMapper;
import com.hnbp.local.fxkgl.model.RiskLibraryFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 *
 * 风险库 附件 Service 层
 */
@Service
public class RiskLibraryFileService {

    @Autowired
    private RiskLibraryFileMapper riskLibraryFileMapper;

    /**
     * 新增风险库附件
     *
     * @param riskLibraryFile 风险库附件对象
     * @return 影响的行数
     */
    public void insert(RiskLibraryFile riskLibraryFile) {
        riskLibraryFile.setFid(IdUtil.fastSimpleUUID());
        String path = riskLibraryFile.getFwjlj();
        if (StrUtil.isBlank(riskLibraryFile.getFwjlx())){
            // 从路径中取文件类型
            riskLibraryFile.setFwjlx(path.substring(path.lastIndexOf(".") + 1));
        }
        if (StrUtil.isBlank(riskLibraryFile.getFwjmc())){
            // 从路径中取文件名称
            riskLibraryFile.setFwjmc(path.substring(path.lastIndexOf("/") + 1));
        }
        riskLibraryFileMapper.insert(riskLibraryFile);
    }

    public void insert(List<RiskLibraryFile> list) {
        list.forEach(this::insert);
    }

    /**
     * 按风险预警模型ID删除风险库附件
     *
     * @param id 风险预警模型ID
     * @return 影响的行数
     */
    public int deleteByFxyjmxId(String id) {
        return riskLibraryFileMapper.deleteByFxyjmxId(id);
    }

    /**
     * 按ID删除风险库附件
     *
     * @param fid 附件ID
     * @return 影响的行数
     */
    public int deleteById(String fid) {
        return riskLibraryFileMapper.deleteById(fid);
    }

    /**
     * 查询风险库附件列表
     *
     * @param riskLibraryFile 查询条件
     * @return 风险库附件列表
     */
    public List<RiskLibraryFile> queryRiskLibraryFileList(RiskLibraryFile riskLibraryFile) {
        return riskLibraryFileMapper.queryRiskLibraryFileList(riskLibraryFile);
    }
}