package com.hnbp.local.fxkgl.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.fxkgl.model.RiskLibrary;
import com.hnbp.local.fxkgl.model.RiskLibrary;
import com.hnbp.local.fxkgl.model.RiskLibraryFile;
import com.hnbp.local.fxkgl.service.RiskLibraryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @Description: 风险库管理controller
 * @date 2025-05-13
 */
@RestController
@RequestMapping("fxkgl")
public class RiskLibraryController {

    @Autowired
    private RiskLibraryService riskLibraryService;

    @Value("${file.upload.path}")
    private String uploadPath;

    /**
     * @Description: 风险库管理表格数据
     */
    @RequestMapping("getRiskLibraryList")
    public ResultMsg getRiskLibraryList(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        PageInfo pageInfo = riskLibraryService.getRiskLibraryData(parameterMap);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    /** 保存 */
    @RequestMapping("saveRiskLibrary")
    public ResultMsg saveRiskLibrary(@RequestBody RiskLibrary riskLibrary) throws IOException, SQLException {
        riskLibraryService.save(riskLibrary);
        return ResultMsg.success();
    }

    /** 删除 */
    @RequestMapping("deleteRiskLibrary")
    public ResultMsg deleteRiskLibrary(RiskLibrary riskLibrary) throws IOException, SQLException {
        riskLibraryService.delete(riskLibrary.getFid());
        return ResultMsg.success();
    }

    @PostMapping(value = "/downloadZip")
    public ResponseEntity<Resource> download(@RequestBody(required = true) Map<String, Object> parameterMap) {
        // pageSize 可能要手动调大一点，先过演示
        PageInfo pageInfo = riskLibraryService.getRiskLibraryData(parameterMap);
        List<RiskLibrary> risks = pageInfo.getList();

        if (CollectionUtils.isEmpty(risks)) {
            return ResponseEntity.status(HttpStatus.NO_CONTENT).body(null);
        }

        List<String> riskIdList = risks.stream().map(RiskLibrary::getFid).distinct().collect(Collectors.toList());
        List<RiskLibraryFile> files = riskLibraryService.getRiskLibraryFiles(riskIdList);

        if (CollectionUtils.isEmpty(files)) {
            return ResponseEntity.status(HttpStatus.NO_CONTENT).body(null);
        }

        Map<String, List<RiskLibraryFile>> riskAndFileMapping = files.stream()
                .peek(file -> {
                    String fwjmc = file.getFwjmc();
                    String fwjlx = file.getFwjlx();
                    String fwjlj = file.getFwjlj();
                    file.setPath(Paths.get(uploadPath, fwjlj));
                })
                .collect(Collectors.groupingBy(RiskLibraryFile::getFfxyjmxid));

        // Create a ZIP file in memory
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        // Create a ZIP file in memory
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream)) {
            for (RiskLibrary risk : risks) {
                String fbgscrq = risk.getFbgscrq();
                // 去除 fbgscrq 的 "-"
                fbgscrq = fbgscrq.replace("-", "");
                String riskId = risk.getFid(), fxyjmx = risk.getFfxyjmx();
                zipOutputStream.putNextEntry(new ZipEntry(fxyjmx+ fbgscrq + "/")); // 每个项目一个目录
                List<RiskLibraryFile> attaches = riskAndFileMapping.get(riskId);
                if (! CollectionUtils.isEmpty(attaches)) {
                    List<Path> paths = attaches.stream().map(RiskLibraryFile::getPath).filter(Objects::nonNull).collect(Collectors.toList());
                    for (Path path : paths) { // 每个项目的附件
                        zipOutputStream.putNextEntry(new ZipEntry(fxyjmx+ fbgscrq + "/" + path.getFileName().toString()));
                        Files.copy(path, zipOutputStream);
                        zipOutputStream.closeEntry();
                    }
                }
                zipOutputStream.closeEntry();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // Convert ByteArrayOutputStream to InputStreamResource
        Resource resource = new InputStreamResource(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()));

        // Set the response headers
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=风险库附件"+ DateUtil.now() +".zip");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

}
