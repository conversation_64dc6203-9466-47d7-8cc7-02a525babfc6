package com.hnbp.local.fxkgl.controller;

import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.fxkgl.model.RiskLibrary;
import com.hnbp.local.fxkgl.model.RiskLibraryFile;
import com.hnbp.local.fxkgl.service.RiskLibraryFileService;
import com.hnbp.local.fxkgl.service.RiskLibraryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 风险库附件管理controller
 * @date 2025-05-13
 */
@RestController
@RequestMapping("fxkgl")
public class RiskLibraryFileController {

    @Autowired
    private RiskLibraryFileService riskLibraryFileService;


    /**
     * 风险库附件管理表格数据
     */
    @RequestMapping("getRiskLibraryFileList")
    public ResultMsg getRiskLibraryFileList(RiskLibraryFile riskLibraryFile) throws IOException, SQLException {
        List<RiskLibraryFile> libraryFileList = riskLibraryFileService.queryRiskLibraryFileList(riskLibraryFile);
        return ResultMsg.success(libraryFileList);
    }

    /** 保存 */
    @RequestMapping("saveRiskLibraryFile")
    public ResultMsg saveRiskLibraryFile(@RequestBody RiskLibraryFile riskLibraryFile) throws IOException, SQLException {
        riskLibraryFileService.insert(riskLibraryFile);
        return ResultMsg.success();
    }

    /** 删除 */
    @RequestMapping("deleteRiskLibraryFile")
    public ResultMsg deleteRiskLibraryFile(RiskLibraryFile riskLibraryFile) throws IOException, SQLException {
        riskLibraryFileService.deleteById(riskLibraryFile.getFid());
        return ResultMsg.success();
    }
}
