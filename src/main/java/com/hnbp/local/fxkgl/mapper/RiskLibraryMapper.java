package com.hnbp.local.fxkgl.mapper;

import com.hnbp.local.fxkgl.model.RiskLibrary;
import com.hnbp.local.fxkgl.model.RiskLibraryFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 * 风险库
 */
@Mapper
public interface RiskLibraryMapper {

    /** 添加 */
    int insert(RiskLibrary riskLibrary);

    /** 更新 */
    int update(RiskLibrary riskLibrary);

    /** 删除 */
    int deleteById(@Param("fid") String fid);

    /** 查询列表 */
    List<RiskLibrary> queryRiskLibraryList(Map<String,Object> parameterMap);

    List<RiskLibraryFile> getRiskLibraryFiles(@Param("riskIds") List<String> riskIds);
}
