package com.hnbp.local.fxkgl.mapper;

import com.hnbp.local.fxkgl.model.RiskLibraryFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 *
 * 风险库 附件
 */
@Mapper
public interface RiskLibraryFileMapper {

    /** 新增 */
    int insert(RiskLibraryFile riskLibraryFile);

    /** 按 风险预警模型ID 删除 */
    int deleteByFxyjmxId(@Param("ffxyjmxid") String id);

    /** 按 ID 删除 */
    int deleteById(@Param("fid") String fid);

    /** 查询列表 */
    List<RiskLibraryFile> queryRiskLibraryFileList(RiskLibraryFile riskLibraryFile);

}
