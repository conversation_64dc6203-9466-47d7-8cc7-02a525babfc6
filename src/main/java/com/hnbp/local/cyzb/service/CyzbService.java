package com.hnbp.local.cyzb.service;

import com.github.pagehelper.Page;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineChart;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineSeriesInitData;
import com.hnbp.local.cyzb.mapper.CyzbMapper;
import com.hnbp.local.util.echarts.Bar.BarChart;
import com.hnbp.local.util.echarts.Bar.BarSeriesInitData;
import com.hnbp.local.util.util.ChartUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Service
public class CyzbService {
    @Autowired
    private CyzbMapper cyzbMapper;

    public Page<Map<String, Object>> getIndustrial(Map<String, String> params) {
        return cyzbMapper.getIndustrial(params);
    }

    public BarChart getjczbGDP_bar(Map<String, String> params){
        List<BarSeriesInitData> barSeriesInitData = new ArrayList<>();
        List<Map<String, Object>> data = cyzbMapper.getjczbGDPbar(params);
        for (Map<String, Object> dataMap : data) {
            String xAxis = dataMap.get("年份").toString();

            BarSeriesInitData ss = new BarSeriesInitData();
            ss.setxAxis(xAxis);
            ss.setyAxis(Optional.ofNullable(dataMap.get("税收完成情况")).map(Object::toString).map(BigDecimal::new).map(BigDecimal::doubleValue).orElse(0d));
            ss.setLegend("税收完成情况");
            ss.setType("bar");
            barSeriesInitData.add(ss);

            BarSeriesInitData gdp = new BarSeriesInitData();
            gdp.setxAxis(xAxis);
            gdp.setyAxis(Optional.ofNullable(dataMap.get("gdp")).map(Object::toString).map(BigDecimal::new).map(BigDecimal::doubleValue).orElse(0d));
            gdp.setLegend("gdp");
            gdp.setType("bar");
            barSeriesInitData.add(gdp);

            BarSeriesInitData gdphsl = new BarSeriesInitData();
            gdphsl.setxAxis(xAxis);
            gdphsl.setyAxis(Optional.ofNullable(dataMap.get("含税量")).map(Object::toString).map(BigDecimal::new).map(BigDecimal::doubleValue).orElse(0d));
            gdphsl.setLegend("含税量");
            gdphsl.setType("line");
            barSeriesInitData.add(gdphsl);
        }
        //数据封装额外数据
        HashMap extraParameter = new HashMap<>();
        String fssqy ="";
        if(params.get("fssqy")!=null){
            fssqy = params.get("fssqy").toString();
        }
        //数据封装
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text( fssqy+"GDP含税量情况图");
        return barChart;
    }
}
