package com.hnbp.local.cyzb.controller;

import com.github.pagehelper.Page;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineChart;
import com.hnbp.local.cyzb.service.CyzbService;
import com.hnbp.local.util.echarts.Bar.BarChart;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 产业指标
 */
@RestController
@RequestMapping("/cyzb")
@Tag(name = "产业指标")
public class CyzbController {

    @Autowired
    private CyzbService cyzbService;

    /**
     * Date: 2022/11/7
     * Description: 园区开发面积信息(西双版纳)
     *
     **/
    @RequestMapping("/getIndustrial_bzb")
    @ResponseBody
    public ResultMsg getIndustrial(@RequestParam Map<String, String> params) throws Exception {
        Page<Map<String, Object>> page = cyzbService.getIndustrial(params);
        return ResultMsg.success(page.getResult(), page.getTotal());
    }

    @RequestMapping("/getjczbGDP_bar_bzb")
    @ResponseBody
    public ResultMsg getjczbGDP_bar(@RequestParam Map<String, String> params) throws Exception {
        BarChart result = cyzbService.getjczbGDP_bar(params);
        return ResultMsg.success(result);
    }
}
