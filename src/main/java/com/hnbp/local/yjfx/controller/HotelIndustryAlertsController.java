package com.hnbp.local.yjfx.controller;


import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.util.paging.PageParameter;
import com.hnbp.local.yjfx.service.HotelIndustryAlertsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Map;

/**
 * 酒店行业预警
 */
@RestController
@RequestMapping("yjfx")
public class HotelIndustryAlertsController {


    @Autowired
    private HotelIndustryAlertsService hotelIndustryAlertsService;


    /**
     * @Description: 酒店预警数据
     */
    @RequestMapping("getHotelData_fb")
    public ResultMsg getHotelData_fb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        PageInfo pageInfo = hotelIndustryAlertsService.getHotelData_fb(parameterMap);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    /**
     * @Description: 酒店价格
     */
    @RequestMapping("getHotelPriceData")
    public ResultMsg getHotelPriceData(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        PageInfo pageInfo = hotelIndustryAlertsService.getHotelPriceData(parameterMap);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


}
