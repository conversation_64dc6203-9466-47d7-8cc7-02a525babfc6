package com.hnbp.local.yjfx.controller;


import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.common.core.utils.StringUtils;
import com.hnbp.local.yjfx.service.DrugstoreIndustryAlertsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 药店行业预警
 */
@RestController
@RequestMapping("yjfx")
public class DrugstoreIndustryAlertsController {


    @Autowired
    private DrugstoreIndustryAlertsService drugstoreIndustryAlertsService;


    /**
     * @Description: 药店预警数据
     */
    @RequestMapping("getYdSubject_fb")
    public ResultMsg getYdSubject_fb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        parameterProcess(parameterMap);
        PageInfo pageInfo = null;
        if ("1".equals(parameterMap.get("ffzhb"))){
            // 合并为总店显示
            pageInfo = drugstoreIndustryAlertsService.getYdSubjectByZd_fb(parameterMap);
        }else {
            pageInfo = drugstoreIndustryAlertsService.getYdSubject_fb(parameterMap);
        }
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }



    private static void parameterProcess(Map<String, Object> parameterMap) {
        Object fssqy = parameterMap.get("fssqy");
        if (fssqy !=null && StringUtils.isNotEmpty(fssqy.toString())) {
            parameterMap.put("fssqyList", Arrays.stream(fssqy.toString().split(","))
                    .map(String::trim) // 去除空格
                    .map(String::valueOf) // 转换为目标类型
                    .collect(Collectors.toList()));
        }

        Object fyear = parameterMap.get("fyear");
        if (fyear!=null && StringUtils.isNotEmpty(fyear.toString())) {
            parameterMap.put("flastyear",String.valueOf(Integer.parseInt(fyear.toString()) - 1));
        }
    }

}
