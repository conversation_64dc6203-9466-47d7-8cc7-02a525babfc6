package com.hnbp.local.yjfx.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.local.util.paging.PageParameter;
import com.hnbp.local.yjfx.mapper.HotelIndustryAlertsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class HotelIndustryAlertsService {

    @Autowired
    private HotelIndustryAlertsMapper hotelIndustryAlertsMapper;


    /**
     * @Description: 酒店预警
     * @Params:
     * @Return
     */
    public PageInfo getHotelData_fb(Map<String, Object> params) throws SQLException {
        String page = params.getOrDefault("page", "1").toString();
        String limit = params.getOrDefault("limit", "100").toString();
        PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(limit));
        return new PageInfo(hotelIndustryAlertsMapper.getHotelData_fb(params));
    }

    /**
     * @Description: 酒店均价
     */
    public PageInfo getHotelPriceData(Map<String, Object> params) throws SQLException {
        String page = params.getOrDefault("page", "1").toString();
        String limit = params.getOrDefault("limit", "100").toString();
        PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(limit));
        return new PageInfo(hotelIndustryAlertsMapper.getHotelPriceData(params));
    }

}
