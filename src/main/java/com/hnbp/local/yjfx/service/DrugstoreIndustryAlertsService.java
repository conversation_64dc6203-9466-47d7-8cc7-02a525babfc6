package com.hnbp.local.yjfx.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.local.yjfx.mapper.DrugstoreIndustryAlertsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class DrugstoreIndustryAlertsService {

    @Autowired
    private DrugstoreIndustryAlertsMapper drugstoreIndustryAlertsMapper;


    /**
     * @Description: 药店预警
     * @Params:
     * @Return
     */
    public PageInfo getYdSubject_fb(Map<String, Object> params) {
        String page = params.getOrDefault("page", "1").toString();
        String limit = params.getOrDefault("limit", "100").toString();
        PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(limit));
        return new PageInfo(drugstoreIndustryAlertsMapper.getYdSubject_fb(params));
    }

    /**
     * @Description: 药店预警 总店列表
     * @Params:
     * @Return
     */
    public PageInfo getYdSubjectByZd_fb(Map<String, Object> params) {
        String page = params.getOrDefault("page", "1").toString();
        String limit = params.getOrDefault("limit", "100").toString();
        PageHelper.startPage(Integer.parseInt(page), Integer.parseInt(limit));

        // 总店List
        List<Map<String, Object>> zdList = drugstoreIndustryAlertsMapper.getYdSubjectByZd_fb(params);
        if (zdList != null && !zdList.isEmpty()) {
            // 获取总店的fnsrmc // 注释查询全部,没有的归类至"其他"
            // List<String> fzdmcList = zdList.stream().map(map -> map.get("fnsrmc")).map(String::valueOf).collect(Collectors.toList());
            // params.put("fzdmcList", fzdmcList);

            params.put("queryzd","queryzd");
            // 分店List
            List<Map<String, Object>> fdList = drugstoreIndustryAlertsMapper.getYdSubject_fb(params);

            // 分店根据 总店(fyfzd)分组
            Map<Object, List<Map<String, Object>>> fdListGroup = fdList.stream().collect(Collectors.groupingBy(map -> map.get("fyfzd")));
            zdList.forEach(map -> {
                map.put("children", fdListGroup.get(map.get("fnsrmc")));
                // map.put("isParent", "true");
            });
        }

        return new PageInfo(zdList);
    }




}
